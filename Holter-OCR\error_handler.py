"""
Error Handling and Logging Module for Holter OCR System
Provides comprehensive error handling, logging, and progress tracking
"""

import os
import logging
import traceback
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from functools import wraps
import sys


class HolterErrorHandler:
    """Centralized error handling and logging for Holter OCR system"""
    
    def __init__(self, log_dir: str = "logs", log_level: str = "INFO"):
        """
        Initialize error handler
        
        Args:
            log_dir: Directory for log files
            log_level: Logging level
        """
        self.log_dir = log_dir
        self.log_level = log_level
        self.setup_logging()
        
        # Error tracking
        self.error_counts = {}
        self.error_details = []
        
        # Progress tracking
        self.progress_data = {
            'start_time': None,
            'current_file': None,
            'processed_count': 0,
            'total_count': 0,
            'errors': []
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        # Create log directory if it doesn't exist
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        # Create timestamp for log files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Configure logging
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # Main log file
        main_log_file = os.path.join(self.log_dir, f"holter_ocr_{timestamp}.log")
        
        # Error log file
        error_log_file = os.path.join(self.log_dir, f"holter_errors_{timestamp}.log")
        
        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            format=log_format,
            handlers=[
                logging.FileHandler(main_log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # Create error logger
        error_logger = logging.getLogger('error_logger')
        error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(logging.Formatter(log_format))
        error_logger.addHandler(error_handler)
        
        self.logger = logging.getLogger(__name__)
        self.error_logger = error_logger
        
        self.logger.info(f"Logging initialized - Main log: {main_log_file}")
        self.logger.info(f"Error log: {error_log_file}")
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None, 
                  file_path: str = None):
        """
        Log an error with context information
        
        Args:
            error: Exception object
            context: Additional context information
            file_path: File being processed when error occurred
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        # Track error counts
        if error_type not in self.error_counts:
            self.error_counts[error_type] = 0
        self.error_counts[error_type] += 1
        
        # Create error record
        error_record = {
            'timestamp': datetime.now().isoformat(),
            'error_type': error_type,
            'error_message': error_message,
            'file_path': file_path,
            'context': context or {},
            'traceback': traceback.format_exc()
        }
        
        self.error_details.append(error_record)
        
        # Log to both main and error loggers
        log_message = f"Error in {file_path or 'unknown file'}: {error_type} - {error_message}"
        
        if context:
            log_message += f" | Context: {context}"
        
        self.logger.error(log_message)
        self.error_logger.error(log_message)
        self.error_logger.error(f"Traceback:\n{traceback.format_exc()}")
    
    def handle_pdf_error(self, pdf_path: str, error: Exception, 
                        operation: str = "processing"):
        """
        Handle PDF-specific errors
        
        Args:
            pdf_path: Path to PDF file
            error: Exception that occurred
            operation: Operation being performed
        """
        context = {
            'operation': operation,
            'pdf_file': os.path.basename(pdf_path),
            'pdf_size': self._get_file_size(pdf_path)
        }
        
        self.log_error(error, context, pdf_path)
        
        # Add to progress tracking
        self.progress_data['errors'].append({
            'file': os.path.basename(pdf_path),
            'error': str(error),
            'operation': operation
        })
    
    def handle_ocr_error(self, image_path: str, error: Exception):
        """
        Handle OCR-specific errors
        
        Args:
            image_path: Path to image file
            error: Exception that occurred
        """
        context = {
            'operation': 'OCR processing',
            'image_file': os.path.basename(image_path) if image_path else 'unknown',
            'image_size': self._get_file_size(image_path) if image_path else 'unknown'
        }
        
        self.log_error(error, context, image_path)
    
    def handle_extraction_error(self, text: str, field_name: str, error: Exception):
        """
        Handle field extraction errors
        
        Args:
            text: Text being processed
            field_name: Field being extracted
            error: Exception that occurred
        """
        context = {
            'operation': 'field extraction',
            'field_name': field_name,
            'text_length': len(text) if text else 0,
            'text_preview': text[:100] if text else 'empty'
        }
        
        self.log_error(error, context)
    
    def _get_file_size(self, file_path: str) -> str:
        """Get file size in human readable format"""
        try:
            if file_path and os.path.exists(file_path):
                size_bytes = os.path.getsize(file_path)
                if size_bytes < 1024:
                    return f"{size_bytes} B"
                elif size_bytes < 1024 * 1024:
                    return f"{size_bytes / 1024:.1f} KB"
                else:
                    return f"{size_bytes / (1024 * 1024):.1f} MB"
        except Exception:
            pass
        return "unknown"
    
    def update_progress(self, current_file: str = None, processed_count: int = None,
                       total_count: int = None):
        """
        Update progress tracking
        
        Args:
            current_file: Currently processing file
            processed_count: Number of files processed
            total_count: Total number of files
        """
        if current_file:
            self.progress_data['current_file'] = current_file
        if processed_count is not None:
            self.progress_data['processed_count'] = processed_count
        if total_count is not None:
            self.progress_data['total_count'] = total_count
        
        if not self.progress_data['start_time']:
            self.progress_data['start_time'] = datetime.now()
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get current progress summary"""
        if self.progress_data['start_time']:
            elapsed = datetime.now() - self.progress_data['start_time']
            elapsed_seconds = elapsed.total_seconds()
        else:
            elapsed_seconds = 0
        
        return {
            'processed': self.progress_data['processed_count'],
            'total': self.progress_data['total_count'],
            'current_file': self.progress_data['current_file'],
            'elapsed_time': elapsed_seconds,
            'errors_count': len(self.progress_data['errors']),
            'error_types': dict(self.error_counts)
        }
    
    def save_error_report(self, output_file: str = None):
        """
        Save detailed error report to JSON file
        
        Args:
            output_file: Output file path
        """
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.log_dir, f"error_report_{timestamp}.json")
        
        report = {
            'summary': {
                'total_errors': len(self.error_details),
                'error_types': self.error_counts,
                'generated_at': datetime.now().isoformat()
            },
            'progress': self.get_progress_summary(),
            'errors': self.error_details
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Error report saved to: {output_file}")
            return output_file
        except Exception as e:
            self.logger.error(f"Failed to save error report: {e}")
            return None


def error_handler_decorator(error_handler: HolterErrorHandler, 
                          operation: str = "unknown"):
    """
    Decorator for automatic error handling
    
    Args:
        error_handler: HolterErrorHandler instance
        operation: Description of operation being performed
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Try to extract file path from arguments
                file_path = None
                for arg in args:
                    if isinstance(arg, str) and (arg.endswith('.pdf') or arg.endswith('.png')):
                        file_path = arg
                        break
                
                context = {
                    'function': func.__name__,
                    'operation': operation,
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                }
                
                error_handler.log_error(e, context, file_path)
                raise  # Re-raise the exception
        
        return wrapper
    return decorator


def setup_global_error_handler(log_dir: str = "logs", log_level: str = "INFO") -> HolterErrorHandler:
    """
    Setup global error handler for the application
    
    Args:
        log_dir: Directory for log files
        log_level: Logging level
        
    Returns:
        Configured HolterErrorHandler instance
    """
    error_handler = HolterErrorHandler(log_dir, log_level)
    
    # Setup global exception handler
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_handler.logger.critical(
            "Uncaught exception",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    sys.excepthook = handle_exception
    
    return error_handler


if __name__ == "__main__":
    # Test error handler
    error_handler = setup_global_error_handler()
    
    # Test error logging
    try:
        raise ValueError("Test error")
    except Exception as e:
        error_handler.log_error(e, {'test': True}, 'test_file.pdf')
    
    # Test progress tracking
    error_handler.update_progress("test.pdf", 1, 10)
    print(error_handler.get_progress_summary())
    
    # Save error report
    error_handler.save_error_report()
