import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from configparser import ConfigParser
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from echo_ocr_engine import EchoOCREngine
from excel_handler import ExcelHandler
from pdf_processor import MixedContentPDFProcessor

def setup_logging(config):
    """设置日志记录"""
    log_file_path = config.get('LOGGING', 'log_file_path')
    log_level = config.get('LOGGING', 'log_level')
    console_log_level = config.get('LOGGING', 'console_log_level', fallback='INFO')
    file_max_bytes = config.getint('LOGGING', 'file_max_bytes', fallback=10485760)
    backup_count = config.getint('LOGGING', 'backup_count', fallback=3)
    
    # 确保日志目录存在
    log_dir = os.path.dirname(log_file_path)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    
    # 清除任何现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 配置文件处理器
    try:
        file_handler = RotatingFileHandler(
            log_file_path,
            maxBytes=file_max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, log_level))
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        print(f"日志文件将被保存到: {os.path.abspath(log_file_path)}")
    except Exception as e:
        print(f"警告: 无法配置日志文件: {str(e)}")
    
    # 添加控制台处理器
    console = logging.StreamHandler()
    console.setLevel(getattr(logging, console_log_level))
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console.setFormatter(console_formatter)
    root_logger.addHandler(console)
    
    # 创建专用的PDFExtractor日志记录器
    logger = logging.getLogger('PDFExtractor')
    logger.info('日志系统初始化完成')
    
    return logger

def print_summary(records_count, extracted_fields, config):
    """打印处理摘要信息"""
    print("\n" + "="*60)
    print("OCR-PDF混合内容抽取整理 - 处理摘要")
    print("="*60)
    print(f"处理PDF文件数: {records_count}")
    print(f"提取字段数量: {len(extracted_fields)}")
    print("\n主要提取字段:")
    
    key_fields = [
        '姓名', '总检日期', '身高', '体重', 'BMI', 
        '血压_收缩压', '血压_舒张压', '心电图诊断小结',
        '冠状动脉CTA(增强)诊断小结', '超声心动图_超声所见', '超声心动图_超声提示'
    ]
    
    for field in key_fields:
        if field in extracted_fields:
            print(f"- {field}: {'成功' if extracted_fields[field] > 0 else '未找到'} ({extracted_fields[field]}条记录)")
    
    print("\n输出文件:")
    print(f"- Excel文件: {os.path.abspath(config.get('DEFAULT', 'output_excel_path'))}")
    print(f"- 临时图片目录: {os.path.abspath(config.get('DEFAULT', 'temp_images_path'))}")
    print(f"- 日志文件: {os.path.abspath(config.get('LOGGING', 'log_file_path'))}")
    print("="*60)

def main():
    # 读取配置文件
    config = ConfigParser()
    config_path = os.path.join(current_dir, 'config.ini')
    if not os.path.exists(config_path):
        print(f"错误：配置文件不存在: {config_path}")
        return 1
        
    config.read(config_path, encoding='utf-8')
    
    # 设置日志记录
    logger = setup_logging(config)
    logger.info('='*50)
    logger.info('OCR-PDF混合内容抽取整理程序启动')
    
    # 确保输出和临时目录存在
    os.makedirs(config.get('DEFAULT', 'temp_images_path'), exist_ok=True)
    os.makedirs(os.path.dirname(config.get('DEFAULT', 'output_excel_path')), exist_ok=True)
    
    try:
        # 初始化各模块
        logger.info('初始化心超OCR引擎...')
        echo_ocr_engine = EchoOCREngine(config)

        logger.info('初始化Excel处理器...')
        excel_handler = ExcelHandler(config)

        logger.info('初始化PDF处理器...')
        pdf_processor = MixedContentPDFProcessor(config, echo_ocr_engine, excel_handler)
        
        logger.info('模块初始化完成')
        
        # 处理PDF文件
        logger.info('开始处理PDF文件...')
        pdf_processor.process_pdfs()
        logger.info('PDF文件处理完成')
        
        # 保存Excel
        logger.info('开始保存Excel文件...')
        excel_handler.save_output()
        logger.info('Excel文件保存完成')
        
        # 统计提取字段信息
        extracted_fields = {}
        for record in excel_handler.data_records:
            for field, value in record.items():
                if field not in extracted_fields:
                    extracted_fields[field] = 0
                if value:
                    extracted_fields[field] += 1
        
        # 打印处理摘要
        print_summary(len(excel_handler.data_records), extracted_fields, config)
        
        logger.info('程序执行完成')
        
    except Exception as e:
        logger.error(f'程序执行出错: {str(e)}', exc_info=True)
        print(f"错误: {str(e)}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
