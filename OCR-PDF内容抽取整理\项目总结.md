# OCR-PDF内容抽取整理项目总结

## 项目概述

本项目实现了从医疗PDF文件中提取信息并整理到Excel表格的功能。项目主要包括以下几个模块：

1. **OCR引擎模块**：使用PaddleOCR进行文字识别
2. **PDF处理模块**：提取PDF中的图片并进行信息提取
3. **Excel处理模块**：将提取的信息整理到Excel表格中

## 实现思路

1. **PDF图片提取**：使用PyMuPDF库提取PDF中的图片，如果PDF中没有嵌入图片，则将整个页面渲染为图片
2. **图像预处理**：对提取的图片进行旋转检测、对比度增强等预处理，优化OCR识别效果
3. **OCR识别**：使用PaddleOCR对预处理后的图片进行文字识别
4. **跨行文本处理**：根据文本块位置信息处理跨多行的文本内容
5. **信息提取**：使用关键词匹配和正则表达式从OCR结果中提取结构化信息
6. **数据整理**：将提取的信息整理到Excel表格中，并根据姓名和住院号自动合并相同患者的记录
7. **图片嵌入**：支持将识别的原始图像嵌入到Excel表格中以便核验

## 关键技术点

1. **PDF处理**：使用PyMuPDF库提取PDF中的图片和文本
2. **图像预处理**：实现自动旋转检测，对比度增强，锐化等处理以提高OCR准确率
3. **OCR识别**：使用PaddleOCR进行中文文字识别，支持旋转文字识别
4. **跨行文本处理**：根据文本块的位置信息将多个文本块组合为完整内容
5. **信息提取**：使用正则表达式和关键词匹配从OCR结果中提取结构化信息
6. **数据合并**：根据患者姓名和住院号自动合并相同患者的信息，优先保留数值较大的记录
7. **Excel处理**：使用openpyxl库创建和格式化Excel表格，支持图片嵌入功能

## 项目结构

```
OCR-PDF内容抽取整理/
├── config.ini          # 配置文件
├── main.py             # 主程序
├── ocr_engine.py       # OCR引擎模块
├── pdf_processor.py    # PDF处理模块
├── excel_handler.py    # Excel处理模块
├── README.md           # 说明文档
└── 项目总结.md         # 项目总结
```

## 功能增强

相比于初始版本，项目进行了以下功能增强：

1. **图像预处理增强**：
   - 添加自动旋转检测功能，可处理翻转的页面
   - 增加图像对比度增强和锐化处理
   - 图像大小标准化以优化OCR识别效果

2. **文本提取增强**：
   - 添加跨行文本处理，可以正确提取跨多行的内容
   - 支持基于文本块位置信息的文本聚合
   - 优化心电图结论等特殊格式内容的提取

3. **字段匹配增强**：
   - 增加了超声所见、室间隔厚度、后壁厚度等新字段的提取
   - 优化数值提取逻辑，自动剔除单位
   - 添加最大值提取功能，对于有多个检测值的字段取数值较大的

4. **Excel输出增强**：
   - 支持将原始图片嵌入Excel单元格
   - 优化Excel格式，更美观易读
   - 添加处理摘要信息统计

## 运行效果

程序能够成功从PDF文件中提取图片，识别文字，并提取关键信息填充到Excel表格中。目前已实现的主要字段提取包括：

- 患者基本信息：姓名、住院号、性别、年龄
- 心超数据：心超结论、超声所见、室间隔厚度、舒张末期前后径、后壁厚度、EF值
- 心电图：心电图结论
- 检验结果：血红蛋白、糖化血红蛋白、血糖、甘油三酯、总胆固醇、低密度脂蛋白胆固醇、高密度脂蛋白胆固醇、载脂蛋白、pro-BNP、肌钙蛋白、肌红蛋白、肌酸激酶及同功酶等

## 改进方向

1. **OCR准确率优化**：进一步提高特殊字符和医学术语的识别准确率
2. **图像处理增强**：添加更多预处理方法，处理复杂背景和低质量图像
3. **匹配规则完善**：针对更多的特殊格式和医学术语优化匹配规则
4. **数据验证**：添加提取数据的验证机制，确保数据一致性和可靠性
5. **界面优化**：添加图形用户界面，方便用户操作和查看结果
6. **批量处理优化**：优化大量PDF文件的并行处理能力

## 总结

本项目成功实现了从PDF文件中提取医疗信息并整理到Excel表格的功能，特别优化了对跨行文本的处理和翻转页面的识别能力。通过模块化设计，使得代码结构清晰，易于维护和扩展。项目中使用了多种技术，包括PDF处理、图像处理、OCR识别、正则表达式匹配、Excel处理等，实现了一个完整的医疗数据提取和整理系统。 