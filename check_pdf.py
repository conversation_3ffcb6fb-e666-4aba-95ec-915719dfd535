import os
import numpy as np
import cv2
import fitz  # PyMuPDF
from PyPDF2 import PdfReader
from paddleocr import PaddleOCR

def check_pdf(pdf_path):
    print(f"检查PDF文件: {pdf_path}")
    
    # 使用PyPDF2读取PDF内容
    reader = PdfReader(pdf_path)
    num_pages = len(reader.pages)
    print(f"PDF页数: {num_pages}")
    
    # 输出文本内容摘要
    for i in range(min(num_pages, 2)):  # 只检查前两页
        page = reader.pages[i]
        text = page.extract_text()
        print(f"第{i+1}页文本预览 (前200字符): {text[:200] if text else '无文本内容'}")
    
    # 使用PyMuPDF提取图片
    print("使用PyMuPDF提取图片...")
    images_dir = "OCR-图片整理进文件夹/temp_images"
    os.makedirs(images_dir, exist_ok=True)
    
    doc = fitz.open(pdf_path)
    image_count = 0
    
    for page_index in range(len(doc)):
        page = doc[page_index]
        image_list = page.get_images(full=True)
        
        print(f"第{page_index+1}页包含 {len(image_list)} 张图片")
        
        if not image_list:
            # 如果没有嵌入图片，将整个页面渲染为图片
            pix = page.get_pixmap()
            img_path = os.path.join(images_dir, f"{os.path.basename(pdf_path)}_page{page_index+1}.png")
            pix.save(img_path)
            print(f"已保存页面图像到: {img_path}")
            image_count += 1
        else:
            # 提取嵌入图片
            for img_index, img in enumerate(image_list):
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                
                img_path = os.path.join(images_dir, f"{os.path.basename(pdf_path)}_p{page_index+1}_img{img_index+1}.png")
                with open(img_path, "wb") as f:
                    f.write(image_bytes)
                print(f"已保存嵌入图像到: {img_path}")
                image_count += 1
    
    print(f"总共提取了 {image_count} 张图片")
    
    # 初始化PaddleOCR
    print("初始化PaddleOCR...")
    ocr = PaddleOCR(use_angle_cls=True, lang='ch')
    
    # 对提取的图片进行OCR识别
    if image_count > 0:
        print("对提取的图片进行OCR识别...")
        sample_img_path = os.path.join(images_dir, os.listdir(images_dir)[0])
        result = ocr.ocr(sample_img_path, cls=True)
        
        print(f"OCR识别结果示例 (第一张图片):")
        if result and result[0]:
            for line in result[0]:
                text, confidence = line[1]
                print(f"文本: {text}, 置信度: {confidence}")
        else:
            print("未识别到文本")

if __name__ == "__main__":
    pdf_dir = "./files"
    for file in os.listdir(pdf_dir):
        if file.lower().endswith('.pdf'):
            check_pdf(os.path.join(pdf_dir, file)) 