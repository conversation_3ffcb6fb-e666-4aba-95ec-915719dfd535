"""
Batch Processing System for Holter PDF Reports
Processes all PDF files in the Holter directory systematically
"""

import os
import logging
import time
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd
from tqdm import tqdm

from ocr_extractor import HolterOCRExtractor
from field_extractor import HolterFieldExtractor


class HolterBatchProcessor:
    """Batch processor for Holter PDF reports"""
    
    def __init__(self, pdf_directory: str, output_file: str = None, max_workers: int = 4):
        """
        Initialize the batch processor
        
        Args:
            pdf_directory: Directory containing PDF files
            output_file: Output Excel file path
            max_workers: Maximum number of worker threads
        """
        self.pdf_directory = pdf_directory
        self.output_file = output_file or "holter_extracted_data.xlsx"
        self.max_workers = max_workers
        
        self.logger = logging.getLogger(__name__)
        self.ocr_extractor = HolterOCRExtractor()
        self.field_extractor = HolterFieldExtractor()
        
        # Statistics
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'start_time': None,
            'end_time': None
        }
        
        self.failed_files = []
        self.processed_data = []
    
    def get_pdf_files(self) -> List[str]:
        """
        Get list of PDF files in the directory
        
        Returns:
            List of PDF file paths
        """
        if not os.path.exists(self.pdf_directory):
            raise FileNotFoundError(f"Directory not found: {self.pdf_directory}")
        
        pdf_files = []
        for filename in os.listdir(self.pdf_directory):
            if filename.lower().endswith('.pdf'):
                pdf_path = os.path.join(self.pdf_directory, filename)
                pdf_files.append(pdf_path)
        
        self.logger.info(f"Found {len(pdf_files)} PDF files in {self.pdf_directory}")
        return pdf_files
    
    def process_single_pdf(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """
        Process a single PDF file
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            Extracted data dictionary or None if failed
        """
        try:
            filename = os.path.basename(pdf_path)
            self.logger.info(f"Processing: {filename}")
            
            # Extract text using OCR
            ocr_text = self.ocr_extractor.get_full_text(pdf_path, page_num=0)
            
            if not ocr_text.strip():
                self.logger.warning(f"No text extracted from {filename}")
                return None
            
            # Extract fields
            extracted_data = self.field_extractor.extract_all_fields(ocr_text, filename)
            cleaned_data = self.field_extractor.clean_and_validate_data(extracted_data)
            
            # Add metadata
            cleaned_data['_filename'] = filename
            cleaned_data['_processing_time'] = time.time()
            
            self.logger.info(f"Successfully processed: {filename}")
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"Error processing {pdf_path}: {e}")
            return None
    
    def process_all_pdfs(self, use_threading: bool = True) -> List[Dict[str, Any]]:
        """
        Process all PDF files in the directory
        
        Args:
            use_threading: Whether to use multi-threading
            
        Returns:
            List of extracted data dictionaries
        """
        pdf_files = self.get_pdf_files()
        self.stats['total_files'] = len(pdf_files)
        self.stats['start_time'] = time.time()
        
        if not pdf_files:
            self.logger.warning("No PDF files found to process")
            return []
        
        results = []
        
        if use_threading and self.max_workers > 1:
            # Multi-threaded processing
            self.logger.info(f"Starting multi-threaded processing with {self.max_workers} workers")
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all tasks
                future_to_pdf = {
                    executor.submit(self.process_single_pdf, pdf_path): pdf_path 
                    for pdf_path in pdf_files
                }
                
                # Process completed tasks with progress bar
                with tqdm(total=len(pdf_files), desc="Processing PDFs") as pbar:
                    for future in as_completed(future_to_pdf):
                        pdf_path = future_to_pdf[future]
                        try:
                            result = future.result()
                            if result:
                                results.append(result)
                                self.stats['processed_files'] += 1
                            else:
                                self.failed_files.append(pdf_path)
                                self.stats['failed_files'] += 1
                        except Exception as e:
                            self.logger.error(f"Error processing {pdf_path}: {e}")
                            self.failed_files.append(pdf_path)
                            self.stats['failed_files'] += 1
                        
                        pbar.update(1)
        else:
            # Single-threaded processing
            self.logger.info("Starting single-threaded processing")
            
            with tqdm(pdf_files, desc="Processing PDFs") as pbar:
                for pdf_path in pbar:
                    result = self.process_single_pdf(pdf_path)
                    if result:
                        results.append(result)
                        self.stats['processed_files'] += 1
                    else:
                        self.failed_files.append(pdf_path)
                        self.stats['failed_files'] += 1
                    
                    pbar.set_postfix({
                        'Processed': self.stats['processed_files'],
                        'Failed': self.stats['failed_files']
                    })
        
        self.stats['end_time'] = time.time()
        self.processed_data = results
        
        return results
    
    def save_to_excel(self, data: List[Dict[str, Any]], output_file: str = None) -> str:
        """
        Save extracted data to Excel file
        
        Args:
            data: List of extracted data dictionaries
            output_file: Output file path
            
        Returns:
            Path to saved file
        """
        if not data:
            self.logger.warning("No data to save")
            return None
        
        output_path = output_file or self.output_file
        
        try:
            # Get column headers from field extractor
            columns = self.field_extractor.get_excel_columns()
            
            # Create DataFrame with proper column order
            df_data = []
            for item in data:
                row = {}
                for col in columns:
                    row[col] = item.get(col, None)
                df_data.append(row)
            
            df = pd.DataFrame(df_data)
            
            # Save to Excel
            df.to_excel(output_path, index=False, engine='openpyxl')
            
            self.logger.info(f"Data saved to: {output_path}")
            self.logger.info(f"Saved {len(df)} records with {len(df.columns)} columns")
            
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error saving to Excel: {e}")
            return None
    
    def print_statistics(self):
        """Print processing statistics"""
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            
            print("\n" + "="*50)
            print("PROCESSING STATISTICS")
            print("="*50)
            print(f"Total files found: {self.stats['total_files']}")
            print(f"Successfully processed: {self.stats['processed_files']}")
            print(f"Failed to process: {self.stats['failed_files']}")
            print(f"Success rate: {self.stats['processed_files']/self.stats['total_files']*100:.1f}%")
            print(f"Processing time: {duration:.2f} seconds")
            print(f"Average time per file: {duration/self.stats['total_files']:.2f} seconds")
            
            if self.failed_files:
                print(f"\nFailed files:")
                for failed_file in self.failed_files:
                    print(f"  - {os.path.basename(failed_file)}")
            
            print("="*50)
    
    def run_batch_processing(self, use_threading: bool = True, save_excel: bool = True) -> str:
        """
        Run the complete batch processing pipeline
        
        Args:
            use_threading: Whether to use multi-threading
            save_excel: Whether to save results to Excel
            
        Returns:
            Path to output file if saved, None otherwise
        """
        self.logger.info("Starting batch processing pipeline")
        
        # Process all PDFs
        results = self.process_all_pdfs(use_threading=use_threading)
        
        # Print statistics
        self.print_statistics()
        
        # Save to Excel if requested
        output_path = None
        if save_excel and results:
            output_path = self.save_to_excel(results)
        
        self.logger.info("Batch processing completed")
        return output_path


if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('holter_batch_processing.log'),
            logging.StreamHandler()
        ]
    )
    
    # Run batch processing
    processor = HolterBatchProcessor(
        pdf_directory="Holter",
        output_file="holter_extracted_data.xlsx",
        max_workers=4
    )
    
    output_file = processor.run_batch_processing(
        use_threading=True,
        save_excel=True
    )
