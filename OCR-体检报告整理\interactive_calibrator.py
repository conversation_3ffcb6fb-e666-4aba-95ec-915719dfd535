#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式坐标标定工具
创建GUI界面让用户精确框选各类字段的目标区域
"""

import cv2
import numpy as np
import os
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import logging
from typing import Dict, List, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InteractiveCalibrator:
    """交互式坐标标定工具"""
    
    def __init__(self):
        """初始化标定工具"""
        self.root = tk.Tk()
        self.root.title("医疗表单字段标定工具")
        self.root.geometry("1400x900")
        
        # 数据存储
        self.current_image = None
        self.current_image_path = None
        self.field_definitions = self._load_field_definitions()
        self.calibrated_regions = {}
        self.current_field = None
        
        # 图像显示相关
        self.canvas_width = 800
        self.canvas_height = 600
        self.image_scale = 1.0
        self.image_offset = (0, 0)
        
        # 标定状态
        self.is_selecting = False
        self.selection_start = None
        self.selection_end = None
        self.current_rect_id = None
        
        self._setup_ui()
        
    def _load_field_definitions(self) -> Dict:
        """加载字段定义"""
        return {
            "基本信息": {
                "姓名": {"type": "text_input", "color": "red"},
                "手机号码": {"type": "text_input", "color": "red"},
                "出生日期1": {"type": "digit_box", "color": "red"},
                "出生日期2": {"type": "digit_box", "color": "red"},
                "出生日期3": {"type": "digit_box", "color": "red"},
                "出生日期4": {"type": "digit_box", "color": "red"},
                "出生日期5": {"type": "digit_box", "color": "red"},
                "出生日期6": {"type": "digit_box", "color": "red"},
                "出生日期7": {"type": "digit_box", "color": "red"},
                "出生日期8": {"type": "digit_box", "color": "red"},
                "性别男": {"type": "checkbox", "color": "blue"},
                "性别女": {"type": "checkbox", "color": "blue"},
                "血压收缩压": {"type": "digit_box", "color": "green"},
                "血压舒张压": {"type": "digit_box", "color": "green"},
                "心率": {"type": "text_input", "color": "green"},
                "体重1": {"type": "digit_box", "color": "green"},
                "体重2": {"type": "digit_box", "color": "green"},
                "体重3": {"type": "digit_box", "color": "green"},
                "体重4": {"type": "digit_box", "color": "green"},
                "身高1": {"type": "digit_box", "color": "green"},
                "身高2": {"type": "digit_box", "color": "green"},
                "身高3": {"type": "digit_box", "color": "green"},
                "临床症状无": {"type": "checkbox", "color": "blue"},
                "临床症状有": {"type": "checkbox", "color": "blue"},
                "临床症状具体": {"type": "text_input", "color": "blue"}
            },
            "危险因素及既往病史": {
                "吸烟否": {"type": "checkbox", "color": "orange"},
                "吸烟已戒烟": {"type": "checkbox", "color": "orange"},
                "吸烟当前吸烟": {"type": "checkbox", "color": "orange"},
                "饮酒否": {"type": "checkbox", "color": "orange"},
                "饮酒已戒酒": {"type": "checkbox", "color": "orange"},
                "饮酒当前饮酒": {"type": "checkbox", "color": "orange"},
                "心肌梗死病史否": {"type": "checkbox", "color": "orange"},
                "心肌梗死病史是": {"type": "checkbox", "color": "orange"},
                "卒中史否": {"type": "checkbox", "color": "orange"},
                "卒中史是": {"type": "checkbox", "color": "orange"},
                "卒中史脑梗死": {"type": "checkbox", "color": "orange"},
                "卒中史脑出血": {"type": "checkbox", "color": "orange"},
                "卒中史不详": {"type": "checkbox", "color": "orange"},
                "高血压病史否": {"type": "checkbox", "color": "orange"},
                "高血压病史是": {"type": "checkbox", "color": "orange"},
                "血脂异常病史否": {"type": "checkbox", "color": "orange"},
                "血脂异常病史是": {"type": "checkbox", "color": "orange"},
                "糖尿病否": {"type": "checkbox", "color": "orange"},
                "糖尿病是": {"type": "checkbox", "color": "orange"},
                "外周血管病否": {"type": "checkbox", "color": "orange"},
                "外周血管病是": {"type": "checkbox", "color": "orange"}
            },
            "用药情况": {
                "抗血小板药物否": {"type": "checkbox", "color": "purple"},
                "抗血小板药物是": {"type": "checkbox", "color": "purple"},
                "阿司匹林": {"type": "checkbox", "color": "purple"},
                "氯吡格雷": {"type": "checkbox", "color": "purple"},
                "替格瑞洛": {"type": "checkbox", "color": "purple"},
                "他汀类否": {"type": "checkbox", "color": "purple"},
                "他汀类是": {"type": "checkbox", "color": "purple"},
                "阿托伐他汀": {"type": "checkbox", "color": "purple"},
                "瑞舒伐他汀": {"type": "checkbox", "color": "purple"},
                "他汀类其他": {"type": "checkbox", "color": "purple"},
                "他汀类其他药物名称": {"type": "text_input", "color": "purple"},
                "降压药物否": {"type": "checkbox", "color": "purple"},
                "降压药物是": {"type": "checkbox", "color": "purple"},
                "降糖药物否": {"type": "checkbox", "color": "purple"},
                "降糖药物是": {"type": "checkbox", "color": "purple"},
                "胰岛素否": {"type": "checkbox", "color": "purple"},
                "胰岛素是": {"type": "checkbox", "color": "purple"}
            }
        }
    
    def _setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame, width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 图像选择
        ttk.Label(control_frame, text="图像文件:", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 5))
        ttk.Button(control_frame, text="选择图像", command=self._select_image).pack(fill=tk.X, pady=(0, 10))
        
        # 字段分类选择
        ttk.Label(control_frame, text="字段分类:", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(10, 5))
        self.category_var = tk.StringVar(value="基本信息")
        category_combo = ttk.Combobox(control_frame, textvariable=self.category_var, 
                                     values=list(self.field_definitions.keys()), state="readonly")
        category_combo.pack(fill=tk.X, pady=(0, 5))
        category_combo.bind("<<ComboboxSelected>>", self._on_category_changed)
        
        # 字段列表
        ttk.Label(control_frame, text="字段列表:", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(10, 5))
        
        # 字段列表框架
        list_frame = ttk.Frame(control_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 字段列表
        self.field_listbox = tk.Listbox(list_frame, height=15)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.field_listbox.yview)
        self.field_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.field_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.field_listbox.bind("<<ListboxSelect>>", self._on_field_selected)
        
        # 操作按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="开始标定", command=self._start_calibration).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="删除标定", command=self._delete_calibration).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="保存配置", command=self._save_configuration).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="加载配置", command=self._load_configuration).pack(fill=tk.X, pady=(0, 5))
        
        # 状态显示
        self.status_var = tk.StringVar(value="请选择图像文件")
        status_label = ttk.Label(control_frame, textvariable=self.status_var, 
                                foreground="blue", font=("Arial", 10))
        status_label.pack(anchor=tk.W, pady=(10, 0))
        
        # 右侧图像显示区域
        image_frame = ttk.Frame(main_frame)
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 图像画布
        self.canvas = tk.Canvas(image_frame, bg="white", width=self.canvas_width, height=self.canvas_height)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self._on_mouse_press)
        self.canvas.bind("<B1-Motion>", self._on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_mouse_release)
        
        # 初始化字段列表
        self._update_field_list()
    
    def _select_image(self):
        """选择图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.png *.jpg *.jpeg *.bmp *.tiff"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self._load_image(file_path)
    
    def _load_image(self, image_path: str):
        """加载图像"""
        try:
            # 读取图像 - 处理中文路径问题
            try:
                self.current_image = cv2.imread(image_path)
            except:
                # 如果直接读取失败，尝试使用numpy处理中文路径
                import numpy as np
                self.current_image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)

            if self.current_image is None:
                messagebox.showerror("错误", f"无法读取图像: {image_path}")
                return
            
            self.current_image_path = image_path
            
            # 转换为RGB格式
            image_rgb = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
            
            # 计算缩放比例
            img_height, img_width = image_rgb.shape[:2]
            canvas_width = self.canvas.winfo_width() or self.canvas_width
            canvas_height = self.canvas.winfo_height() or self.canvas_height
            
            scale_x = canvas_width / img_width
            scale_y = canvas_height / img_height
            self.image_scale = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
            
            # 调整图像大小
            new_width = int(img_width * self.image_scale)
            new_height = int(img_height * self.image_scale)
            
            pil_image = Image.fromarray(image_rgb)
            # 兼容旧版本PIL
            try:
                pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            except AttributeError:
                pil_image = pil_image.resize((new_width, new_height), Image.LANCZOS)
            
            # 转换为Tkinter格式
            self.photo = ImageTk.PhotoImage(pil_image)
            
            # 清空画布并显示图像
            self.canvas.delete("all")
            
            # 计算居中偏移
            self.image_offset = (
                (canvas_width - new_width) // 2,
                (canvas_height - new_height) // 2
            )
            
            self.canvas.create_image(
                self.image_offset[0], self.image_offset[1], 
                anchor=tk.NW, image=self.photo
            )
            
            # 重新绘制已标定的区域
            self._redraw_calibrated_regions()
            
            self.status_var.set(f"已加载图像: {os.path.basename(image_path)}")
            logger.info(f"图像加载成功: {image_path}")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图像失败: {str(e)}")
            logger.error(f"图像加载失败: {str(e)}")
    
    def _on_category_changed(self, event=None):
        """分类改变事件"""
        self._update_field_list()
    
    def _update_field_list(self):
        """更新字段列表"""
        category = self.category_var.get()
        if category in self.field_definitions:
            self.field_listbox.delete(0, tk.END)
            for field_name in self.field_definitions[category]:
                # 显示已标定的字段
                status = " ✓" if field_name in self.calibrated_regions else ""
                self.field_listbox.insert(tk.END, f"{field_name}{status}")
    
    def _on_field_selected(self, event=None):
        """字段选择事件"""
        selection = self.field_listbox.curselection()
        if selection:
            field_text = self.field_listbox.get(selection[0])
            self.current_field = field_text.replace(" ✓", "")
            self.status_var.set(f"已选择字段: {self.current_field}")
    
    def _start_calibration(self):
        """开始标定"""
        if not self.current_image_path:
            messagebox.showwarning("警告", "请先选择图像文件")
            return
        
        if not self.current_field:
            messagebox.showwarning("警告", "请先选择要标定的字段")
            return
        
        self.is_selecting = True
        self.status_var.set(f"请在图像上拖拽选择 '{self.current_field}' 的区域")
    
    def _on_mouse_press(self, event):
        """鼠标按下事件"""
        if not self.is_selecting:
            return
        
        # 转换为图像坐标
        canvas_x = event.x - self.image_offset[0]
        canvas_y = event.y - self.image_offset[1]
        
        if canvas_x >= 0 and canvas_y >= 0:
            self.selection_start = (canvas_x, canvas_y)
            self.selection_end = (canvas_x, canvas_y)
    
    def _on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if not self.is_selecting or not self.selection_start:
            return
        
        # 转换为图像坐标
        canvas_x = event.x - self.image_offset[0]
        canvas_y = event.y - self.image_offset[1]
        
        self.selection_end = (canvas_x, canvas_y)
        
        # 删除之前的选择框
        if self.current_rect_id:
            self.canvas.delete(self.current_rect_id)
        
        # 绘制新的选择框
        x1, y1 = self.selection_start
        x2, y2 = self.selection_end
        
        self.current_rect_id = self.canvas.create_rectangle(
            x1 + self.image_offset[0], y1 + self.image_offset[1],
            x2 + self.image_offset[0], y2 + self.image_offset[1],
            outline="red", width=2, dash=(5, 5)
        )
    
    def _on_mouse_release(self, event):
        """鼠标释放事件"""
        if not self.is_selecting or not self.selection_start:
            return
        
        # 计算选择区域
        x1, y1 = self.selection_start
        x2, y2 = self.selection_end
        
        # 确保坐标顺序正确
        min_x, max_x = min(x1, x2), max(x1, x2)
        min_y, max_y = min(y1, y2), max(y1, y2)
        
        # 转换为原始图像坐标
        orig_x1 = int(min_x / self.image_scale)
        orig_y1 = int(min_y / self.image_scale)
        orig_x2 = int(max_x / self.image_scale)
        orig_y2 = int(max_y / self.image_scale)
        
        # 保存标定结果
        if abs(max_x - min_x) > 10 and abs(max_y - min_y) > 10:  # 最小区域限制
            category = self.category_var.get()
            field_info = self.field_definitions[category][self.current_field]
            
            self.calibrated_regions[self.current_field] = {
                "category": category,
                "type": field_info["type"],
                "color": field_info["color"],
                "region": [orig_x1, orig_y1, orig_x2 - orig_x1, orig_y2 - orig_y1],
                "canvas_region": [min_x, min_y, max_x - min_x, max_y - min_y]
            }
            
            self.status_var.set(f"已标定字段: {self.current_field}")
            self._update_field_list()
            self._redraw_calibrated_regions()
        
        # 重置选择状态
        self.is_selecting = False
        self.selection_start = None
        self.selection_end = None
        if self.current_rect_id:
            self.canvas.delete(self.current_rect_id)
            self.current_rect_id = None
    
    def _redraw_calibrated_regions(self):
        """重新绘制已标定的区域"""
        # 删除所有标定区域
        self.canvas.delete("calibrated")
        
        for field_name, region_info in self.calibrated_regions.items():
            if "canvas_region" in region_info:
                x, y, w, h = region_info["canvas_region"]
                color = region_info["color"]
                
                # 绘制区域框
                self.canvas.create_rectangle(
                    x + self.image_offset[0], y + self.image_offset[1],
                    x + w + self.image_offset[0], y + h + self.image_offset[1],
                    outline=color, width=2, tags="calibrated"
                )
                
                # 绘制标签
                self.canvas.create_text(
                    x + self.image_offset[0] + 5, y + self.image_offset[1] - 10,
                    text=field_name, fill=color, anchor=tk.W, 
                    font=("Arial", 8, "bold"), tags="calibrated"
                )
    
    def _delete_calibration(self):
        """删除标定"""
        if not self.current_field:
            messagebox.showwarning("警告", "请先选择要删除的字段")
            return
        
        if self.current_field in self.calibrated_regions:
            del self.calibrated_regions[self.current_field]
            self.status_var.set(f"已删除字段标定: {self.current_field}")
            self._update_field_list()
            self._redraw_calibrated_regions()
        else:
            messagebox.showinfo("信息", f"字段 '{self.current_field}' 尚未标定")
    
    def _save_configuration(self):
        """保存配置"""
        if not self.calibrated_regions:
            messagebox.showwarning("警告", "没有标定数据可保存")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存标定配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                config_data = {
                    "image_path": self.current_image_path,
                    "calibrated_regions": {}
                }
                
                # 只保存原始图像坐标，不保存画布坐标
                for field_name, region_info in self.calibrated_regions.items():
                    config_data["calibrated_regions"][field_name] = {
                        "category": region_info["category"],
                        "type": region_info["type"],
                        "color": region_info["color"],
                        "region": region_info["region"]
                    }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("成功", f"配置已保存到: {file_path}")
                logger.info(f"配置保存成功: {file_path}")
                
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")
                logger.error(f"配置保存失败: {str(e)}")
    
    def _load_configuration(self):
        """加载配置"""
        file_path = filedialog.askopenfilename(
            title="加载标定配置",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                if "calibrated_regions" in config_data:
                    self.calibrated_regions = {}
                    
                    for field_name, region_info in config_data["calibrated_regions"].items():
                        # 重新计算画布坐标
                        orig_region = region_info["region"]
                        canvas_region = [
                            int(orig_region[0] * self.image_scale),
                            int(orig_region[1] * self.image_scale),
                            int(orig_region[2] * self.image_scale),
                            int(orig_region[3] * self.image_scale)
                        ]
                        
                        self.calibrated_regions[field_name] = {
                            "category": region_info["category"],
                            "type": region_info["type"],
                            "color": region_info["color"],
                            "region": orig_region,
                            "canvas_region": canvas_region
                        }
                    
                    self._update_field_list()
                    self._redraw_calibrated_regions()
                    
                    messagebox.showinfo("成功", f"配置已加载: {len(self.calibrated_regions)} 个字段")
                    logger.info(f"配置加载成功: {file_path}")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")
                logger.error(f"配置加载失败: {str(e)}")
    
    def run(self):
        """运行应用"""
        self.root.mainloop()


def main():
    """主函数"""
    app = InteractiveCalibrator()
    app.run()


if __name__ == "__main__":
    main()
