import os
import sys
from configparser import ConfigParser

from image_processor import ImageProcessor
from excel_handler import ExcelHandler
from ocr_engine import OCREngine


def main():
    # 读取配置文件
    config = ConfigParser()
    config.read('config/config.ini', encoding='utf-8')
    
    # 初始化各模块
    ocr_engine = OCREngine(config)
    excel_handler = ExcelHandler(config)
    image_processor = ImageProcessor(config, ocr_engine, excel_handler)
    
    # 处理图片
    image_processor.process_images()
    
    # 保存Excel
    excel_handler.save_output()


if __name__ == '__main__':
    main()

    img_path ="D://data//临床信息图片//1//IMG_20250211_111207.jpg"
    img_path = ".//1.jpg"
    img = cv2.imread(img_path)
