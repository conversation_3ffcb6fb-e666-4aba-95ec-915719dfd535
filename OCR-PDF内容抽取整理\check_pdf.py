import os
import sys
import logging
import argparse
from configparser import ConfigParser

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from ocr_engine import OCREngine
from excel_handler import ExcelHandler
from pdf_processor import PDFProcessor

def setup_basic_logging():
    """配置基本日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('PDFCheck')

def check_pdf(pdf_path):
    """检查单个PDF文件的OCR效果"""
    logger = setup_basic_logging()
    logger.info(f'开始检查PDF: {pdf_path}')
    
    # 加载配置
    config = ConfigParser()
    config_path = os.path.join(current_dir, 'config.ini')
    if not os.path.exists(config_path):
        logger.error(f"配置文件不存在: {config_path}")
        return 1
    
    config.read(config_path, encoding='utf-8')
    
    # 临时修改配置，只处理这一个文件
    if not os.path.exists(pdf_path):
        logger.error(f"PDF文件不存在: {pdf_path}")
        return 1
    
    # 创建临时目录
    temp_dir = os.path.join(current_dir, 'temp_check')
    os.makedirs(temp_dir, exist_ok=True)
    
    # 配置临时路径
    config['DEFAULT']['temp_images_path'] = temp_dir
    output_excel = os.path.join(temp_dir, 'check_result.xlsx')
    config['DEFAULT']['output_excel_path'] = output_excel
    
    try:
        # 初始化OCR引擎
        ocr_engine = OCREngine(config)
        excel_handler = ExcelHandler(config)
        
        # 提取图片
        pdf_processor = PDFProcessor(config, ocr_engine, excel_handler)
        logger.info('正在提取PDF图片...')
        image_paths = pdf_processor._extract_images_from_pdf(pdf_path)
        
        if not image_paths:
            logger.error('未能从PDF中提取图片')
            return 1
            
        logger.info(f'提取了 {len(image_paths)} 张图片')
        
        # OCR识别
        logger.info('开始OCR识别...')
        ocr_results = ocr_engine.batch_recognize(image_paths)
        
        # 显示OCR结果
        for img_path, blocks in ocr_results.items():
            logger.info(f'图片 {os.path.basename(img_path)} 识别到 {len(blocks)} 个文本块')
            for i, block in enumerate(blocks[:10]):  # 只显示前10个文本块
                logger.info(f'  文本 {i+1}: {block["text"]} (置信度: {block["confidence"]:.2f})')
            
            if len(blocks) > 10:
                logger.info(f'  ... 还有 {len(blocks) - 10} 个文本块')
        
        # 合并文本块
        logger.info('合并文本块为行...')
        all_blocks = []
        for img_path, blocks in ocr_results.items():
            for block in blocks:
                block['image_path'] = img_path
                all_blocks.append(block)
                
        merged_lines = pdf_processor._merge_text_blocks_by_line(all_blocks)
        
        # 显示合并行
        for img_path, lines in merged_lines.items():
            logger.info(f'图片 {os.path.basename(img_path)} 合并为 {len(lines)} 行')
            for i, line in enumerate(lines[:5]):  # 只显示前5行
                logger.info(f'  行 {i+1}: {line["text"][:100]}{"..." if len(line["text"]) > 100 else ""}')
            
            if len(lines) > 5:
                logger.info(f'  ... 还有 {len(lines) - 5} 行')
        
        # 提取信息
        logger.info('提取医疗信息...')
        grouped_blocks = pdf_processor._group_text_blocks_by_position(all_blocks)
        extracted_data = pdf_processor._extract_info(all_blocks, grouped_blocks, merged_lines)
        
        # 显示提取结果
        logger.info('提取结果:')
        for field, value in extracted_data.items():
            if value and field not in ['图片路径', 'PDF文件']:
                if len(str(value)) > 100:
                    logger.info(f'  {field}: {str(value)[:100]}...')
                else:
                    logger.info(f'  {field}: {value}')
        
        logger.info(f'检查完成，临时文件保存在: {temp_dir}')
        return 0
        
    except Exception as e:
        logger.error(f'检查过程出错: {str(e)}', exc_info=True)
        return 1

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='检查PDF文件的OCR识别效果')
    parser.add_argument('pdf_path', help='要检查的PDF文件路径')
    args = parser.parse_args()
    
    sys.exit(check_pdf(args.pdf_path)) 