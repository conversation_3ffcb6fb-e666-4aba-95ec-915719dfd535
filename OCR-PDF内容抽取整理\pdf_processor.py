import os
import re
import logging
import fitz  # PyMuPDF
from typing import List, Dict, Optional, Tuple, Any
from configparser import ConfigParser
from ocr_engine import OCREngine
from excel_handler import ExcelHandler
from PIL import Image, ImageOps
import io
import numpy as np
import cv2
import difflib

logger = logging.getLogger('PDFExtractor')

class PDFProcessor:
    def __init__(self, config: ConfigParser, ocr_engine: OCREngine, excel_handler: ExcelHandler):
        self.config = config
        self.ocr_engine = ocr_engine
        self.excel_handler = excel_handler
        self.input_path = config.get('DEFAULT', 'input_pdf_path')
        self.temp_images_path = config.get('DEFAULT', 'temp_images_path')
        
        # 文本处理参数
        self.line_merge_threshold = config.getint('TEXT_PROCESSING', 'line_merge_threshold', fallback=30)
        self.field_search_distance = config.getint('TEXT_PROCESSING', 'field_search_distance', fallback=100)
        
        # OCR结果合并策略
        self.force_pipeline_override = config.getboolean('OCR', 'force_pipeline_override', fallback=False)
        
        # 创建临时图片目录
        os.makedirs(self.temp_images_path, exist_ok=True)
        
    def process_pdfs(self):
        """处理所有PDF文件"""
        # 获取所有PDF文件
        pdf_files = self._get_pdf_files()
        logger.info(f'找到 {len(pdf_files)} 个待处理PDF文件')
        
        # 逐个处理PDF文件
        for pdf_path in pdf_files:
            logger.info(f'开始处理PDF文件: {os.path.basename(pdf_path)}')
            
            # 提取PDF中的图片
            image_paths = self._extract_images_from_pdf(pdf_path)
            
            if not image_paths:
                logger.warning(f'未能从PDF中提取图片: {pdf_path}')
                continue
            
            # 对提取的图片进行OCR识别
            ocr_results = self.ocr_engine.batch_recognize(image_paths)
            
            # 合并同一PDF的所有OCR结果，并保留图片路径信息
            all_text_blocks = []
            for img_path, text_blocks in ocr_results.items():
                for block in text_blocks:
                    # 添加图片路径信息到文本块
                    block['image_path'] = img_path
                all_text_blocks.extend(text_blocks)
            
            # 根据文本位置对文本块进行排序和分组，以便更好地处理跨行文本
            grouped_blocks = self._group_text_blocks_by_position(all_text_blocks)
            
            # 按行合并文本块，改进跨行文本提取
            merged_lines = self._merge_text_blocks_by_line(all_text_blocks)
            
            # 从OCR结果中提取信息
            extracted_data = self._extract_info(all_text_blocks, grouped_blocks, merged_lines)
            
            # 添加PDF路径信息
            extracted_data['PDF文件'] = os.path.basename(pdf_path)
            
            # 添加图片路径信息
            extracted_data['图片路径'] = ','.join(image_paths)
            
            # 将提取的信息添加到Excel中
            if extracted_data:
                logger.info(f'从PDF中提取到数据: {extracted_data}')
                self.excel_handler.add_record(extracted_data)
            else:
                logger.warning(f'未能从PDF中提取有效数据: {pdf_path}')
        
        logger.info('所有PDF处理完成')
    
    def _get_pdf_files(self) -> List[str]:
        """获取所有待处理的PDF文件"""
        if not os.path.isdir(self.input_path):
            raise ValueError(f'输入路径不存在: {self.input_path}')
        
        pdf_files = []
        for file in os.listdir(self.input_path):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(self.input_path, file))
        
        # 按文件名排序
        pdf_files.sort(key=lambda x: os.path.basename(x))
        return pdf_files
    
    def _extract_images_from_pdf(self, pdf_path: str) -> List[str]:
        """从PDF中提取图片，确保图像不旋转"""
        image_paths = []
        
        try:
            doc = fitz.open(pdf_path)
            pdf_name = os.path.basename(pdf_path)
            
            # 检查PDF是否有布局问题
            for page_index in range(len(doc)):
                page = doc[page_index]
                
                # 记录页面的原始旋转属性
                original_rotation = page.rotation
                logger.debug(f'第{page_index+1}页原始旋转属性: {original_rotation}度')
                
                # 如果页面有旋转，临时将其设为0以正确提取
                if original_rotation != 0:
                    page.set_rotation(0)
                
                image_list = page.get_images(full=True)
                logger.debug(f'第{page_index+1}页包含 {len(image_list)} 张图片')
                
                if not image_list:
                    # 如果没有嵌入图片，将整个页面渲染为图片
                    # 使用强制参数确保不旋转
                    try:
                        # 创建一个Identity矩阵，确保不进行任何变换
                        identity_matrix = fitz.Matrix(1, 0, 0, 1, 0, 0)
                        
                        # 设置dpi以获得足够的清晰度，但不设置任何旋转
                        pix = page.get_pixmap(dpi=300, alpha=False, matrix=identity_matrix)
                        
                        # 获取图像尺寸信息用于日志
                        width, height = pix.width, pix.height
                        logger.debug(f'渲染页面图像尺寸: {width}x{height}')
                        
                        img_path = os.path.join(self.temp_images_path, f"{pdf_name}_page{page_index+1}.png")
                        pix.save(img_path)
                        
                        # 检查生成的图像尺寸是否符合预期
                        check_img = cv2.imread(img_path)
                        if check_img is not None:
                            h, w = check_img.shape[:2]
                            logger.debug(f'保存的图像尺寸: {w}x{h}, 是否与预期一致: {w==width and h==height}')
                        
                        logger.debug(f'已保存页面图像到: {img_path}')
                        image_paths.append(img_path)
                    except Exception as render_e:
                        logger.error(f'渲染页面图像失败: {str(render_e)}')
                else:
                    # 提取嵌入图片
                    for img_index, img in enumerate(image_list):
                        xref = img[0]
                        try:
                            base_image = doc.extract_image(xref)
                            image_bytes = base_image["image"]
                            
                            # 使用CV2直接解码图像数据，避免任何自动旋转
                            img_path = os.path.join(self.temp_images_path, f"{pdf_name}_p{page_index+1}_img{img_index+1}.png")
                            
                            # 方法1: 使用numpy和cv2直接解码和保存，避免PIL的自动旋转
                            nparr = np.frombuffer(image_bytes, np.uint8)
                            img_np = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                            
                            # 记录原始尺寸
                            if img_np is not None:
                                h, w = img_np.shape[:2]
                                logger.debug(f'提取的嵌入图像原始尺寸: {w}x{h}')
                                
                                # 使用cv2保存，确保不进行任何变换
                                cv2.imwrite(img_path, img_np)
                                
                                # 检查保存后的尺寸
                                saved_img = cv2.imread(img_path)
                                if saved_img is not None:
                                    saved_h, saved_w = saved_img.shape[:2]
                                    logger.debug(f'保存后的图像尺寸: {saved_w}x{saved_h}, 是否保持一致: {saved_w==w and saved_h==h}')
                            else:
                                logger.warning(f'无法解码嵌入图像数据: xref={xref}')
                                continue
                            
                            logger.debug(f'已保存嵌入图像到: {img_path}')
                            image_paths.append(img_path)
                        except Exception as e:
                            logger.warning(f'提取图像失败(xref={xref}): {str(e)}')
                
                # 恢复原始旋转属性
                if original_rotation != 0:
                    page.set_rotation(original_rotation)
            
            logger.info(f'从PDF中提取了 {len(image_paths)} 张图片')
            return image_paths
            
        except Exception as e:
            logger.error(f'提取PDF图片失败: {str(e)}')
            return []
    
    def _group_text_blocks_by_position(self, text_blocks: List[Dict]) -> Dict[str, List[Dict]]:
        """根据文本块的位置将其分组，用于处理跨行文本
        
        返回格式：{
            "image_path": [
                {文本块1}, {文本块2}, ...
            ],
            ...
        }
        """
        grouped = {}
        
        # 按图片路径分组
        for block in text_blocks:
            img_path = block.get('image_path', '')
            if img_path not in grouped:
                grouped[img_path] = []
            grouped[img_path].append(block)
        
        # 在每个图片内按照y坐标排序，大致按照从上到下的阅读顺序
        for img_path, blocks in grouped.items():
            # 计算每个块的中心点y坐标
            for block in blocks:
                positions = block.get('position', [])
                if positions and len(positions) >= 4:  # 确保有足够的点来计算
                    y_coords = [pos[1] for pos in positions]
                    block['center_y'] = sum(y_coords) / len(y_coords)
            
            # 按y坐标排序
            grouped[img_path] = sorted(blocks, key=lambda b: b.get('center_y', 0))
        
        return grouped
    
    def _merge_text_blocks_by_line(self, text_blocks: List[Dict]) -> Dict[str, List[Dict]]:
        """按行合并文本块，改进跨行文本处理（增强版，处理多栏布局）
        
        返回格式：{
            "image_path": [
                {"line_blocks": [文本块1, 文本块2, ...], "text": "合并后的文本"},
                ...
            ],
            ...
        }
        """
        # 按图片路径分组
        img_blocks = {}
        for block in text_blocks:
            img_path = block.get('image_path', '')
            if img_path not in img_blocks:
                img_blocks[img_path] = []
            img_blocks[img_path].append(block)
        
        merged_lines = {}
        for img_path, blocks in img_blocks.items():
            if not blocks:
                merged_lines[img_path] = []
                continue

            # 确保所有块都有中心坐标
            for block in blocks:
                if 'center_y' not in block or 'center_x' not in block:
                    positions = block.get('position', [])
                    if positions and len(positions) >= 4:
                        x_coords = [pos[0] for pos in positions]
                        y_coords = [pos[1] for pos in positions]
                        block['center_x'] = sum(x_coords) / len(x_coords)
                        block['center_y'] = sum(y_coords) / len(y_coords)

            # 动态地将块分组成行
            lines_of_blocks = []
            # 先按Y坐标对块进行排序，以便从上到下处理
            sorted_by_y = sorted(blocks, key=lambda b: b.get('center_y', 0))

            for block in sorted_by_y:
                if not block.get('text', '').strip():
                    continue  # 跳过空文本块
                
                found_line = False
                # 寻找一个可以容纳当前块的现有行
                for line in lines_of_blocks:
                    # 使用行的平均Y坐标作为参考
                    avg_y = sum(b.get('center_y', 0) for b in line) / len(line)
                    if abs(block.get('center_y', 0) - avg_y) < self.line_merge_threshold:
                        line.append(block)
                        found_line = True
                        break
                
                # 如果没有找到合适的行，则创建一个新行
                if not found_line:
                    lines_of_blocks.append([block])

            # 按行的平均Y坐标对所有行进行排序（从上到下）
            lines_of_blocks.sort(key=lambda line: sum(b.get('center_y', 0) for b in line) / len(line))
            
            # 对每行内部的块按X坐标排序，并生成最终的行对象
            final_lines = []
            for line_blocks in lines_of_blocks:
                line_blocks.sort(key=lambda b: b.get('center_x', 0))
                line_text = ' '.join([b['text'] for b in line_blocks])
                final_lines.append({
                    'line_blocks': line_blocks,
                    'text': line_text
                })
            
            merged_lines[img_path] = final_lines
            logger.debug(f'图片{os.path.basename(img_path)}合并文本块为{len(final_lines)}行')
        
        return merged_lines
    
    def _extract_info(self, text_blocks: List[Dict], grouped_blocks: Dict[str, List[Dict]], 
                      merged_lines: Dict[str, List[Dict]]) -> Dict:
        """从OCR结果中提取与表头匹配的信息"""
        extracted_data = {}
        
        # 提取患者基本信息
        self._extract_patient_info(text_blocks, extracted_data)
        
        # 重新按左到右、上到下的顺序合并文本块，优化超声所见等长文本提取
        optimized_merged_lines = self._merge_text_blocks_by_line(text_blocks)
        
        # 提取心超数据
        self._extract_echo_data(text_blocks, grouped_blocks, optimized_merged_lines, extracted_data)
        
        # 提取检验结果
        self._extract_lab_values(text_blocks, merged_lines, extracted_data)
        
        # 提取心电图结论
        self._extract_ecg_conclusion(text_blocks, grouped_blocks, optimized_merged_lines, extracted_data)
        
        return extracted_data
    
    def _extract_patient_info(self, text_blocks: List[Dict], data: Dict):
        """提取患者基本信息"""
        # 姓名
        name = self._find_field_value(text_blocks, ['姓名'])
        if name:
            data['姓名'] = name
        
        # 住院号
        hospital_id = self._find_field_value(text_blocks, ['住院号', '病历号'])
        if hospital_id:
            data['住院号'] = hospital_id.split('，')[0] if '，' in hospital_id else hospital_id
        
        # 性别
        gender = self._find_field_value(text_blocks, ['性别'])
        if gender:
            data['性别'] = gender
        
        # 年龄
        age = self._find_field_value(text_blocks, ['年龄'])
        if age:
            # 提取纯数字
            age_match = re.search(r'\d+', age)
            if age_match:
                data['年龄'] = age_match.group(0)
            else:
                data['年龄'] = age
    
    def _extract_echo_data(self, text_blocks: List[Dict], grouped_blocks: Dict[str, List[Dict]], 
                         merged_lines: Dict[str, List[Dict]], data: Dict):
        """提取心超数据"""
        # 超声所见（超声所见关键词到超声提示关键词之间的所有文字内容）
        echo_findings = self._extract_long_text_between_keywords(text_blocks, '超声所见', '超声提示')
        if echo_findings:
            data['超声所见'] = echo_findings
        
        # 心超结论（超声提示关键词到报告医师关键词之间的所有文字内容）
        echo_conclusion = self._extract_long_text_between_keywords(text_blocks, '超声提示', '报告医师')
        if echo_conclusion:
            # 去除 "记录员：xxx" 及之后的内容
            conclusion_parts = re.split(r'记录员[：:]\s*\S+', echo_conclusion, 1)
            data['心超结论'] = conclusion_parts[0].strip()
        
        # --- 位置限制：心超数值不能在"超声所见"下方 ---
        # 找到"超声所见"的Y坐标
        ultrasound_finding_y = None
        # 使用原始文本块来定位"超声所见"
        for block in text_blocks:
            if '超声所见' in block['text']:
                ultrasound_finding_y = block.get('center_y')
                logger.debug(f'找到"超声所见"位置, Y坐标: {ultrasound_finding_y}')
                break
        
        # 对心超数值型结果应用模糊匹配和位置限制
        # 室间隔厚度
        ivs = self._find_field_with_number_new(text_blocks, merged_lines, ['室间隔厚度', '间隔厚', 'IVS'], allow_fuzzy=True, y_max_coord=ultrasound_finding_y)
        if ivs:
            value = self._extract_number_only(ivs)
            # 优选在5-30区间内的非小数数值
            try:
                num_value = float(value)
                if 5 <= num_value <= 30 and num_value.is_integer():
                    # 更可能是合理值
                    pass
            except (ValueError, AttributeError):
                pass
            data['室间隔厚度'] = value
        
        # 舒张末期前后径
        lvdd = self._find_field_with_number_new(text_blocks, merged_lines, ['舒张末期前后径','舒张未期前后径', 'LVDD'], allow_fuzzy=True, y_max_coord=ultrasound_finding_y)
        if lvdd:
            # 提取括号前的数字，如"42 (37~49)mm"中的"42"
            lvdd_match = re.search(r'(\d+\.?\d*)[\s]*\(', lvdd)
            if lvdd_match:
                data['舒张末期前后径'] = lvdd_match.group(1)
            else:
                data['舒张末期前后径'] = self._extract_number_only(lvdd)
        
        # 后壁厚度
        pw = self._find_field_with_number_new(text_blocks, merged_lines, ['后壁厚度', '后壁厚', '后壁', 'PW'], allow_fuzzy=True, y_max_coord=ultrasound_finding_y)
        if pw:
            value = self._extract_number_only(pw)
            # 优选在5-30区间内的非小数数值
            try:
                num_value = float(value)
                if 5 <= num_value <= 30 and num_value.is_integer():
                    # 更可能是合理值
                    pass
            except (ValueError, AttributeError):
                pass
            data['后壁厚度'] = value
        
        # EF值
        ef_value = self._find_ef_value(text_blocks, merged_lines, y_max_coord=ultrasound_finding_y)
        if ef_value:
            data['%EF'] = ef_value
    
    def _extract_ecg_conclusion(self, text_blocks: List[Dict], grouped_blocks: Dict[str, List[Dict]], 
                              merged_lines: Dict[str, List[Dict]], data: Dict):
        """提取心电图结论, 并追加后续对齐的行 (增强版)"""
        # 1. 查找包含"心电图报告"的图片
        has_ecg_report = False
        ecg_report_img_path = None
        for block in text_blocks:
            if "心电图报告" in block['text']:
                has_ecg_report = True
                ecg_report_img_path = block.get('image_path', '')
                logger.debug(f'找到心电图报告关键词: {block["text"]}')
                break
        
        if not has_ecg_report or not ecg_report_img_path or ecg_report_img_path not in merged_lines:
            logger.debug('未检测到心电图报告或相关文本行，跳过心电图结论提取')
            return

        logger.info(f'检测到心电图报告，准备提取心电图结论')
        lines = merged_lines[ecg_report_img_path]
        
        # 2. 精确定位包含"诊断"或"结论"的起始行和块
        start_line_idx = -1
        start_block = None
        for i, line in enumerate(lines):
            for block in line['line_blocks']:
                if "诊断" in block['text'] or "结论" in block['text']:
                    start_line_idx = i
                    start_block = block
                    break
            if start_block:
                break
        
        if not start_block:
            logger.debug("在心电图报告中未找到'诊断'或'结论'关键词")
            return

        # 3. 提取第一部分结论
        conclusion_parts = []
        text = start_block['text']
        keyword_pattern = r'(诊断|结论)[\s:：]*'
        match = re.search(keyword_pattern, text)
        if match:
            first_part = text[match.end():].strip()
            if first_part:
                conclusion_parts.append(first_part)

        # 如果关键词和内容在同一行但不同块，追加该行后续内容
        start_line_blocks = lines[start_line_idx]['line_blocks']
        start_block_index = start_line_blocks.index(start_block)
        for i in range(start_block_index + 1, len(start_line_blocks)):
            conclusion_parts.append(start_line_blocks[i]['text'])

        # 4. 查找并追加后续对齐的结论行
        ref_x = start_block.get('center_x', 0)  # 使用关键词块的X坐标作为对齐基准
        
        for i in range(start_line_idx + 1, len(lines)):
            current_line_blocks = lines[i]['line_blocks']
            if not current_line_blocks:
                continue

            current_line_text = lines[i]['text'].strip()
            first_block_in_line = current_line_blocks[0]
            current_x = first_block_in_line.get('center_x', 0)
            
            # 判断是否为结论的一部分：
            # a) X坐标与关键词大致对齐
            # b) 或者，文本以数字列表项开头 (e.g., "2.", "3 ")
            is_aligned = abs(current_x - ref_x) < 100  # 放宽对齐容差至100像素
            is_list_item = re.match(r'^\d+\s*[\.、]', current_line_text)

            if is_aligned or is_list_item:
                 # 过滤掉明显是其他字段的行
                if any(kw in current_line_text for kw in ['姓名', '心率', '性别', '年龄', '科室']):
                    continue
                conclusion_parts.append(current_line_text)
            # 如果一行内容既不对齐，也不是列表项，则认为结论结束
            elif not is_aligned and not is_list_item:
                # 检查是否是上一行结论的延续（如长句换行）
                prev_line_blocks = lines[i-1]['line_blocks']
                if prev_line_blocks:
                    last_block_of_prev_line = prev_line_blocks[-1]
                    # 如果当前行与上一行结论的最后一个块在Y上很近，也认为是延续
                    if abs(first_block_in_line.get('center_y', 0) - last_block_of_prev_line.get('center_y', 0)) < self.line_merge_threshold * 1.5:
                        # 但X不能离得太远
                        if abs(current_x - last_block_of_prev_line.get('center_x', 0)) < 400: # 避免跳到另一栏
                             if any(kw in current_line_text for kw in ['姓名', '心率', '性别', '年龄', '科室']):
                                continue
                             conclusion_parts.append(current_line_text)
                             continue # 继续检查下一行
                # 否则，确认结论结束
                break
        
        # 5. 合并并保存结论
        if conclusion_parts:
            final_conclusion = " ".join(filter(None, conclusion_parts))
            data['心电图结论'] = final_conclusion
            data['ECG图片路径'] = ecg_report_img_path
            logger.info(f'提取到心电图结论: {final_conclusion}')
        else:
            logger.debug("在'诊断'或'结论'关键词后未找到有效的结论文本")
    
    def _extract_lab_values(self, text_blocks: List[Dict], merged_lines: Dict[str, List[Dict]], data: Dict):
        """提取检验结果"""
        # 血红蛋白 (偏好整数, 排除其他含血红蛋白的项目)
        hb = self._find_field_with_number_new(
            text_blocks, merged_lines, 
            ['血红蛋白', '*血红蛋白', 'HB'], 
            exclude_keywords=['糖化', '平均', '浓度', '分布'], 
            prefer_integer=True
        )
        if hb:
            data['血红蛋白'] = self._extract_number_only(hb)
        
        # 糖化血红蛋白
        glycated_hb = self._find_field_with_number_new(text_blocks, merged_lines, ['糖化血红蛋白', '糖化血红'])
        if glycated_hb:
            data['糖化血红蛋白'] = self._extract_number_only(glycated_hb)
        
        # 血糖（葡萄糖，但前面不能有"尿"字）
        glucose = self._find_field_with_number_new(text_blocks, merged_lines, ['葡萄糖', '血糖','葡糖','萄糖'], exclude_keywords=['尿'])
        if glucose:
            data['血糖'] = self._extract_number_only(glucose)
        
        # 甘油三酯
        tg = self._find_field_with_number_new(text_blocks, merged_lines, ['甘油三酯','甘油三', '甘油', 'TG'], allow_fuzzy=True)
        if tg:
            data['甘油三酯'] = self._extract_number_only(tg)
        
        # 总胆固醇
        tc = self._find_field_with_number_new(text_blocks, merged_lines, ['总胆固醇', '总胆固', '总胆', 'TC'], allow_fuzzy=True)
        if tc:
            data['总胆固醇'] = self._extract_number_only(tc)
        
        # 低密度脂蛋白胆固醇
        ldl = self._find_field_with_number_new(text_blocks, merged_lines, ['低密度脂蛋白胆固醇', '低密度脂蛋白', '低密度', 'LDL'], allow_fuzzy=True)
        if ldl:
            data['低密度脂蛋白胆固醇'] = self._extract_number_only(ldl)
        
        # 高密度脂蛋白胆固醇
        hdl = self._find_field_with_number_new(text_blocks, merged_lines, ['高密度脂蛋白胆固醇', '高密度脂蛋白', '高密度', 'HDL'], allow_fuzzy=True)
        if hdl:
            data['高密度脂蛋白胆固醇'] = self._extract_number_only(hdl)
        
        # 载脂蛋白A1
        apoa1 = self._find_field_with_number_new(text_blocks, merged_lines, ['载脂蛋白A1', '载脂蛋白A 1', '载脂蛋白A', 'ApoA1'], allow_fuzzy=True)
        if apoa1:
            data['载脂蛋白A1'] = self._extract_number_only(apoa1)
        
        # 载脂蛋白B
        apob = self._find_field_with_number_new(text_blocks, merged_lines, ['载脂蛋白B', '载脂', 'ApoB'], allow_fuzzy=True)
        if apob:
            data['载脂蛋白B'] = self._extract_number_only(apob)
        
        # 脂蛋白(a)
        lpa = self._find_field_with_number_new(text_blocks, merged_lines, ['脂蛋白(a)', '脂蛋白a', '脂蛋白', 'Lp(a)'], allow_fuzzy=True)
        if lpa:
            data['脂蛋白(a)'] = self._extract_number_only(lpa)
        
        # pro-BNP (B型钠尿肽前体)，取较高值
        pro_bnp = self._find_highest_value(text_blocks, ['B型钠尿肽前体', 'B型钠', '肽前体'])
        if not pro_bnp:
            # 尝试在合并行中查找
            pro_bnp = self._find_highest_value_in_lines(merged_lines, ['B型钠尿肽前体', 'B型钠', '肽前体'])
        if pro_bnp:
            data['pro-BNP'] = self._extract_number_only(pro_bnp)
        
        # 高敏肌钙蛋白T
        tnt = self._find_field_with_number_new(text_blocks, merged_lines, ['肌钙蛋白T', '肌钙蛋白 T', '高敏肌钙蛋白T'])
        if tnt:
            data['高敏肌钙蛋白T'] = self._extract_number_only(tnt)
        
        # 高敏肌钙蛋白I
        tni = self._find_field_with_number_new(text_blocks, merged_lines, ['肌钙蛋白I', '肌钙蛋白 I', '高敏肌钙蛋白I', '肌钙蛋白'], exclude_keywords=['T', 'T '])
        if tni:
            data['高敏肌钙蛋白I'] = self._extract_number_only(tni, keep_operator=True)
        
        # 肌红蛋白
        myo = self._find_field_with_number_new(text_blocks, merged_lines, ['肌红蛋白', 'Myo'])
        if myo:
            data['肌红蛋白'] = self._extract_number_only(myo)
        
        # 肌酸激酶
        ck = self._find_field_with_number_new(text_blocks, merged_lines, ['肌酸激酶','*肌酸激酶', '肌酸'], exclude_keywords=['同功酶'], allow_fuzzy=True)
        if ck:
            data['肌酸激酶'] = self._extract_number_only(ck)
        
        # 肌酸激酶同功酶，取较高值
        ckmb = self._find_highest_value(text_blocks, ['肌酸激酶同功酶', '肌酸激酶同功酶质量测定', '肌酸激酶同工酶', 'CK-MB'])
        if not ckmb:
            # 尝试在合并行中查找
            ckmb = self._find_highest_value_in_lines(merged_lines, ['肌酸激酶同功酶', 'CK-MB'])
        if ckmb:
            numeric_value = self._extract_number_only(ckmb)
            data['肌酸激酶同功酶'] = numeric_value

    def _extract_long_text_between_keywords(self, text_blocks: List[Dict], start_keyword: str, end_keyword: str) -> str:
        """
        根据Y轴坐标和横向位置提取两个关键词之间的所有文字内容。
        此方法按页处理，直接操作原始文本块，以应对复杂布局和多页问题。
        """
        # 确保所有块都有坐标和 image_path
        for block in text_blocks:
            if 'image_path' not in block: continue
            if 'center_y' not in block or 'center_x' not in block:
                positions = block.get('position', [])
                if positions and len(positions) >= 4:
                    block['center_y'] = sum(p[1] for p in positions) / len(positions)
                    block['center_x'] = sum(p[0] for p in positions) / len(positions)

        # 按页面对文本块进行分组
        blocks_by_page = {}
        for block in text_blocks:
            path = block.get('image_path')
            if not path: continue
            if path not in blocks_by_page:
                blocks_by_page[path] = []
            blocks_by_page[path].append(block)
        
        # 逐页搜索
        for img_path, page_blocks in blocks_by_page.items():
            # 在当前页面上查找起始关键词块
            candidate_start_blocks = [b for b in page_blocks if start_keyword in b['text']]
            if not candidate_start_blocks:
                continue

            start_block = min(candidate_start_blocks, key=lambda b: b.get('center_y', 0))

            # 在同一页面上查找结束关键词块
            candidate_end_blocks = [
                b for b in page_blocks 
                if end_keyword in b['text'] and b.get('center_y', 0) > start_block.get('center_y', 0)
            ]
            end_block = min(candidate_end_blocks, key=lambda b: b.get('center_y', 0)) if candidate_end_blocks else None

            start_y = start_block.get('center_y', 0)
            start_x = start_block.get('center_x', 0)
            end_y = end_block.get('center_y', 0) if end_block else float('inf')
            
            # 定义一个横向的栏宽来过滤无关列的文本
            column_x_min = start_x - 100 # 允许左边有一些缩进
            column_x_max = start_x + 500 # 定义一个合理的右边界
            
            # 收集所有相关的块
            relevant_blocks = [
                b for b in page_blocks 
                if start_y <= b.get('center_y', 0) and 
                   b.get('center_y', 0) <= end_y and
                   column_x_min <= b.get('center_x', 0) <= column_x_max
            ]

            if not relevant_blocks:
                continue

            # 核心逻辑：按Y坐标排序，Y坐标相同时按X坐标排序
            relevant_blocks.sort(key=lambda b: (b.get('center_y', 0), b.get('center_x', 0)))
            
            full_text = " ".join(b['text'] for b in relevant_blocks)
            
            # 修正提取逻辑：在拼接后的文本中查找起止点
            start_pos = full_text.find(start_keyword)
            end_pos = full_text.find(end_keyword)
            
            # 如果没找到起始词，说明逻辑有问题，跳过
            if start_pos == -1:
                continue
                
            # 提取起始词之后的内容
            extracted_text = full_text[start_pos + len(start_keyword):]
            
            # 如果找到了结束词，则在起始词之后的内容里再找结束词
            # 这样做可以避免错误地匹配到正文之前的结束词
            if end_pos != -1:
                end_pos_in_extracted = extracted_text.find(end_keyword)
                if end_pos_in_extracted != -1:
                    extracted_text = extracted_text[:end_pos_in_extracted]

            cleaned_text = re.sub(r'^[\s:：]+', '', extracted_text)
            return cleaned_text.strip()

        # 如果循环结束仍未返回，说明未找到
        logger.debug(f"未在任何单页上找到 '{start_keyword}' 和 '{end_keyword}' 之间的文本")
        return ""

    def _find_field_value(self, text_blocks: List[Dict], keywords: List[str], exclude_keywords: List[str] = None) -> Optional[str]:
        """根据关键词查找字段值"""
        if exclude_keywords is None:
            exclude_keywords = []
        
        for block in text_blocks:
            text = block['text']
            # 检查是否包含排除关键词
            if any(ex_kw in text for ex_kw in exclude_keywords):
                continue
            
            for keyword in keywords:
                if keyword in text:
                    # 提取冒号、等号或空格后的值
                    pattern = f"{keyword}[：:=\\s]+([^\\n]+)"
                    match = re.search(pattern, text)
                    if match:
                        return match.group(1).strip()
                    
                    # 如果没有冒号等，尝试提取关键词后的内容
                    parts = text.split(keyword)
                    if len(parts) > 1:
                        return parts[1].strip()
        
        return None
    
    def _find_field_with_number_new(self, text_blocks: List[Dict], merged_lines: Dict[str, List[Dict]], 
                                 keywords: List[str], exclude_keywords: List[str] = None, allow_fuzzy=False,
                                 y_max_coord: Optional[float] = None, prefer_integer: bool = False) -> Optional[str]:
        """
        增强的查找包含数字的字段值
        - 统一收集所有可能的候选值，然后进行综合评分排序，选出最优值。
        - 评分模型优先考虑Y轴对齐，其次是X轴距离和中间内容。
        """
        if exclude_keywords is None:
            exclude_keywords = []
            
        found_values = []
        
        # --- 内部辅助函数 ---
        def fuzzy_keyword_match(text: str, keywords: List[str], threshold=0.8) -> bool:
            """
            更强大的模糊匹配:
            1. 优先进行精确匹配.
            2. 支持具有OCR错误的模糊匹配
            3. 针对特定字段的特殊模式匹配
            """
            # 1. 精确匹配
            if any(kw in text for kw in keywords):
                return True
            
            # 2. 特殊字段的特殊模式匹配
            # 甘油三酯：匹配"甘x三x"模式
            if '甘油三酯' in keywords and re.search(r'甘.{0,2}三.{0,2}', text):
                logger.debug(f"特殊模式匹配成功: '{text}' 匹配 '甘油三酯'")
                return True
            
            # 肌酸激酶：匹配"肌酸x酶"或"肌酸激x"模式
            if '肌酸激酶' in keywords and (re.search(r'肌酸.{0,2}酶', text) or re.search(r'肌酸激.{0,2}', text)):
                logger.debug(f"特殊模式匹配成功: '{text}' 匹配 '肌酸激酶'")
                return True
            
            # 室间隔厚度：匹配"室间x厚度"，"室间隔x度"或"室间隔厚x"模式
            if '室间隔厚度' in keywords and (re.search(r'室间.{0,2}厚度', text) or re.search(r'室间隔.{0,2}度', text) or re.search(r'室间隔厚.{0,2}', text)):
                logger.debug(f"特殊模式匹配成功: '{text}' 匹配 '室间隔厚度'")
                return True
            
            # 后壁厚度：匹配"后x厚度"，"后壁x度"或"后壁厚x"模式
            if '后壁厚度' in keywords and (re.search(r'后.{0,2}厚度', text) or re.search(r'后壁.{0,2}度', text) or re.search(r'后.{0,2}厚.{0,2}度', text)):
                logger.debug(f"特殊模式匹配成功: '{text}' 匹配 '后壁厚度'")
                return True
            
            # 3. 其他字段的模糊匹配
            # if len(text) >= 4:  # 只对较长的文本块进行模糊匹配
            #     for kw in keywords:
            #         if len(kw) < 4: continue  # 短词不进行模糊匹配
            #
            #         # 与文本中长度相近的子串进行比较
            #         for i in range(len(text) - len(kw) + 1):
            #             substring = text[i : i + len(kw)]
            #             ratio = difflib.SequenceMatcher(None, kw, substring).ratio()
            #             if ratio >= threshold:
            #                 logger.debug(f"模糊匹配成功: '{substring}' (来自'{text}') 匹配关键词 '{kw}', 相似度: {ratio:.2f}")
            #                 return True
            return False

        def extract_and_normalize_number(text: str) -> Optional[str]:
            """提取数字并标准化, 处理 '15.' 和 '0. 5' 等情况"""
            if not text: return None
            
            # 预处理: 替换常见OCR错误
            processed_text = text.replace('TOmm', '10mm').replace('O. ', '0.').replace('o.', '0.')
            processed_text = re.sub(r'[Ss]m', '8m', processed_text, flags=re.IGNORECASE) # 处理"8mm"的OCR错误
            processed_text = re.sub(r'[lI1][oO0]', '10', processed_text, flags=re.IGNORECASE) # 处理"10"的OCR错误
            processed_text = re.sub(r'[lI1][oO0][oO0]', '100', processed_text, flags=re.IGNORECASE) # 处理"100"的OCR错误
            processed_text = re.sub(r'\bT\b', '7', processed_text) # 单个T替换成7
            # 合并数字间的空格, 如 '0. 5' -> '0.5'
            processed_text = re.sub(r'(\d+\.)\s+(\d+)', r'\1\2', processed_text)
            
            # 查找数字, 包含可选的 '<' 或 '>'
            num_match = re.search(r'([<>])?\s*(\d+\.?\d*)', processed_text)
            if not num_match: return None

            operator = num_match.group(1) or ''
            number_str = num_match.group(2) or ''
            
            # 处理结尾的小数点, 如 '15.' -> '15.0'
            if number_str.endswith('.'):
                number_str += '0'
            
            # 过滤掉明显是年份的数值
            try:
                if float(number_str) > 2000:
                    return None
            except ValueError:
                pass

            return f"{operator}{number_str}"

        def get_intervening_text(text: str, keyword: str, number_str: str) -> str:
            """获取关键词和数字之间的文本"""
            try:
                kw_pos = text.find(keyword)
                num_pos = text.find(number_str)
                if kw_pos == -1 or num_pos == -1: return ""
                
                start = min(kw_pos + len(keyword), num_pos)
                end = max(kw_pos, num_pos) # 应该取关键词的开头或数字的开头
                return text[start:end]
            except:
                return ""

        # --- 1. 过滤有效文本块 ---
        valid_blocks = [b for b in text_blocks if y_max_coord is None or b.get('center_y', 0) < y_max_coord]
        
        # --- 2. 收集所有可能的候选值 ---
        # 从所有文本块中找到关键词块和数值块
        keyword_blocks = []
        for b in valid_blocks:
            if fuzzy_keyword_match(b['text'], keywords):
                # 检查关键词块是否含有排除词
                if not any(ex_kw in b['text'] for ex_kw in exclude_keywords):
                    keyword_blocks.append(b)
                    
        number_blocks = [b for b in valid_blocks if extract_and_normalize_number(b['text'])]
        
        # 记录关键词块的位置，用于调试
        if keyword_blocks:
            kw_positions = [(b.get('center_x', 0), b.get('center_y', 0)) for b in keyword_blocks]
            logger.debug(f"关键词 {keywords} 块位置: {kw_positions}")
            
        # 记录找到的数值块，用于调试
        if number_blocks:
            num_texts = [(b['text'], b.get('center_y', 0)) for b in number_blocks[:5]]
            logger.debug(f"找到的数值块(前5个): {num_texts}")
        
        for kw_block in keyword_blocks:
            # 检查关键词块本身是否包含数字，如果包含则跳过该块
            if extract_and_normalize_number(kw_block['text']):
                # 如果关键词块本身包含数字，检查是否是我们要找的数字
                kw_text = kw_block['text']
                for keyword in keywords:
                    if keyword in kw_text:
                        # 提取关键词后面的内容
                        parts = kw_text.split(keyword, 1)
                        if len(parts) > 1 and extract_and_normalize_number(parts[1]):
                            # 将这个块同时作为关键词块和数值块处理
                            number = extract_and_normalize_number(parts[1])
                            found_values.append({
                                'text': kw_text,
                                'number': number,
                                'y_diff': 0,  # 同一块内，y_diff设为0
                                'x_diff': 10,  # 设置一个合理的小值
                                'has_decimal': '.' in number,
                                'is_integer': '.' not in number,
                                'intervening_chars': 0,
                                'same_block': True,  # 标记为同一块
                                'contains_keyword': True  # 新增：标记为包含关键词
                            })
                
            for num_block in number_blocks:
                # 避免自己匹配自己
                if kw_block == num_block:
                    continue
                    
                # 必须在同一张图片
                if kw_block.get('image_path') != num_block.get('image_path'):
                    continue

                number = extract_and_normalize_number(num_block['text'])
                if not number: 
                    continue
                
                # 计算距离和差异
                kw_y = kw_block.get('center_y', 0)
                num_y = num_block.get('center_y', 0)
                kw_x = kw_block.get('center_x', 0)
                num_x = num_block.get('center_x', 0)
                
                y_diff = abs(kw_y - num_y)
                x_diff = num_x - kw_x
                
                # 必须在右侧，且不能太远 (放宽X轴距离限制)
                if x_diff < 0 or x_diff > (self.field_search_distance * 4) or y_diff > 50:
                    continue
                    
                # 检查中间是否有其他关键词或排除词
                intervening_blocks = [b for b in valid_blocks if 
                                      b.get('image_path') == kw_block.get('image_path') and
                                      b != kw_block and b != num_block and
                                      min(kw_x, num_x) < b.get('center_x',0) < max(kw_x, num_x) and
                                      abs(b.get('center_y',0) - kw_y) < 20
                                     ]
                
                intervening_text = " ".join([b['text'] for b in intervening_blocks])
                if any(ex_kw in intervening_text for ex_kw in exclude_keywords):
                    continue
                
                # 记录详细的位置信息用于调试
                position_info = {
                    'kw_y': kw_y,
                    'num_y': num_y,
                    'kw_x': kw_x,
                    'num_x': num_x
                }
                
                found_values.append({
                    'text': num_block['text'],
                    'number': number,
                    'y_diff': y_diff,
                    'x_diff': x_diff,
                    'has_decimal': '.' in number,
                    'is_integer': '.' not in number,
                    'intervening_chars': len(re.findall(r'[\u4e00-\u9fa5]', intervening_text)),
                    'position_info': position_info,
                    'same_block': False,
                    'contains_keyword': False
                })

        # --- 3. 如果没有找到, 尝试从合并行中进行补充查找 ---
        if not found_values:
             for img_path, lines in merged_lines.items():
                for line in lines:
                     if fuzzy_keyword_match(line['text'], keywords):
                         # ... (此处可以补充行内查找逻辑作为备用)
                         pass

        return self._select_best_value(found_values, keywords, prefer_integer)

    def _select_best_value(self, found_values: List[Dict], keywords: List[str], prefer_integer: bool) -> Optional[str]:
        """
        从候选值列表中，根据综合评分模型选择最优值
        """
        if not found_values:
            return None
        
        # --- 4. 排序和选择最优结果 ---
        
        # 4.1 计算每个候选值的综合得分
        for v in found_values:
            # 对于同一块中的关键词和数值，给予较高评分
            if v.get('same_block', False):
                # 如果文本块同时包含关键词和数值，给予高评分
                if v.get('contains_keyword', False):
                    v['score'] = 0.95  # 提高包含关键词的块的评分
                else:
                    v['score'] = 0.85  # 同一块内的其他匹配
                continue
                
            # Y轴对齐是最高优先级，使用指数衰减函数，y_diff越大，评分下降越快
            # 这样可以更好地区分不同y_diff的情况
            y_score = 1.0 * (0.95 ** v['y_diff'])
            
            # X轴距离，x_diff越小越好，但重要性低于y轴
            x_score = 1.0 / (1.0 + (v['x_diff'] / 100))
            
            # 中间字符数，越少越好
            char_score = 1.0 / (1.0 + v['intervening_chars'])
            
            # 整数偏好
            integer_bonus = 0.1 if prefer_integer and v['is_integer'] else 0
            
            # 小数偏好 (与整数偏好互斥)
            decimal_bonus = 0.05 if not prefer_integer and v['has_decimal'] else 0

            # 综合得分: Y轴权重更高，确保y轴差异能明显影响最终评分
            v['score'] = (y_score * 0.7) + (x_score * 0.2) + (char_score * 0.05) + integer_bonus + decimal_bonus
            
            # 记录详细的评分组成，用于调试
            v['score_details'] = {
                'y_score': y_score,
                'x_score': x_score,
                'char_score': char_score,
                'integer_bonus': integer_bonus,
                'decimal_bonus': decimal_bonus
            }
        
        # 4.2 按综合得分从高到低排序
        found_values.sort(key=lambda v: v.get('score', 0), reverse=True)
        
        # 记录详细的评分信息
        debug_values = []
        for v in found_values[:5]:  # 只记录前5个结果
            debug_info = f"'{v['text']}'(y_diff:{v['y_diff']:.1f}, x_diff:{v['x_diff']:.1f}, score:{v.get('score',0):.2f}"
            if 'score_details' in v:
                details = v['score_details']
                debug_info += f", y_score:{details['y_score']:.2f}"
            debug_info += ")"
            debug_values.append(debug_info)
            
        logger.debug(f"关键词 {keywords} 找到的排序后可能值: " + ', '.join(debug_values))
        
        best_value = found_values[0]
        logger.debug(f"选择的最优值: '{best_value['text']}', 数值: {best_value['number']}, 得分: {best_value.get('score',0):.2f}")
        return best_value['text']

    def _find_highest_value(self, text_blocks: List[Dict], keywords: List[str]) -> Optional[str]:
        """查找给定关键词的最高数值"""
        # 注释掉保留较大值的逻辑，使用正常的字段查找
        return self._find_field_with_number_new(text_blocks, {}, keywords)
        
        # 以下是原始保留较大值的逻辑，暂时注释掉
        """
        highest_value = -1
        highest_text = None
        
        for block in text_blocks:
            text = block['text']
            
            for keyword in keywords:
                if keyword in text:
                    # 提取冒号、等号或空格后的数字
                    pattern = f"{keyword}[：:=\\s]+([^\\n]+)"
                    match = re.search(pattern, text)
                    value_text = None
                    
                    if match:
                        value_text = match.group(1).strip()
                    else:
                        parts = text.split(keyword)
                        if len(parts) > 1:
                            value_text = parts[1].strip()
                    
                    if value_text:
                        # 提取数字
                        number_match = re.search(r'(\d+\.?\d*)', value_text)
                        if number_match:
                            value = float(number_match.group(1))
                            if value > highest_value:
                                highest_value = value
                                highest_text = text
        
        return highest_text
        """
    
    def _find_highest_value_in_lines(self, merged_lines: Dict[str, List[Dict]], keywords: List[str]) -> Optional[str]:
        """在合并行中查找给定关键词的最高数值"""
        # 注释掉保留较大值的逻辑，使用正常的字段查找
        return self._find_field_with_number_new([], merged_lines, keywords)
        
        # 以下是原始保留较大值的逻辑，暂时注释掉
        """
        highest_value = -1
        highest_text = None
        
        for img_path, lines in merged_lines.items():
            for line in lines:
                text = line['text']
                
                for keyword in keywords:
                    if keyword in text:
                        # 提取数字
                        number_match = re.search(r'(\d+\.?\d*)', text)
                        if number_match:
                            value = float(number_match.group(1))
                            if value > highest_value:
                                highest_value = value
                                highest_text = text
        
        return highest_text
        """
    
    def _extract_number_only(self, text: str, keep_operator=False) -> str:
        """从文本中仅提取数字部分, 可选择保留前置的<或>"""
        if not text:
            return ""

        # 将所有清理和标准化逻辑集中到这里
        # 预处理: 替换常见OCR错误
        processed_text = text.replace('TOmm', '10mm').replace('O. ', '0.').replace('o.', '0.')
        processed_text = re.sub(r'[Ss]m', '8m', processed_text, flags=re.IGNORECASE) # 处理"8mm"的OCR错误
        processed_text = re.sub(r'[lI1][oO0]', '10', processed_text, flags=re.IGNORECASE) # 处理"10"的OCR错误
        processed_text = re.sub(r'[lI1][oO0][oO0]', '100', processed_text, flags=re.IGNORECASE) # 处理"100"的OCR错误
        processed_text = re.sub(r'\bT\b', '7', processed_text) # 单个T替换成7
        # 合并数字间的空格, 如 '0. 5' -> '0.5'
        processed_text = re.sub(r'(\d+\.)\s+(\d+)', r'\1\2', processed_text)
            
        if keep_operator:
            # 匹配可选的<或>符号, 以及后续的数字
            match = re.search(r'([<>])?\s*(\d+\.?\d*)', processed_text)
            if match:
                operator = match.group(1) or ''
                number = match.group(2) or ''
                # 补全小数点
                if number.endswith('.') and '.' in number:
                    number += '0'
                return f"{operator}{number}".strip()
        
        # 提取所有数字（整数或小数）
        number_match = re.search(r'(\d+\.?\d*)', processed_text)
        if number_match:
            number = number_match.group(1)
            if number.endswith('.') and '.' in number:
                number += '0'
            return number
        
        return text
    
    def _find_ef_value(self, text_blocks: List[Dict], merged_lines: Dict[str, List[Dict]], y_max_coord: Optional[float] = None) -> Optional[str]:
        """查找EF值"""
        # 常见的EF值表达方式
        ef_patterns = [
            r'EF[：:=\s]+(\d+\.?\d*)[%％]?',
            r'射血分数[：:=\s]+(\d+\.?\d*)[%％]?',
            r'LVEF[：:=\s]+(\d+\.?\d*)[%％]?'
        ]
        
        # 1. 先尝试在"左心功能测定"相关块中寻找
        for block in text_blocks:
            if y_max_coord is not None and block.get('center_y', 0) >= y_max_coord:
                continue  # 跳过超声所见下方的块
                
            if '左心功能' in block['text'] or 'EF' in block['text'] or '射血分数' in block['text']:
                for pattern in ef_patterns:
                    match = re.search(pattern, block['text'])
                    if match:
                        return match.group(1)

        # 2. 如果没找到，按之前的搜索逻辑继续
        valid_blocks = [b for b in text_blocks if y_max_coord is None or b.get('center_y', 0) < y_max_coord]
        valid_lines_by_img = {
            img_path: [l for l in lines if y_max_coord is None or (l['line_blocks'] and l['line_blocks'][0].get('center_y', 0) < y_max_coord)]
            for img_path, lines in merged_lines.items()
        }

        # 先查找文本块
        for block in valid_blocks:
            text = block['text']
            for pattern in ef_patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(1)
                    
        # 如果没找到，查找合并行
        for img_path, lines in valid_lines_by_img.items():
            for line in lines:
                text = line['text']
                for pattern in ef_patterns:
                    match = re.search(pattern, text)
                    if match:
                        return match.group(1)
        
        # 如果上述方法都没找到，尝试查找任何包含"EF"和数字的内容
        for img_path, lines in valid_lines_by_img.items():
            for line in lines:
                text = line['text']
                if 'EF' in text or '射血分数' in text:
                    number_match = re.search(r'(\d+\.?\d*)[%％]?', text)
                    if number_match:
                        return number_match.group(1)
        
        return None 

    def _merge_data(self, primary_data: dict, pipeline_data: dict, is_ultrasound_page: bool) -> dict:
        """
        根据特定规则合并两次OCR提取的数据。
        - 对超声报告的特定字段，管线结果强制覆盖。
        - 根据配置决定是否对所有非空字段强制覆盖。
        """
        merged_data = primary_data.copy()
        ultrasound_overwrite_fields = ["室间隔厚度", "舒张末期前后径", "后壁厚度"]

        for field, pipeline_value in pipeline_data.items():
            # 忽略管线未提取出的值或空值
            if not pipeline_value:
                continue

            # 规则1: 对超声页面的特定字段，执行强制覆盖
            if is_ultrasound_page and field in ultrasound_overwrite_fields:
                if merged_data.get(field) != pipeline_value:
                    logger.info(f"字段覆盖 (超声): '{field}' | 原值: '{merged_data.get(field)}' -> 新值: '{pipeline_value}'")
                    merged_data[field] = pipeline_value
                continue

            # 规则2: 根据配置决定是否强制覆盖所有非空字段
            if self.force_pipeline_override:
                if merged_data.get(field) != pipeline_value:
                    logger.info(f"字段覆盖 (强制): '{field}' | 原值: '{merged_data.get(field)}' -> 新值: '{pipeline_value}'")
                    merged_data[field] = pipeline_value
            else:
                # 仅填充主结果中的空值
                if not merged_data.get(field):
                    logger.info(f"字段填充 (空值): '{field}' -> 新值: '{pipeline_value}'")
                    merged_data[field] = pipeline_value
        
        # 对合并后的数据进行修正
        merged_data = self._fix_echo_data(merged_data)
        
        return merged_data

    def _fix_echo_data(self, data: dict) -> dict:
        """
        对心超数据进行特殊修正:
        1. 如果室间隔厚度或后壁厚度为1，则改为10
        2. 如果室间隔厚度或后壁厚度>50，则去掉个位数
        3. 如果后壁厚度为空而室间隔厚度不为空，则后壁厚度=室间隔厚度
        """
        # 修正室间隔厚度
        if '室间隔厚度' in data and data['室间隔厚度']:
            try:
                ivs_value = float(data['室间隔厚度'])
                
                # 处理情况1: 值为1的情况修正为10
                if ivs_value == 1:
                    logger.info(f"修正室间隔厚度: 1 -> 10 (OCR识别错误修正)")
                    data['室间隔厚度'] = '10'
                
                # 处理情况2: 值>50的情况，去掉个位数
                elif ivs_value > 50:
                    new_value = str(int(ivs_value / 10))
                    logger.info(f"修正室间隔厚度: {data['室间隔厚度']} -> {new_value} (去掉个位)")
                    data['室间隔厚度'] = new_value
            except (ValueError, TypeError):
                logger.warning(f"室间隔厚度 '{data['室间隔厚度']}' 不是有效数值，跳过修正")
        
        # 修正后壁厚度
        if '后壁厚度' in data and data['后壁厚度']:
            try:
                pw_value = float(data['后壁厚度'])
                
                # 处理情况1: 值为1的情况修正为10
                if pw_value == 1:
                    logger.info(f"修正后壁厚度: 1 -> 10 (OCR识别错误修正)")
                    data['后壁厚度'] = '10'
                
                # 处理情况2: 值>50的情况，去掉个位数
                elif pw_value > 50:
                    new_value = str(int(pw_value / 10))
                    logger.info(f"修正后壁厚度: {data['后壁厚度']} -> {new_value} (去掉个位)")
                    data['后壁厚度'] = new_value
            except (ValueError, TypeError):
                logger.warning(f"后壁厚度 '{data['后壁厚度']}' 不是有效数值，跳过修正")
        
        # 处理情况3: 后壁厚度为空而室间隔厚度不为空
        if (not data.get('后壁厚度') and data.get('室间隔厚度')):
            logger.info(f"修正后壁厚度: 空值 -> {data['室间隔厚度']} (基于室间隔厚度)")
            data['后壁厚度'] = data['室间隔厚度']
        
        return data

    def process_page(self, page_num, img_path):
        """
        处理单个页面，包含主处理和可选的二次管线处理流程。
        """
        # 1. 主OCR识别
        logger.info(f"第 {page_num + 1} 页 - 执行主OCR...")
        primary_text_blocks = self.ocr_engine.recognize_text(img_path, use_pipeline=False)
        if not primary_text_blocks:
            logger.warning(f"第 {page_num + 1} 页 - 主OCR未返回任何结果。")
            return {}

        # 2. 提取主数据和全部文本
        primary_data, all_text = self._extract_fields(primary_text_blocks, return_all_text=True)

        # 3. 判断是否需要二次处理
        is_ultrasound_page = any(kw in all_text for kw in ["超声结论", "超声提示"])
        is_bloodsugar_page = "血糖" in all_text

        if not is_ultrasound_page and not is_bloodsugar_page:
            logger.info(f"第 {page_num + 1} 页 - 无需二次处理。")
            # 即使不进行二次处理，也应应用数据修正
            return self._fix_echo_data(primary_data)

        # 4. 执行带管线的二次OCR
        secondary_keywords = "超声" if is_ultrasound_page else "血糖"
        logger.info(f"第 {page_num + 1} 页 - 检测到 '{secondary_keywords}' 关键词，执行管线增强识别...")
        pipeline_text_blocks = self.ocr_engine.recognize_text(img_path, use_pipeline=True)
        if not pipeline_text_blocks:
            logger.warning(f"第 {page_num + 1} 页 - 管线OCR未返回任何结果，将使用主OCR结果。")
            # 即使没有管线OCR结果，也应应用数据修正
            return self._fix_echo_data(primary_data)

        # 5. 提取管线处理后的数据
        pipeline_data, _ = self._extract_fields(pipeline_text_blocks, return_all_text=True)

        # 6. 根据规则合并数据
        logger.info(f"第 {page_num + 1} 页 - 合并主OCR和管线OCR的结果...")
        final_data = self._merge_data(primary_data, pipeline_data, is_ultrasound_page)

        return final_data

    def _extract_fields(self, text_blocks, return_all_text=False):
        # 合并所有文本块以进行关键词检查
        all_text = ' '.join(block['text'] for block in text_blocks)

        # 基础信息提取
        extracted_data = {}
        extracted_data['姓名'] = self._find_value_for_key(text_blocks, ['姓名', '患者姓名'])
        extracted_data['住院号'] = self._find_value_for_key(text_blocks, ['住院号', '病历号'])
        extracted_data['性别'] = self._find_value_for_key(text_blocks, ['性别'])
        extracted_data['年龄'] = self._find_value_for_key(text_blocks, ['年龄'])

        # 提取心超数据
        self._extract_echo_data(text_blocks, {}, {}, extracted_data)

        # 提取检验结果
        self._extract_lab_values(text_blocks, {}, extracted_data)

        # 提取心电图结论
        ecg_conclusion = self._extract_ecg_conclusion(text_blocks, {}, {}, extracted_data)

        # 将提取的心超结论添加到最终数据
        if ecg_conclusion:
            extracted_data['心超结论'] = ecg_conclusion

        if return_all_text:
            return extracted_data, all_text
        else:
            return extracted_data

    def _find_value_for_key(self, text_blocks: List[Dict], keywords: List[str], search_right=True, is_numeric=True, numeric_range=None) -> Optional[str]:
        """根据关键词查找字段值"""
        for block in text_blocks:
            text = block['text']
            for keyword in keywords:
                if keyword in text:
                    # 提取冒号、等号或空格后的值
                    pattern = f"{keyword}[：:=\\s]+([^\\n]+)"
                    match = re.search(pattern, text)
                    if match:
                        return match.group(1).strip()
                    
                    # 如果没有冒号等，尝试提取关键词后的内容
                    parts = text.split(keyword)
                    if len(parts) > 1:
                        return parts[1].strip()
        
        return None 