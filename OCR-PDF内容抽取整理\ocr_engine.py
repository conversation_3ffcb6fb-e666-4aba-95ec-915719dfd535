import os
import logging
import numpy as np
import cv2
from paddleocr import PaddleOCR
from configparser import ConfigParser
import uuid

logger = logging.getLogger('PDFExtractor')


# --- 1. 复制自 visual_debugger.py 的处理步骤定义 ---

class ProcessingStep:
    """图像处理步骤的基类"""
    def __init__(self, params: dict):
        self.params = params
    def apply(self, image):
        raise NotImplementedError

class Grayscale(ProcessingStep):
    def apply(self, image):
        if len(image.shape) == 3:
            return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        return image

class ContrastEnhancement(ProcessingStep):
    def apply(self, image):
        if len(image.shape) == 3:
            logger.warning("'ContrastEnhancement' 收到彩色图像，可能效果不佳。建议先灰度化。")
            return image
        clip_limit = float(self.params.get('clip_limit', 2.0))
        grid_size_str = self.params.get('grid_size', '8,8').split(',')
        grid_size = (int(grid_size_str[0]), int(grid_size_str[1]))
        clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=grid_size)
        return clahe.apply(image)

class Denoise(ProcessingStep):
    def apply(self, image):
        if len(image.shape) == 3:
            logger.warning("'Denoise' 收到彩色图像，可能效果不佳。建议先灰度化。")
            return image
        if image.dtype != np.uint8:
            image = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        h = int(self.params.get('h', 10))
        return cv2.fastNlMeansDenoising(image, None, h, 7, 21)

class Sharpen(ProcessingStep):
    def apply(self, image):
        strength = int(self.params.get('strength', 9))
        kernel = np.array([[-1, -1, -1], [-1, strength, -1], [-1, -1, -1]])
        return cv2.filter2D(image, -1, kernel)

class Binarize(ProcessingStep):
    def apply(self, image):
        if len(image.shape) == 3:
            logger.warning("'Binarize' 收到彩色图像，可能效果不佳。建议先灰度化。")
            return image
        method = self.params.get('method', 'adaptive')
        if method == 'otsu':
            _, processed = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        else:
            block_size = int(self.params.get('block_size', 11))
            c = int(self.params.get('c', 2))
            method_map = {
                'adaptive': cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                'local': cv2.ADAPTIVE_THRESH_MEAN_C
            }
            processed = cv2.adaptiveThreshold(image, 255, method_map[method], cv2.THRESH_BINARY, block_size, c)
        return processed

class Morphology(ProcessingStep):
    def apply(self, image):
        op_type = self.params.get('op_type', 'close')
        kernel_shape = self.params.get('kernel_shape', 'rect')
        ksize_str = self.params.get('kernel_size', '2,2').split(',')
        kernel_size = (int(ksize_str[0]), int(ksize_str[1]))
        
        shape_map = {'rect': cv2.MORPH_RECT, 'ellipse': cv2.MORPH_ELLIPSE, 'cross': cv2.MORPH_CROSS}
        op_map = {'erode': cv2.MORPH_ERODE, 'dilate': cv2.MORPH_DILATE, 'open': cv2.MORPH_OPEN, 'close': cv2.MORPH_CLOSE}
        
        kernel = cv2.getStructuringElement(shape_map[kernel_shape], kernel_size)
        return cv2.morphologyEx(image, op_map[op_type], kernel)

# 步骤名称到类的映射
STEP_CLASSES = {
    'Grayscale': Grayscale,
    'ContrastEnhancement': ContrastEnhancement,
    'Denoise': Denoise,
    'Sharpen': Sharpen,
    'Binarize': Binarize,
    'Morphology': Morphology,
}


class OCREngine:
    def __init__(self, config: ConfigParser):
        self.config = config
        self.engine_type = config.get('OCR', 'engine')
        self.language = config.get('OCR', 'language')
        self.confidence_threshold = config.getfloat('OCR', 'confidence_threshold')
        
        # 初始化OCR引擎
        logger.info(f"初始化OCR引擎: {self.engine_type}, 语言: {self.language}")
        if self.engine_type == 'paddleocr':
            self.ocr = PaddleOCR(
                use_angle_cls=False,  # 禁用内置文本方向分类，使用自定义方法
                lang=self.language,
                use_gpu=False,  # 根据环境设置
                det_db_thresh=0.2,  # 降低检测阈值以捕获更多文本
                det_db_box_thresh=0.3,  # 降低文本框阈值以捕获更多文本
                det_db_unclip_ratio=2.0,  # 增加文本框扩展比例，合并相邻文本
                rec_batch_num=10,  # 增加识别批处理大小
                use_space_char=True,  # 允许识别空格
                max_text_length=200,  # 增加最大文本长度限制
                drop_score=0.3  # 降低置信度阈值以保留更多潜在的文本
            )
        else:
            raise ValueError(f'不支持的OCR引擎: {self.engine_type}')
    
    def _execute_pipeline(self, img):
        """根据配置文件中的[PIPELINE]部分执行图像处理"""
        if 'PIPELINE' not in self.config or 'steps' not in self.config['PIPELINE']:
            logger.warning("配置文件中未找到 [PIPELINE] 部分，跳过动态处理流程。")
            # 在此可以回退到旧的、硬编码的逻辑，或直接返回
            return img

        step_ids = self.config['PIPELINE']['steps'].split(',')
        if not step_ids or not step_ids[0]:
            logger.info("PIPELINE 为空，不执行任何处理步骤。")
            return img
        
        processed_img = img.copy()
        logger.info(f"开始执行处理管线，共 {len(step_ids)} 个步骤: {step_ids}")

        for step_id in step_ids:
            step_id = step_id.strip()
            if step_id not in self.config:
                logger.error(f"未找到步骤 '{step_id}' 的配置信息，已跳过。")
                continue
            
            step_config = self.config[step_id]
            step_type = step_config.get('type')

            if step_type not in STEP_CLASSES:
                logger.error(f"未知的步骤类型 '{step_type}'，已跳过。")
                continue
            
            try:
                logger.debug(f"应用步骤: {step_type} (ID: {step_id})")
                step_class = STEP_CLASSES[step_type]
                # 将配置字典传递给步骤实例
                step_instance = step_class(params=dict(step_config))
                processed_img = step_instance.apply(processed_img)
            except Exception as e:
                logger.error(f"执行步骤 '{step_type}' (ID: {step_id}) 时出错: {e}", exc_info=True)
                # 选择继续处理或直接返回
                continue
        
        logger.info("处理管线执行完毕。")
        return processed_img


    def preprocess_image(self, img, use_pipeline=False):
        """
        预处理图像。
        - 如果 use_pipeline 为 True，则执行 config.ini 中定义的动态管线。
        - 否则，执行一个标准化的、基础的预处理流程。
        """
        try:
            if img is None:
                logger.error('预处理图像为空')
                return None
            
            if use_pipeline:
                logger.info("正在使用 config.ini 中定义的动态处理管线...")
                processed_img = self._execute_pipeline(img)
            else:
                logger.info("正在使用标准基础预处理流程...")
                # 标准化流程：调整尺寸 + 灰度化
                height, width = img.shape[:2]
                if width > 1800 or height > 1800:
                    scale = min(1800 / width, 1800 / height)
                    img = cv2.resize(img, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
                
                if len(img.shape) == 3:
                    processed_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                else:
                    processed_img = img

            # 最终转回彩色图像以匹配OCR期望的输入格式
            if len(processed_img.shape) == 2:
                return cv2.cvtColor(processed_img, cv2.COLOR_GRAY2BGR)
            else:
                # 如果已经是彩色（例如管线输出），直接返回
                return processed_img
            
        except Exception as e:
            logger.error(f'图像预处理失败: {str(e)}', exc_info=True)
            return img # 在出错时返回原始图像
    
    def is_chinese_char(self, c):
        """判断字符是否为中文字符"""
        return '\u4e00' <= c <= '\u9fff'
        
    def analyze_ocr_quality(self, ocr_result):
        """分析OCR结果质量，返回综合评分"""
        if not ocr_result or not ocr_result[0]:
            return 0
            
        blocks = ocr_result[0]
        
        # 1. 文本块数量
        block_count = len(blocks)
        
        # 2. 总置信度
        total_conf = sum([block[1][1] for block in blocks])
        
        # 3. 平均单词/文本长度
        text_lengths = [len(block[1][0]) for block in blocks]
        avg_text_len = np.mean(text_lengths) if text_lengths else 0
        
        # 4. 中文字符比例
        all_chars = ''.join([block[1][0] for block in blocks])
        chinese_chars = sum(1 for c in all_chars if self.is_chinese_char(c))
        chinese_ratio = chinese_chars / len(all_chars) if all_chars else 0
        
        # 5. 分析文本块的空间排布规律性
        positions = [block[0] for block in blocks]
        heights = []
        widths = []
        
        for pos in positions:
            xs = [p[0] for p in pos]
            ys = [p[1] for p in pos]
            width = max(xs) - min(xs)
            height = max(ys) - min(ys)
            widths.append(width)
            heights.append(height)
        
        # 文本块宽高比的一致性（标准差越小越一致）
        aspect_ratios = [w/h if h > 0 else 0 for w, h in zip(widths, heights)]
        aspect_std = np.std(aspect_ratios) if aspect_ratios else float('inf')
        
        # 6. 高置信度文本块比例
        high_conf_blocks = sum(1 for block in blocks if block[1][1] > 0.9)
        high_conf_ratio = high_conf_blocks / block_count if block_count > 0 else 0
        
        # 7. 长文本块比例（长文本块通常表示完整句子，在正确方向更多）
        long_blocks = sum(1 for text in [block[1][0] for block in blocks] if len(text) > 10)
        long_blocks_ratio = long_blocks / block_count if block_count > 0 else 0
        
        # 计算综合得分，权重可以根据实际情况调整
        block_count_score = block_count * 1.0
        confidence_score = total_conf * 0.5
        text_len_score = avg_text_len * 3.0
        chinese_ratio_score = chinese_ratio * 100.0
        aspect_score = 10.0 / (1.0 + aspect_std) if aspect_std > 0 else 10.0
        high_conf_score = high_conf_ratio * 20.0
        long_blocks_score = long_blocks_ratio * 30.0
        
        total_score = (text_len_score +
                        high_conf_score +
                        long_blocks_score)
        
        # 记录各项得分细节，便于调试
        logger.debug(f"OCR质量分析: 文本块:{block_count}({block_count_score:.1f}), " + 
                    f"置信度:{total_conf:.1f}({confidence_score:.1f}), " + 
                    f"文本长度:{avg_text_len:.1f}({text_len_score:.1f}), " + 
                    f"中文比例:{chinese_ratio:.2f}({chinese_ratio_score:.1f}), " + 
                    f"布局一致性:({aspect_score:.1f}), " +
                    f"高置信度比例:{high_conf_ratio:.2f}({high_conf_score:.1f}), " + 
                    f"长文本比例:{long_blocks_ratio:.2f}({long_blocks_score:.1f}), " +
                    f"总分:{total_score:.1f}")
        
        return total_score

    def has_ecg_keywords(self, ocr_result):
        """检测是否包含心电图相关关键词"""
        if not ocr_result or not ocr_result[0]:
            return False
            
        keywords = ["心电图报告", "心电图"]
        all_text = ' '.join([block[1][0] for block in ocr_result[0]])
        
        return any(keyword in all_text for keyword in keywords)
    
    def recognize_text(self, image_path: str, auto_rotate=True, use_pipeline=False):
        """
        识别图片中的文字。
        新增 use_pipeline 参数以控制预处理流程。
        """
        processing_type = "管线" if use_pipeline else "主"
        logger.debug(f'正在处理图片: {image_path} (模式: {processing_type})')
        
        # 读取图片 - 处理损坏的JPEG数据问题
        try:
            # 先尝试直接读取
            img = cv2.imread(image_path)
            
            # 如果失败，尝试使用IMREAD_IGNORE_ORIENTATION标志
            if img is None:
                logger.warning(f'使用标准方法无法读取图片: {image_path}，尝试替代方法')
                try:
                    # 使用imdecode和IGNORE_ORIENTATION标志
                    img = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), 
                                      cv2.IMREAD_COLOR | cv2.IMREAD_IGNORE_ORIENTATION)
                except Exception as inner_e:
                    logger.error(f'替代图片读取方法失败: {str(inner_e)}')
            
            if img is None:
                raise ValueError(f'无法读取图片: {image_path}')
        except Exception as e:
            logger.error(f'读取图片失败: {str(e)}')
            return []
            
        # 图像预处理
        img = self.preprocess_image(img, use_pipeline=use_pipeline)
        if img is None:
            logger.error(f'图像预处理返回空结果: {image_path}')
            return []
            
        try:
            # 调整OCR引擎参数，针对二次识别优化
            original_conf_threshold = None
            if use_pipeline:
                # 二次处理时降低识别阈值，提高识别率
                original_conf_threshold = self.confidence_threshold
                # 临时降低置信度阈值以获取更多文本块
                self.confidence_threshold = max(0.45, self.confidence_threshold - 0.1)
                logger.debug(f'二次处理时临时调整置信度阈值为: {self.confidence_threshold}')
            
            # 后置旋转策略：由于图像已经被自动旋转了90度，默认需要旋转270度来纠正
            # 旋转图像270度（逆时针旋转90度）
            logger.debug(f'应用后置旋转策略，默认旋转270度来纠正图像方向')
            rotated_img = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
            
            # 对纠正后的图像进行OCR识别
            result_0 = self.ocr.ocr(rotated_img, cls=False)
            
            # 如果启用自动旋转，检查是否需要进一步旋转
            if auto_rotate:
                # 检查是否包含心电图关键词
                if self.has_ecg_keywords(result_0):
                    logger.info(f'检测到心电图报告，需要额外旋转270度')
                    # 心电图报告需要额外旋转180度
                    rotated_img_ecg = cv2.rotate(rotated_img, cv2.ROTATE_90_COUNTERCLOCKWISE)
                    result = self.ocr.ocr(rotated_img_ecg, cls=False)
                else:
                    # 不是心电图报告，保持纠正后的270度旋转方向
                    logger.debug(f'非心电图报告，保持纠正后的方向')
                    result = result_0
            else:
                # 不启用自动旋转，使用纠正后的结果
                result = result_0
            
            # 解析结果
            text_blocks = []
            if result and result[0]:
                for line in result[0]:
                    text, confidence = line[1]
                    position = line[0]
                    if confidence >= self.confidence_threshold:
                        # 提取位置中心点用于排序
                        center_x = sum([p[0] for p in position]) / 4
                        center_y = sum([p[1] for p in position]) / 4
                        
                        text_blocks.append({
                            'text': text,
                            'confidence': confidence,
                            'position': position,
                            'center_x': center_x,
                            'center_y': center_y,
                            'width': max([p[0] for p in position]) - min([p[0] for p in position]),
                            'height': max([p[1] for p in position]) - min([p[1] for p in position]),
                            'is_pipeline': use_pipeline  # 标记是否来自管线处理
                        })
                        logger.debug(f'识别到文本: {text}, 置信度: {confidence:.2f}, 位置: ({center_x:.0f}, {center_y:.0f})')
            
            # 恢复原始置信度阈值
            if use_pipeline and original_conf_threshold is not None:
                self.confidence_threshold = original_conf_threshold
                logger.debug(f'恢复置信度阈值为: {self.confidence_threshold}')
            
            logger.info(f'图片识别完成，共找到{len(text_blocks)}个文本块: {image_path}')
            return text_blocks
            
        except Exception as e:
            logger.error(f'OCR识别失败: {str(e)}')
            return []
    
    def batch_recognize(self, image_paths: list):
        """批量识别多张图片的文字"""
        logger.info(f'开始批量识别 {len(image_paths)} 张图片')
        results = {}
        for img_path in image_paths:
            try:
                results[img_path] = self.recognize_text(img_path)
            except Exception as e:
                logger.error(f'处理图片 {img_path} 时出错: {str(e)}')
                continue
        return results 