# OCR-PDF内容抽取整理系统 - 功能更新

## 最新更新：强制二次识别覆盖功能

### 功能说明
在本次更新中，我们新增了一个配置选项，允许系统强制使用二次识别（管线处理）的结果覆盖主识别结果。这对于一些特殊场景非常有用，例如：

1. 当主识别结果质量不佳，而二次识别（使用特定图像处理管线）结果更准确时
2. 需要提取更多文本内容，即使置信度稍低
3. 测试不同图像处理管线的效果

### 使用方法

#### 方式一：修改配置文件
在`config.ini`文件中，找到`[OCR]`部分，将`force_pipeline_override`设置为`true`：

```ini
[OCR]
engine = paddleocr
language = ch
confidence_threshold = 0.5
force_pipeline_override = true   # 默认为false
```

#### 方式二：使用示例脚本
我们提供了一个示例脚本`示例-强制二次识别覆盖.py`，它会动态设置强制覆盖选项并运行处理流程。

### 技术实现说明

1. **二次识别参数优化**
   - 在使用管线处理时，系统会自动降低识别阈值，以获取更多可能的文本
   - 文本块会被标记为`is_pipeline=True`，便于后续处理

2. **数据合并策略**
   - 当`force_pipeline_override = false`时：
     - 对超声页面的特定字段（室间隔厚度、舒张末期前后径、后壁厚度）强制覆盖
     - 其他字段只在主结果为空时填充
   - 当`force_pipeline_override = true`时：
     - 对超声页面的特定字段仍然强制覆盖
     - 所有非空的二次识别结果都会覆盖主结果

### 日志输出
系统会记录所有字段的覆盖情况，便于调试和查看：
- `字段覆盖 (超声)`: 超声页面特定字段的覆盖
- `字段覆盖 (强制)`: 启用强制覆盖模式时的覆盖
- `字段填充 (空值)`: 仅填充主结果中的空值

### 注意事项
1. 强制覆盖模式可能会导致一些准确度较高的主结果被覆盖，请根据实际需求选择是否启用
2. 建议先对少量样本进行测试，比较两种模式的处理效果
3. 可以通过查看日志了解具体的字段覆盖情况

## 心超数据自动修正功能

### 功能说明
为了提高心超数据的准确性，我们增加了针对心超数据的智能修正机制，可以自动处理常见的OCR错误并补全缺失数据：

1. **OCR错误修正**
   - 当室间隔厚度或后壁厚度识别为"1"时，自动修正为"10"（医学上这些值通常不会是1mm）
   - 当室间隔厚度或后壁厚度大于50时，自动去掉个位数（如"120"修正为"12"）

2. **数据补全**
   - 当后壁厚度缺失但室间隔厚度存在时，自动将室间隔厚度的值赋给后壁厚度

### 技术实现
- 系统会在完成OCR识别和数据合并后，对结果进行智能后处理
- 修正过程会记录在日志中，便于跟踪数据变化
- 此功能无需配置，默认启用

### 日志示例
```
修正室间隔厚度: 1 -> 10 (OCR识别错误修正)
修正后壁厚度: 120 -> 12 (去掉个位)
修正后壁厚度: 空值 -> 11 (基于室间隔厚度)
``` 