#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的配置效果
验证崩溃修复和识别率提升
"""

import os
import cv2
import numpy as np
from field_extractor import FieldExtractor
import configparser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_roi_images_safely():
    """安全加载ROI图片"""
    roi_dir = "roi_images"
    roi_images = []
    
    target_files = [
        "current_image_出生日期_digit_large_box.png",
        "current_image_血压_digit_large_box.png", 
        "current_image_心率_digit_large_box.png",
        "current_image_体重_digit_large_box.png",
        "current_image_身高_digit_large_box.png",
        "current_image_手机号码_digit_large_box.png"
    ]
    
    for filename in target_files:
        filepath = os.path.join(roi_dir, filename)
        
        try:
            with open(filepath, 'rb') as f:
                image_data = f.read()
            
            image_array = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
            
            if image is not None:
                roi_images.append((filename, image))
                print(f"✅ 加载ROI图片: {filename}")
            else:
                print(f"❌ 无法解码图片: {filename}")
                
        except Exception as e:
            print(f"❌ 加载图片失败 {filename}: {str(e)}")
    
    return roi_images

def test_optimized_field_extractor():
    """测试优化后的字段提取器"""
    print("🔍 测试优化后的字段提取器...")
    
    # 加载配置
    config = configparser.ConfigParser()
    config.read('config_new.ini', encoding='utf-8')
    
    # 创建字段提取器
    ocr_config = {
        'confidence_threshold': 0.1,
        'use_gpu': False,
        'use_angle_cls': True,
        'lang': 'ch'
    }
    
    try:
        extractor = FieldExtractor("field_calibration3.json", ocr_config, config)
        print("✅ 字段提取器创建成功")
    except Exception as e:
        print(f"❌ 字段提取器创建失败: {str(e)}")
        return {}
    
    # 加载测试图片
    roi_images = load_roi_images_safely()
    
    if not roi_images:
        print("❌ 没有找到测试图片")
        return {}
    
    results = {}
    
    for filename, image in roi_images:
        field_name = filename.split('_')[2]  # 提取字段名
        
        # 模拟区域（使用整个图片）
        h, w = image.shape[:2]
        region = [0, 0, w, h]
        
        print(f"\n🔍 测试字段: {field_name}")
        
        try:
            # 使用大框数字提取方法
            result = extractor.extract_digit_large_box_field(image, region, field_name)
            
            text = result.get("text", "")
            confidence = result.get("confidence", 0)
            is_empty = result.get("is_empty", True)
            
            results[field_name] = {
                "text": text,
                "confidence": confidence,
                "success": not is_empty and len(text) > 0,
                "error": None
            }
            
            status = "✅" if not is_empty and len(text) > 0 else "⚠️"
            print(f"  {status} 结果: '{text}' (置信度: {confidence:.3f})")
            
        except Exception as e:
            results[field_name] = {
                "text": "",
                "confidence": 0,
                "success": False,
                "error": str(e)
            }
            print(f"  ❌ 提取失败: {str(e)}")
    
    return results

def compare_with_previous_results():
    """与之前的结果比较"""
    print("\n📊 与优化前结果比较...")
    
    # 优化前的结果（从测试日志中提取）
    previous_results = {
        "出生日期": {"success": False, "error": "list index out of range"},
        "血压": {"success": True, "text": "/", "confidence": 0.633},
        "心率": {"success": False, "text": "", "confidence": 0.0},
        "体重": {"success": True, "text": "1711619.1", "confidence": 0.826},
        "身高": {"success": True, "text": "11618", "confidence": 0.681},
        "手机号码": {"success": True, "text": "13923282491131", "confidence": 0.360}
    }
    
    # 测试优化后的结果
    current_results = test_optimized_field_extractor()
    
    print("\n对比结果:")
    print("="*60)
    print(f"{'字段':<10} {'优化前':<20} {'优化后':<20} {'状态'}")
    print("-"*60)
    
    improvements = 0
    total_fields = len(previous_results)
    
    for field_name in previous_results.keys():
        prev = previous_results[field_name]
        curr = current_results.get(field_name, {})
        
        prev_status = "✅" if prev.get("success", False) else "❌"
        curr_status = "✅" if curr.get("success", False) else "❌"
        
        prev_text = prev.get("text", prev.get("error", "失败"))[:15]
        curr_text = curr.get("text", curr.get("error", "失败"))[:15]
        
        # 判断是否有改善
        improvement = ""
        if not prev.get("success", False) and curr.get("success", False):
            improvement = "🎉 修复"
            improvements += 1
        elif prev.get("success", False) and curr.get("success", False):
            prev_conf = prev.get("confidence", 0)
            curr_conf = curr.get("confidence", 0)
            if curr_conf > prev_conf + 0.1:  # 置信度提升超过0.1
                improvement = "📈 提升"
                improvements += 1
            elif curr_conf < prev_conf - 0.1:  # 置信度下降超过0.1
                improvement = "📉 下降"
            else:
                improvement = "➡️ 相似"
        elif prev.get("success", False) and not curr.get("success", False):
            improvement = "⚠️ 退化"
        else:
            improvement = "➡️ 无变化"
        
        print(f"{field_name:<10} {prev_status} {prev_text:<15} {curr_status} {curr_text:<15} {improvement}")
    
    print("-"*60)
    print(f"总体改善: {improvements}/{total_fields} 个字段 ({improvements/total_fields*100:.1f}%)")
    
    return current_results

def test_crash_fix():
    """专门测试崩溃修复"""
    print("\n🔧 测试崩溃修复...")
    
    # 加载配置
    config = configparser.ConfigParser()
    config.read('config_new.ini', encoding='utf-8')
    
    ocr_config = {
        'confidence_threshold': 0.1,
        'use_gpu': False,
        'use_angle_cls': True,
        'lang': 'ch'
    }
    
    try:
        extractor = FieldExtractor("field_calibration3.json", ocr_config, config)
        
        # 创建一个可能导致崩溃的测试图片（空白图片）
        test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
        region = [0, 0, 100, 100]
        
        print("测试空白图片处理...")
        result = extractor.extract_digit_large_box_field(test_image, region, "测试字段")
        
        print(f"✅ 空白图片处理成功: {result}")
        
        # 测试出生日期字段（之前崩溃的字段）
        roi_images = load_roi_images_safely()
        birth_date_image = None
        
        for filename, image in roi_images:
            if "出生日期" in filename:
                birth_date_image = image
                break
        
        if birth_date_image is not None:
            print("测试出生日期字段...")
            h, w = birth_date_image.shape[:2]
            region = [0, 0, w, h]
            
            result = extractor.extract_digit_large_box_field(birth_date_image, region, "出生日期")
            print(f"✅ 出生日期字段处理成功: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 崩溃修复测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 优化配置验证测试")
    print("="*80)
    
    # 测试崩溃修复
    crash_fixed = test_crash_fix()
    
    # 比较优化效果
    current_results = compare_with_previous_results()
    
    # 统计结果
    successful_fields = sum(1 for r in current_results.values() if r.get("success", False))
    total_fields = len(current_results)
    success_rate = successful_fields / total_fields if total_fields > 0 else 0
    
    avg_confidence = np.mean([r.get("confidence", 0) for r in current_results.values() if r.get("success", False)])
    
    print("\n" + "="*80)
    print("📊 优化效果总结")
    print("="*80)
    print(f"✅ 崩溃修复: {'成功' if crash_fixed else '失败'}")
    print(f"📈 成功率: {success_rate:.1%} ({successful_fields}/{total_fields})")
    print(f"🎯 平均置信度: {avg_confidence:.3f}")
    
    if success_rate >= 0.8:
        print("\n🎉 优化效果显著！")
        print("💡 建议:")
        print("  1. 配置已应用，可以正式使用")
        print("  2. 继续监控识别效果")
        print("  3. 如有需要可进一步微调参数")
    elif success_rate >= 0.6:
        print("\n✅ 优化效果良好")
        print("💡 建议继续使用并观察效果")
    else:
        print("\n⚠️ 优化效果有限")
        print("💡 建议进一步调整参数或检查图片质量")
    
    print("="*80)

if __name__ == "__main__":
    main()
