#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simple_echo_test():
    """简单的心超OCR测试"""
    
    try:
        logger.info('开始简单心超OCR测试...')
        
        from configparser import ConfigParser
        
        # 读取配置
        config = ConfigParser()
        config.read('config.ini', encoding='utf-8')
        logger.info('配置文件读取成功')
        
        # 检查测试PDF是否存在
        test_pdf = '../files/7.28体检/202412130173_弄庆新_45250119800812256X.pdf'
        if not os.path.exists(test_pdf):
            logger.error(f'测试PDF不存在: {test_pdf}')
            return
        
        logger.info(f'测试PDF存在: {test_pdf}')
        
        # 尝试导入心超OCR引擎
        logger.info('尝试导入心超OCR引擎...')
        from echo_ocr_engine import EchoOCREngine
        logger.info('心超OCR引擎导入成功')
        
        # 初始化心超OCR引擎
        logger.info('初始化心超OCR引擎...')
        echo_ocr_engine = EchoOCREngine(config)
        logger.info('心超OCR引擎初始化成功')
        
        # 检查OCR引擎是否正常（延迟初始化）
        logger.info('OCR引擎采用延迟初始化，将在需要时自动初始化')
        logger.info('OCR引擎检查通过')

        # 测试从弄庆新PDF中提取图片并进行心超OCR
        logger.info('开始从PDF中提取图片进行心超测试...')

        import fitz
        doc = fitz.open(test_pdf)

        # 创建临时目录
        temp_dir = './temp_test_images'
        os.makedirs(temp_dir, exist_ok=True)

        # 检查第19页（已知包含心超图片）
        if len(doc) >= 19:
            page = doc[18]  # 第19页，索引为18
            image_list = page.get_images()
            logger.info(f'第19页包含{len(image_list)}张图片')

            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)

                    if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图
                        temp_img_path = os.path.join(temp_dir, f'page_19_img_{img_index}.png')

                        # 保存图片
                        if pix.alpha:
                            pix = fitz.Pixmap(fitz.csRGB, pix)

                        img_data = pix.tobytes("png")
                        with open(temp_img_path, "wb") as f:
                            f.write(img_data)

                        logger.info(f'保存图片: {temp_img_path}')

                        # 快速检查是否是心超图片
                        is_echo = echo_ocr_engine.quick_check_echo_image(temp_img_path)
                        logger.info(f'心超图片检查结果: {is_echo}')

                        if is_echo:
                            # 提取心超信息
                            echo_info = echo_ocr_engine.extract_echo_info(temp_img_path)
                            logger.info(f'提取信息结果: {len(echo_info)} 个字段')
                            for key, value in echo_info.items():
                                logger.info(f'  {key}: {value[:100]}...')
                            break

                    pix = None

                except Exception as e:
                    logger.error(f'处理图片出错: {str(e)}')

        doc.close()

        # 清理临时文件
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

        logger.info('简单测试完成')
        
    except Exception as e:
        logger.error(f'测试过程出错: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_echo_test()
