#  
整体类似于 OCR-图片整理进文件夹，但是是从PDF文件中提取信息，且不需要图片归类等操作
可以一定程度上参考OCR-图片整理进文件夹的实现方法，也可以另起炉灶，建议先整体看一遍图片整理进文件夹的实现思路。

一份PDF为固定的1个患者的信息，PDF中包含多张图片
最终目标为将目标目录下所有PDF文件中的信息提取出来，填充到一个Excel表格中。其中EXCEL表的表头已经给出。
将识别信息匹配到表中的字段，然后填充到对应的单元格中。
识别前的图片也需要贴在单元格最后一栏内


建议的执行步骤：
1.从目录中for遍历PDF文件：
    从PDF文件中提取信息，将提取的信息保留到专门的数据结构中。
2.将数据结构中的信息填充到表格中。

其中，第二步需要严格的匹配规范，可能需要较多调试。这里需要设计较多的模式匹配/正则匹配及逻辑。

建议第二步的注意点：
1.某些信息对于页面来说可能是唯一的（例如，第 1 页上的超声心动图详细信息）。
2.某些信息可能会重复（例如，患者姓名）。确定事实来源或采用第一次出现。
3.使用目标架构中的所有键初始化字典，并将值设置为没有或空字符串。
4.提取信息后，填充此字典。
5.创建单独的函数来提取不同类别的信息（例如，extract_patient_info,extract_echo_data,extract_lab_value). 这使得代码更简洁，更易于调试和维护。
6.对于实验室测试，在 PDF 中的名称（例如，“肌钙蛋白 I”、“B 型钠尿肽前体”）和目标图式名称（例如，“高敏肌钙蛋白 I”、“pro-BNP”）之间创建映射，在执行开始前我会给你部分映射规则。 此映射还可以保存每个项目的正则表达式模式。
7.OCR代码方面 可以先“OCR-图片整理进文件夹”的基础实现，后续更新再参考GITHUB提供的微信内置的OCR实现方式等。
8.还需要考虑多行值，结论或注释可以跨越多行，单位/特殊字符可能会引起



附：需要识别的表头：
    心磁ID
    缺血程度
    综合标签
    入排
    人工读图
    心磁日期
    心磁质量
    姓名
    住院号
    性别
    年龄
    身高
    体重
    吸烟
    饮酒
    冠心病
    高血压
    HP等级
    糖尿病
    高脂血症
    心梗史
    心衰
    NYHA心功能分级
    主诉症状
    出院诊断
    是否造影
    造影时间
    造影标签
    LM
    LAD
    TIMI
    LAD分支
    LCX
    TIMI
    LCX分支
    RCA
    TIMI
    RCA分支
    备注
    既往介入
    详情
    本次介入
    介入情况
    心超结论
    %EF
    心电标签
    心电图结论
    血红蛋白
    糖化血红蛋白
    血糖
    甘油三酯
    总胆固醇
    低密度脂蛋白
    高密度脂蛋白
    载脂蛋白A1
    载脂蛋白B
    脂蛋白(a)
    BNP
    pro-BNP
    高敏肌钙蛋白T
    高敏肌钙蛋白I
    肌红蛋白
    CK-MB(门诊)
    CK-MB(急诊)


