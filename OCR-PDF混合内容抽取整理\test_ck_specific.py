#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import fitz
import re
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ck_specific():
    """测试特定PDF的肌酸激酶提取"""
    
    # 测试弄庆新的PDF
    pdf_path = '../files/7.28体检/202412130173_弄庆新_45250119800812256X.pdf'
    
    if not os.path.exists(pdf_path):
        logger.error(f'PDF文件不存在: {pdf_path}')
        return
    
    logger.info(f'测试PDF: {os.path.basename(pdf_path)}')
    
    try:
        doc = fitz.open(pdf_path)
        
        # 提取所有文本
        all_text = ""
        for page_num in range(len(doc)):
            page = doc[page_num]
            page_text = page.get_text()
            all_text += page_text + "\n"
        
        doc.close()
        
        logger.info(f'提取文本长度: {len(all_text)}字符')
        
        # 搜索所有包含"肌酸"或"CK"的行
        lines = all_text.split('\n')
        ck_lines = []
        for line in lines:
            if '肌酸' in line or re.search(r'\bCK\b', line):
                ck_lines.append(line.strip())
        
        print("\n" + "=" * 80)
        print("包含肌酸激酶相关的文本行:")
        print("=" * 80)
        for i, line in enumerate(ck_lines, 1):
            print(f"{i:2d}. {line}")
        
        # 测试当前的正则表达式
        print("\n" + "=" * 80)
        print("正则表达式测试结果:")
        print("=" * 80)
        
        # 肌酸激酶 - 使用修复后的正则表达式
        ck_patterns = [
            r'肌酸激酶\(CK\)\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',  # 肌酸激酶(CK) 115 35-175 U/L
            r'肌酸激酶\(CK\)[:\s]*(\d+(?:\.\d+)?)\s*U/L',  # 肌酸激酶(CK):115 U/L
            r'肌酸激酶\(CK\)[:\s]*(\d+(?:\.\d+)?)',  # 肌酸激酶(CK):115
            r'肌酸激酶[^同工酶]*[:\s]+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',  # 排除同工酶，匹配完整格式
            r'肌酸激酶[^同工酶]*[:\s]*(\d+(?:\.\d+)?)\s*U/L',  # 排除同工酶
            r'(?<!同工)肌酸激酶[:\s]*(\d+(?:\.\d+)?)',  # 使用负向后查找排除同工酶
            r'CK[^-M\s]*\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',  # CK 115 35-175 U/L，排除CK-MB
            r'CK[^-M]*[:\s]*(\d+(?:\.\d+)?)\s*U/L'  # CK:115 U/L，排除CK-MB
        ]
        
        for i, pattern in enumerate(ck_patterns, 1):
            match = re.search(pattern, all_text, re.IGNORECASE)
            if match:
                try:
                    value = float(match.group(1))
                    if 10 <= value <= 1000:  # CK合理范围检查
                        print(f"模式{i}: 匹配成功 - {match.group(1)} (值: {value})")
                        # 显示匹配的上下文
                        start = max(0, match.start() - 50)
                        end = min(len(all_text), match.end() + 50)
                        context = all_text[start:end].replace('\n', ' ')
                        print(f"    上下文: ...{context}...")
                        break
                    else:
                        print(f"模式{i}: 匹配但数值超出范围 - {match.group(1)} (值: {value})")
                except ValueError:
                    print(f"模式{i}: 匹配但数值转换失败 - {match.group(1)}")
            else:
                print(f"模式{i}: 无匹配")
        
        # 肌酸激酶同工酶
        print("\n肌酸激酶同工酶(CK-MB):")
        ckmb_patterns = [
            r'肌酸激酶同工酶\(CK-MB\)\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',
            r'肌酸激酶同工酶[:\s]*(\d+(?:\.\d+)?)',
            r'肌酸激酶同功酶[:\s]*(\d+(?:\.\d+)?)',
            r'CK-MB[:\s]*(\d+(?:\.\d+)?)',
            r'CKMB[:\s]*(\d+(?:\.\d+)?)',
            # 新增模式
            r'CK-MB\s+(\d+(?:\.\d+)?)\s+U/L',
            r'肌酸激酶同工酶.*?(\d+(?:\.\d+)?)\s*U/L',
            r'肌酸激酶同工酶\(CK-MB\)[:\s]*(\d+(?:\.\d+)?)'
        ]
        
        for i, pattern in enumerate(ckmb_patterns, 1):
            match = re.search(pattern, all_text, re.IGNORECASE)
            if match:
                try:
                    value = float(match.group(1))
                    print(f"模式{i}: 匹配成功 - {match.group(1)} (值: {value})")
                    # 显示匹配的上下文
                    start = max(0, match.start() - 50)
                    end = min(len(all_text), match.end() + 50)
                    context = all_text[start:end].replace('\n', ' ')
                    print(f"    上下文: ...{context}...")
                    break
                except ValueError:
                    print(f"模式{i}: 匹配但数值转换失败 - {match.group(1)}")
            else:
                print(f"模式{i}: 无匹配")
        
        print("=" * 80)
        
    except Exception as e:
        logger.error(f'处理PDF出错: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ck_specific()
