import os
import logging
import openpyxl
from openpyxl.styles import Alignment, PatternFill, Font
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image as XlImage
from PIL import Image
import io
from configparser import ConfigParser
from typing import Dict, List, Any

logger = logging.getLogger('PDFExtractor')

class ExcelHandler:
    def __init__(self, config: ConfigParser):
        self.config = config
        self.output_file = config.get('DEFAULT', 'output_excel_path')
        self.insert_images = config.getboolean('EXCEL', 'insert_images', fallback=False)
        self.header_fields = self._get_header_fields()
        self.data_records = []
        
    def _get_header_fields(self) -> List[str]:
        """获取表头字段列表"""
        # 从需求中提取的表头字段
        return [
            # 心磁相关
            '心磁ID', '缺血程度', '综合标签', '入排', '人工读图', '心磁日期', '心磁质量',
            
            # 基本信息
            '姓名', '住院号', '性别', '年龄', '身高', '体重', '吸烟', '饮酒',
            
            # 疾病信息
            '冠心病', '高血压', 'HP等级', '糖尿病', '高脂血症', '心梗史', '心衰',
            'NYHA心功能分级', '主诉症状', '出院诊断', 
            
            # 造影相关
            '是否造影', '造影时间', '造影标签',
            'LM', 'LAD', 'TIMI', 'LAD分支', 'LCX', 'TIMI', 'LCX分支',
            'RCA', 'TIMI', 'RCA分支', '备注', 
            
            # 介入相关
            '既往介入', '详情', '本次介入', '介入情况', 
            
            # 心超数据
            '心超结论', '超声所见', '室间隔厚度', '舒张末期前后径', '后壁厚度', '%EF', 
            
            # 心电图
            '心电标签', '心电图结论', 
            
            # 化验结果
            '血红蛋白', '糖化血红蛋白', '血糖', '甘油三酯', '总胆固醇', 
            '低密度脂蛋白胆固醇', '高密度脂蛋白胆固醇',
            '载脂蛋白A1', '载脂蛋白B', '脂蛋白(a)', 
            'BNP', 'pro-BNP', '高敏肌钙蛋白T', '高敏肌钙蛋白I', '肌红蛋白', 
            '肌酸激酶', '肌酸激酶同功酶', 'CK-MB(门诊)', 'CK-MB(急诊)',
            
            # 文件信息
            'PDF文件', '图片路径',
            
            # 心电图图片
            'ECG图片'
        ]
    
    def add_record(self, record_data: Dict[str, Any]):
        """添加一条记录数据"""
        logger.info(f'添加记录: {record_data.get("姓名", "未知")}')
        
        # 检查是否有相同姓名和住院号的记录
        name = record_data.get('姓名', '')
        hospital_id = record_data.get('住院号', '')
        
        if name and hospital_id:
            for existing_record in self.data_records:
                if (existing_record.get('姓名') == name and 
                    existing_record.get('住院号') == hospital_id):
                    # 合并记录，保留更完整的信息
                    logger.info(f'发现相同患者记录，合并信息: {name}')
                    self._merge_records(existing_record, record_data)
                    return
        
        # 如果没有找到匹配的记录，添加新记录
        self.data_records.append(record_data)
        logger.info(f'添加新记录: {name}')
    
    def _merge_records(self, existing_record: Dict[str, Any], new_record: Dict[str, Any]):
        """合并两条记录，保留更完整的信息"""
        for field in self.header_fields:
            if field not in existing_record or not existing_record[field]:
                if field in new_record and new_record[field]:
                    existing_record[field] = new_record[field]
            # 对于数值型字段，取较大值
            elif field in new_record and new_record[field]:
                try:
                    existing_value = float(existing_record[field])
                    new_value = float(new_record[field])
                    if new_value > existing_value:
                        existing_record[field] = new_record[field]
                except (ValueError, TypeError):
                    # 非数值型，保持原样
                    pass
        
        # 特殊处理图片路径，合并而不是覆盖
        if 'PDF文件' in new_record and new_record['PDF文件']:
            if 'PDF文件' in existing_record and existing_record['PDF文件']:
                if new_record['PDF文件'] not in existing_record['PDF文件']:
                    existing_record['PDF文件'] += f", {new_record['PDF文件']}"
            else:
                existing_record['PDF文件'] = new_record['PDF文件']
        
        if '图片路径' in new_record and new_record['图片路径']:
            if '图片路径' in existing_record and existing_record['图片路径']:
                existing_record['图片路径'] += f", {new_record['图片路径']}"
            else:
                existing_record['图片路径'] = new_record['图片路径']

        # 合并ECG图片路径
        if 'ECG图片路径' in new_record and new_record['ECG图片路径']:
            if 'ECG图片路径' in existing_record and existing_record['ECG图片路径']:
                if new_record['ECG图片路径'] not in existing_record['ECG图片路径']:
                     existing_record['ECG图片路径'] += f", {new_record['ECG图片路径']}"
            else:
                existing_record['ECG图片路径'] = new_record['ECG图片路径']
    
    def _prepare_image_for_excel(self, img_path, max_width=200, max_height=200, rotate=0):
        """准备适合Excel单元格的图片, 支持旋转"""
        try:
            img = Image.open(img_path)

            # 如果需要，旋转图片
            if rotate != 0:
                img = img.rotate(rotate, expand=True)

            # 调整图片大小以适应单元格
            width, height = img.size
            if width > max_width or height > max_height:
                scale = min(max_width / width, max_height / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                img = img.resize((new_width, new_height), Image.LANCZOS)
            
            # 将PIL图像转换为openpyxl可用的格式
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG')
            img_bytes.seek(0)
            return XlImage(img_bytes)
        except Exception as e:
            logger.error(f"处理图片失败 {img_path}: {str(e)}")
            return None
    
    def save_output(self):
        """保存结果到Excel文件"""
        if not self.data_records:
            logger.warning('没有数据记录，不创建Excel文件')
            return
        
        logger.info(f'准备保存Excel文件，共{len(self.data_records)}条记录')
        
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(self.output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 创建工作簿和工作表
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = '医疗数据'
            
            # 写入表头
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            
            for col_idx, field in enumerate(self.header_fields, start=1):
                cell = ws.cell(row=1, column=col_idx, value=field)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # 写入数据
            for row_idx, record in enumerate(self.data_records, start=2):
                for col_idx, field in enumerate(self.header_fields, start=1):
                    value = record.get(field, '')
                    ws.cell(row=row_idx, column=col_idx, value=value)
                
                # 如果配置启用了图片插入，插入图片到相应单元格
                if self.insert_images and '图片路径' in record and record['图片路径']:
                    img_paths = record['图片路径'].split(',')
                    # 找到图片路径列的索引
                    img_col_idx = self.header_fields.index('图片路径') + 1
                    
                    # 只处理第一张图片作为示例
                    if img_paths and os.path.exists(img_paths[0].strip()):
                        img_obj = self._prepare_image_for_excel(img_paths[0].strip())
                        if img_obj:
                            # 调整行高以适应图片
                            ws.row_dimensions[row_idx].height = 150
                            # 插入图片到单元格
                            cell = ws.cell(row=row_idx, column=img_col_idx)
                            ws.add_image(img_obj, f'{get_column_letter(img_col_idx)}{row_idx}')

                # 如果有心电图图片，插入到ECG图片列
                if 'ECG图片路径' in record and record['ECG图片路径']:
                    ecg_img_paths = record['ECG图片路径'].split(',')
                    ecg_col_idx = self.header_fields.index('ECG图片') + 1
                    
                    if ecg_img_paths and os.path.exists(ecg_img_paths[0].strip()):
                        # 旋转180度并使用更高清的尺寸
                        ecg_img_obj = self._prepare_image_for_excel(
                            ecg_img_paths[0].strip(), 
                            max_width=400, 
                            max_height=300, 
                            rotate=180
                        )
                        if ecg_img_obj:
                            # 确保行高足够
                            current_height = ws.row_dimensions[row_idx].height
                            if current_height is None or current_height < 230: # 增加高度以适应图片
                                ws.row_dimensions[row_idx].height = 230
                            
                            cell = ws.cell(row=row_idx, column=ecg_col_idx)
                            ws.add_image(ecg_img_obj, f'{get_column_letter(ecg_col_idx)}{row_idx}')
            
            # 设置列宽
            for col in range(1, len(self.header_fields) + 1):
                column_letter = get_column_letter(col)
                # 根据内容设置合适的列宽
                max_length = 0
                for row in range(1, len(self.data_records) + 2):  # +2 是因为包括表头和从1开始计数
                    cell_value = ws.cell(row=row, column=col).value
                    if cell_value:
                        max_length = max(max_length, len(str(cell_value)))
                
                # 设置列宽，最小10，最大50
                adjusted_width = min(max(10, max_length + 2), 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 保存文件
            try:
                wb.save(self.output_file)
                logger.info(f'Excel文件已保存: {self.output_file}')
            except PermissionError:
                # 如果权限错误，尝试使用备用文件名
                backup_file = f"{os.path.splitext(self.output_file)[0]}_backup.xlsx"
                wb.save(backup_file)
                logger.warning(f'无法保存到原始路径，已保存备份文件: {backup_file}')
            
        except Exception as e:
            logger.error(f'保存Excel文件失败: {str(e)}')
            raise 