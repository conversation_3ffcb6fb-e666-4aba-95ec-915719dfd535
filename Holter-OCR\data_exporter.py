"""
Data Export Module for Holter PDF Reports
Exports extracted data to Excel format with validation and formatting
"""

import os
import logging
import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows


class HolterDataExporter:
    """Exports Holter data to Excel with proper formatting and validation"""
    
    def __init__(self, template_file: str = None):
        """
        Initialize the data exporter
        
        Args:
            template_file: Path to Excel template file
        """
        self.template_file = template_file
        self.logger = logging.getLogger(__name__)
        
        # Define expected columns in correct order including report conclusion
        self.expected_columns = [
            '姓名', '总心搏数', 'RR间期＞2.0s次数', '最长RR间期(s)', '平均心率（bpm）',
            '最快心率（bpm）', '最慢心率（bpm）', '窦性心搏总数', '窦性平均心率（bpm）',
            '窦性最快心率（bpm）', '窦性最慢心率（bpm）', '房早总数', '单发', '成对',
            '二联律', '三联律', '房早未下传', '室早总数', '单发.1', '成对.1', '二联律.1',
            '三联律.1', '室早未下传', '心率变异性', 'SDNN', 'SDANN', 'SDNNIndex',
            'rMSSD', 'pNN50', '三角指数', '报告结论'
        ]
        
        # Data validation rules
        self.validation_rules = {
            'numeric_fields': [
                '总心搏数', 'RR间期＞2.0s次数', '平均心率（bpm）', '最快心率（bpm）',
                '最慢心率（bpm）', '窦性心搏总数', '窦性平均心率（bpm）', '窦性最快心率（bpm）',
                '窦性最慢心率（bpm）', '房早总数', '单发', '成对', '二联律', '三联律',
                '房早未下传', '室早总数', '单发.1', '成对.1', '二联律.1', '三联律.1',
                '室早未下传', 'SDNN', 'SDANN', 'SDNNIndex', 'rMSSD'
            ],
            'float_fields': [
                '最长RR间期(s)', 'pNN50', '三角指数'
            ],
            'string_fields': [
                '姓名', '心率变异性', '报告结论'
            ]
        }
    
    def validate_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Validate and clean the extracted data
        
        Args:
            data: List of extracted data dictionaries
            
        Returns:
            List of validated data dictionaries
        """
        validated_data = []
        
        for i, record in enumerate(data):
            validated_record = {}
            
            # Validate each field
            for column in self.expected_columns:
                value = record.get(column)
                
                if column in self.validation_rules['numeric_fields']:
                    validated_record[column] = self._validate_numeric(value, column, i)
                elif column in self.validation_rules['float_fields']:
                    validated_record[column] = self._validate_float(value, column, i)
                elif column in self.validation_rules['string_fields']:
                    validated_record[column] = self._validate_string(value, column, i)
                else:
                    validated_record[column] = value
            
            validated_data.append(validated_record)
        
        return validated_data
    
    def _validate_numeric(self, value: Any, field_name: str, record_index: int) -> Optional[int]:
        """Validate numeric field"""
        if value is None:
            return None
        
        try:
            if isinstance(value, (int, float)):
                return int(value)
            elif isinstance(value, str):
                # Remove any non-numeric characters except decimal point
                cleaned = ''.join(c for c in value if c.isdigit() or c == '.')
                if cleaned:
                    return int(float(cleaned))
            return None
        except (ValueError, TypeError):
            self.logger.warning(f"Invalid numeric value for {field_name} in record {record_index}: {value}")
            return None
    
    def _validate_float(self, value: Any, field_name: str, record_index: int) -> Optional[float]:
        """Validate float field"""
        if value is None:
            return None
        
        try:
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                # Remove any non-numeric characters except decimal point
                cleaned = ''.join(c for c in value if c.isdigit() or c == '.')
                if cleaned:
                    return float(cleaned)
            return None
        except (ValueError, TypeError):
            self.logger.warning(f"Invalid float value for {field_name} in record {record_index}: {value}")
            return None
    
    def _validate_string(self, value: Any, field_name: str, record_index: int) -> Optional[str]:
        """Validate string field"""
        if value is None:
            return None
        
        try:
            return str(value).strip() if value else None
        except Exception:
            self.logger.warning(f"Invalid string value for {field_name} in record {record_index}: {value}")
            return None
    
    def create_dataframe(self, data: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        Create a pandas DataFrame from the data
        
        Args:
            data: List of data dictionaries
            
        Returns:
            Formatted DataFrame
        """
        # Validate data first
        validated_data = self.validate_data(data)
        
        # Create DataFrame with expected columns
        df = pd.DataFrame(validated_data, columns=self.expected_columns)
        
        # Fill missing columns with None
        for col in self.expected_columns:
            if col not in df.columns:
                df[col] = None
        
        # Reorder columns
        df = df[self.expected_columns]
        
        self.logger.info(f"Created DataFrame with {len(df)} rows and {len(df.columns)} columns")
        return df
    
    def export_to_excel(self, data: List[Dict[str, Any]], output_file: str, 
                       add_formatting: bool = True) -> str:
        """
        Export data to Excel file
        
        Args:
            data: List of data dictionaries
            output_file: Output Excel file path
            add_formatting: Whether to add Excel formatting
            
        Returns:
            Path to created Excel file
        """
        try:
            # Create DataFrame
            df = self.create_dataframe(data)
            
            if add_formatting:
                # Create formatted Excel file
                self._create_formatted_excel(df, output_file)
            else:
                # Simple Excel export
                df.to_excel(output_file, index=False, engine='openpyxl')
            
            self.logger.info(f"Data exported to: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error exporting to Excel: {e}")
            raise
    
    def _create_formatted_excel(self, df: pd.DataFrame, output_file: str):
        """Create Excel file with formatting"""
        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Holter数据"
        
        # Add title
        ws.merge_cells('A1:AD1')
        title_cell = ws['A1']
        title_cell.value = f"安医附院Holter数据提取结果 - {datetime.now().strftime('%Y-%m-%d')}"
        title_cell.font = Font(size=16, bold=True)
        title_cell.alignment = Alignment(horizontal='center')
        title_cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        title_cell.font = Font(size=16, bold=True, color="FFFFFF")
        
        # Add headers
        for col_idx, column in enumerate(df.columns, 1):
            cell = ws.cell(row=2, column=col_idx)
            cell.value = column
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center', wrap_text=True)
            cell.fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
        
        # Add data
        for row_idx, row in enumerate(dataframe_to_rows(df, index=False, header=False), 3):
            for col_idx, value in enumerate(row, 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                cell.value = value
                
                # Format numeric cells
                if isinstance(value, (int, float)) and value is not None:
                    cell.alignment = Alignment(horizontal='right')
                else:
                    cell.alignment = Alignment(horizontal='center')
        
        # Add borders
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in ws.iter_rows(min_row=2, max_row=len(df)+2, 
                               min_col=1, max_col=len(df.columns)):
            for cell in row:
                cell.border = thin_border
        
        # Adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Save workbook
        wb.save(output_file)
    
    def export_summary_report(self, data: List[Dict[str, Any]], output_file: str):
        """
        Export a summary report with statistics
        
        Args:
            data: List of data dictionaries
            output_file: Output file path
        """
        df = self.create_dataframe(data)
        
        # Create summary statistics
        summary_stats = {}
        
        for column in df.columns:
            if column in self.validation_rules['numeric_fields'] + self.validation_rules['float_fields']:
                numeric_data = pd.to_numeric(df[column], errors='coerce')
                summary_stats[column] = {
                    'count': numeric_data.count(),
                    'mean': numeric_data.mean(),
                    'std': numeric_data.std(),
                    'min': numeric_data.min(),
                    'max': numeric_data.max(),
                    'missing': numeric_data.isna().sum()
                }
        
        # Create summary DataFrame
        summary_df = pd.DataFrame(summary_stats).T
        
        # Export to Excel with multiple sheets
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='原始数据', index=False)
            summary_df.to_excel(writer, sheet_name='统计摘要')
        
        self.logger.info(f"Summary report exported to: {output_file}")


if __name__ == "__main__":
    # Test the data exporter
    logging.basicConfig(level=logging.INFO)
    
    # Sample data for testing
    sample_data = [
        {
            '姓名': '张三',
            '总心搏数': 92787,
            'RR间期＞2.0s次数': 0,
            '最长RR间期(s)': 1.669,
            '平均心率（bpm）': 65,
            '最快心率（bpm）': 157,
            '最慢心率（bpm）': 47,
            'SDNN': 141,
            'pNN50': 1.82,
            '三角指数': 47.38
        }
    ]
    
    exporter = HolterDataExporter()
    exporter.export_to_excel(sample_data, "test_export.xlsx")
    exporter.export_summary_report(sample_data, "test_summary.xlsx")
