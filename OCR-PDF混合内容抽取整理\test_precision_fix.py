#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试心超和心电图识别精度修复
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from echo_ocr_engine import EchoOCREngine
from pdf_processor import MixedContentPDFProcessor
import configparser

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_echo_precision():
    """测试心超识别精度"""
    logger.info("=== 测试心超识别精度 ===")
    
    # 加载配置
    config = configparser.ConfigParser()
    config.read('config.ini', encoding='utf-8')
    
    # 初始化心超OCR引擎
    echo_engine = EchoOCREngine(config)
    
    # 测试几个已知的PDF文件
    test_files = [
        "202412130173_弄庆新_45250119800812256X.pdf",
        "202403140123_张东_44082219660422041X.pdf",
        "202404221696_叶海燕_440924197402183422.pdf"
    ]
    
    pdf_dir = Path("../files/7.28体检")
    
    for pdf_file in test_files:
        pdf_path = pdf_dir / pdf_file
        if pdf_path.exists():
            logger.info(f"\n--- 测试文件: {pdf_file} ---")
            
            # 初始化PDF处理器 (需要ExcelHandler)
            from excel_handler import ExcelHandler
            excel_handler = ExcelHandler(config)
            processor = MixedContentPDFProcessor(config, echo_engine, excel_handler)
            
            # 提取并分析
            try:
                record = processor._process_single_pdf(str(pdf_path))
                
                # 检查心超字段
                echo_seen = record.get('超声心动图_超声所见', '')
                echo_suggest = record.get('超声心动图_超声提示', '')
                
                logger.info(f"心超提取结果:")
                logger.info(f"  超声所见: {'✅' if echo_seen else '❌'} ({len(echo_seen)} 字符)")
                if echo_seen:
                    logger.info(f"    内容预览: {echo_seen[:100]}...")
                    
                logger.info(f"  超声提示: {'✅' if echo_suggest else '❌'} ({len(echo_suggest)} 字符)")
                if echo_suggest:
                    logger.info(f"    内容预览: {echo_suggest[:100]}...")
                    
                # 检查心电图字段
                ecg_image = record.get('心电图图片', '')
                logger.info(f"心电图图片: {'✅' if ecg_image else '❌'}")
                if ecg_image:
                    logger.info(f"  图片路径: {ecg_image}")
                    
            except Exception as e:
                logger.error(f"处理文件失败: {e}")
        else:
            logger.warning(f"文件不存在: {pdf_path}")

if __name__ == "__main__":
    test_echo_precision()
