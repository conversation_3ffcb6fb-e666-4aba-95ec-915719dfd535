#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel处理器模块
负责将提取的结构化数据导出到Excel文件，支持低置信度结果标红
"""

import os
import logging
import pandas as pd
from typing import List, Dict, Optional
from configparser import ConfigParser
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill, Font
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.hyperlink import Hyperlink

logger = logging.getLogger('ClinicalExtractor')


class ExcelHandler:
    """Excel处理器"""
    
    def __init__(self, config: ConfigParser):
        self.config = config
        self.output_path = config.get('DEFAULT', 'output_excel_path')
        self.mark_low_confidence = config.getboolean('OUTPUT', 'mark_low_confidence')
        self.low_confidence_threshold = config.getfloat('TEXT_PROCESSING', 'low_confidence_threshold')
        self.include_debug_info = config.getboolean('OUTPUT', 'include_debug_info')
        
        # 解析低置信度标记颜色
        color_str = config.get('OUTPUT', 'low_confidence_color')
        try:
            r, g, b = [int(x.strip()) for x in color_str.split(',')]
            self.low_confidence_color = f"{r:02X}{g:02X}{b:02X}"
        except:
            self.low_confidence_color = "FF0000"  # 默认红色
        
        # 数据存储
        self.records = []
        self.low_confidence_cells = []  # 存储低置信度单元格位置
        
        # 定义列顺序
        self.column_order = [
            'PDF文件', '页码', '图片路径',
            '姓名', '手机号码', '出生日期', '性别',
            '血压', '心率', '体重', '身高',
            '临床症状', '症状描述',
            '高血压病史', '血脂异常病史', '糖尿病',
            '心肌梗死病史', '卒中史', '外周血管病',
            '用药_抗血小板药物', '用药_抗血小板药物类型',
            '用药_他汀类', '用药_他汀类药物类型', '用药_他汀类其他药物',
            '用药_降压药物', '用药_降糖药物', '用药_胰岛素'
        ]
        
        if self.include_debug_info:
            self.column_order.extend(['调试信息', '处理时间'])
    
    def add_record(self, data: Dict[str, str], confidence_info: Optional[Dict[str, float]] = None):
        """添加一条记录"""
        try:
            # 确保所有列都存在
            record = {}
            for col in self.column_order:
                record[col] = data.get(col, '')
            
            # 添加记录
            self.records.append(record)
            
            # 记录低置信度字段
            if confidence_info and self.mark_low_confidence:
                row_index = len(self.records)  # Excel行号（从1开始，加上表头）
                for field, confidence in confidence_info.items():
                    if confidence < self.low_confidence_threshold and field in self.column_order:
                        col_index = self.column_order.index(field) + 1  # Excel列号（从1开始）
                        self.low_confidence_cells.append((row_index + 1, col_index))  # +1 for header
            
            logger.debug(f'添加记录: {len([k for k, v in record.items() if v])} 个有效字段')
            
        except Exception as e:
            logger.error(f'添加记录失败: {str(e)}', exc_info=True)
    
    def save(self):
        """保存Excel文件"""
        try:
            if not self.records:
                logger.warning('没有数据需要保存')
                return
            
            # 创建输出目录
            output_dir = os.path.dirname(self.output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 创建DataFrame
            df = pd.DataFrame(self.records, columns=self.column_order)
            
            # 保存基础Excel文件
            with pd.ExcelWriter(self.output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='临床信息表', index=False)
            
            # 如果需要标红低置信度结果或添加超链接，使用openpyxl进行格式化
            if self.mark_low_confidence and self.low_confidence_cells:
                self._format_low_confidence_cells()

            # 添加图片路径超链接
            self._add_image_hyperlinks()
            
            logger.info(f'Excel文件已保存: {self.output_path}')
            logger.info(f'共保存 {len(self.records)} 条记录')
            
            if self.low_confidence_cells:
                logger.info(f'标记了 {len(self.low_confidence_cells)} 个低置信度单元格')
            
        except Exception as e:
            logger.error(f'保存Excel文件失败: {str(e)}', exc_info=True)
            raise
    
    def _format_low_confidence_cells(self):
        """格式化低置信度单元格"""
        try:
            # 加载工作簿
            wb = load_workbook(self.output_path)
            ws = wb['临床信息表']
            
            # 创建红色填充样式
            red_fill = PatternFill(start_color=self.low_confidence_color, 
                                 end_color=self.low_confidence_color, 
                                 fill_type='solid')
            
            # 应用格式到低置信度单元格
            for row, col in self.low_confidence_cells:
                cell = ws.cell(row=row, column=col)
                cell.fill = red_fill
                logger.debug(f'标记低置信度单元格: 行{row}, 列{col}')
            
            # 保存工作簿
            wb.save(self.output_path)
            wb.close()
            
        except Exception as e:
            logger.error(f'格式化低置信度单元格失败: {str(e)}', exc_info=True)

    def _add_image_hyperlinks(self):
        """为图片路径添加超链接"""
        try:
            # 加载工作簿
            wb = load_workbook(self.output_path)
            ws = wb['临床信息表']

            # 找到图片路径列
            image_path_col = None
            for col_idx, col_name in enumerate(self.column_order, 1):
                if col_name == '图片路径':
                    image_path_col = col_idx
                    break

            if image_path_col is None:
                logger.warning('未找到图片路径列')
                return

            # 为每行的图片路径添加超链接
            for row_idx in range(2, len(self.records) + 2):  # 从第2行开始（跳过表头）
                cell = ws.cell(row=row_idx, column=image_path_col)
                image_path = cell.value

                if image_path and os.path.exists(image_path):
                    # 转换为绝对路径
                    abs_path = os.path.abspath(image_path)

                    # 创建超链接
                    cell.hyperlink = f"file:///{abs_path.replace(os.sep, '/')}"
                    cell.style = "Hyperlink"

                    # 设置显示文本为文件名
                    cell.value = os.path.basename(image_path)

                    logger.debug(f'为第{row_idx}行添加图片超链接: {abs_path}')
                elif image_path:
                    logger.warning(f'图片文件不存在: {image_path}')

            # 保存工作簿
            wb.save(self.output_path)
            wb.close()

            logger.info('图片超链接添加完成')

        except Exception as e:
            logger.error(f'添加图片超链接失败: {str(e)}', exc_info=True)
    
    def get_statistics(self) -> Dict[str, int]:
        """获取统计信息"""
        if not self.records:
            return {}
        
        stats = {
            '总记录数': len(self.records),
            '有效字段总数': 0,
            '空字段总数': 0
        }
        
        # 统计各字段的填充情况
        field_stats = {}
        for col in self.column_order:
            if col in ['PDF文件', '页码', '图片路径']:  # 跳过元数据字段
                continue
            
            filled_count = sum(1 for record in self.records if record.get(col, '').strip())
            field_stats[col] = {
                '填充数量': filled_count,
                '填充率': f"{filled_count/len(self.records)*100:.1f}%"
            }
            
            stats['有效字段总数'] += filled_count
            stats['空字段总数'] += len(self.records) - filled_count
        
        stats['字段统计'] = field_stats
        
        if self.low_confidence_cells:
            stats['低置信度单元格数'] = len(self.low_confidence_cells)
        
        return stats
    
    def export_summary(self, summary_path: Optional[str] = None):
        """导出处理摘要"""
        try:
            if not summary_path:
                base_path = os.path.splitext(self.output_path)[0]
                summary_path = f"{base_path}_summary.txt"
            
            stats = self.get_statistics()
            
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write("临床信息表PDF批量提取系统 - 处理摘要\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"总记录数: {stats.get('总记录数', 0)}\n")
                f.write(f"有效字段总数: {stats.get('有效字段总数', 0)}\n")
                f.write(f"空字段总数: {stats.get('空字段总数', 0)}\n")
                
                if '低置信度单元格数' in stats:
                    f.write(f"低置信度单元格数: {stats['低置信度单元格数']}\n")
                
                f.write("\n字段填充统计:\n")
                f.write("-" * 30 + "\n")
                
                field_stats = stats.get('字段统计', {})
                for field, info in field_stats.items():
                    f.write(f"{field}: {info['填充数量']} ({info['填充率']})\n")
            
            logger.info(f'处理摘要已保存: {summary_path}')
            
        except Exception as e:
            logger.error(f'导出处理摘要失败: {str(e)}', exc_info=True)
    
    def clear_records(self):
        """清空记录"""
        self.records.clear()
        self.low_confidence_cells.clear()
        logger.info('已清空所有记录')

    def create_excel_from_extractions(self, extraction_results: List[Dict], output_file: str):
        """
        从提取结果创建Excel文件 - 支持新的字段结构

        Args:
            extraction_results: 提取结果列表
            output_file: 输出Excel文件路径
        """
        try:
            import openpyxl

            # 创建工作簿
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "医疗表单数据"

            # 定义表头
            headers = [
                "文件名", "图像质量", "姓名", "手机号码", "出生日期", "性别", "血压", "心率",
                "体重", "身高", "临床症状", "临床症状具体",
                "吸烟", "饮酒", "心肌梗死病史", "卒中史", "高血压病史",
                "血脂异常病史", "糖尿病", "外周血管病",
                "抗血小板药物", "阿司匹林", "氯吡格雷", "替格瑞洛",
                "他汀类", "阿托伐他汀", "瑞舒伐他汀", "他汀类其他",
                "降压药物", "降糖药物", "胰岛素",
                "验证警告", "处理状态"
            ]

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col, value=header)
                cell.font = openpyxl.styles.Font(bold=True)
                cell.fill = openpyxl.styles.PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            # 写入数据
            for row_idx, result in enumerate(extraction_results, 2):
                filename = os.path.basename(result.get("image_path", ""))
                extracted_fields = result.get("extracted_fields", {})
                combined_fields = result.get("combined_fields", {})
                image_quality = result.get("image_quality", {})

                # 图像质量状态
                quality_status = "正常"
                if image_quality.get("should_highlight_row", False):
                    quality_status = "质量警告"

                # 基本信息 - 使用组合字段优先
                birth_date = combined_fields.get("出生日期", {}).get("value", "")
                weight = combined_fields.get("体重", {}).get("value", "")
                height = combined_fields.get("身高", {}).get("value", "")
                blood_pressure = combined_fields.get("血压", {}).get("value", "")

                row_data = [
                    filename,
                    quality_status,
                    extracted_fields.get("姓名", {}).get("text", ""),
                    extracted_fields.get("手机号码", {}).get("text", ""),
                    birth_date,
                    self._get_gender_from_extractions(extracted_fields),
                    blood_pressure,
                    extracted_fields.get("心率", {}).get("text", ""),
                    weight,
                    height,
                    self._get_clinical_symptoms_from_extractions(extracted_fields),
                    extracted_fields.get("临床症状具体", {}).get("text", ""),
                ]

                # 危险因素及既往病史
                risk_factors = [
                    self._get_checkbox_value_from_extractions(extracted_fields, ["吸烟否", "吸烟已戒烟", "吸烟当前吸烟"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["饮酒否", "饮酒已戒酒", "饮酒当前饮酒"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["心肌梗死病史否", "心肌梗死病史是"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["卒中史否", "卒中史是", "卒中史脑梗死", "卒中史脑出血", "卒中史不详"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["高血压病史否", "高血压病史是"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["血脂异常病史否", "血脂异常病史是"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["糖尿病否", "糖尿病是"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["外周血管病否", "外周血管病是"]),
                ]

                # 用药情况
                medications = [
                    self._get_checkbox_value_from_extractions(extracted_fields, ["抗血小板药物否", "抗血小板药物是"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["阿司匹林"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["氯吡格雷"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["替格瑞洛"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["他汀类否", "他汀类是"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["阿托伐他汀"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["瑞舒伐他汀"]),
                    extracted_fields.get("他汀类其他药物名称", {}).get("text", ""),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["降压药物否", "降压药物是"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["降糖药物否", "降糖药物是"]),
                    self._get_checkbox_value_from_extractions(extracted_fields, ["胰岛素否", "胰岛素是"]),
                ]

                # 收集验证警告
                validation_warnings = []
                summary = result.get("summary", {})

                # 检查组合字段警告
                for field_name, field_info in combined_fields.items():
                    warnings = field_info.get("validation", {}).get("warnings", [])
                    if warnings:
                        validation_warnings.extend([f"{field_name}: {w}" for w in warnings])

                # 检查图像质量警告
                if image_quality.get("warnings"):
                    validation_warnings.extend([f"图像: {w}" for w in image_quality["warnings"]])

                warning_text = "; ".join(validation_warnings) if validation_warnings else ""

                processing_status = "完成"
                if summary.get("failed_extractions", 0) > 0:
                    processing_status = "部分失败"

                # 合并所有数据
                full_row_data = row_data + risk_factors + medications + [warning_text, processing_status]

                # 写入行数据
                for col, value in enumerate(full_row_data, 1):
                    cell = worksheet.cell(row=row_idx, column=col, value=value)

                    # 根据数据质量设置样式
                    if quality_status == "质量警告" or validation_warnings:
                        cell.fill = openpyxl.styles.PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
                    elif processing_status == "部分失败":
                        cell.fill = openpyxl.styles.PatternFill(start_color="FFFFCC", end_color="FFFFCC", fill_type="solid")

            # 调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # 保存文件
            workbook.save(output_file)
            logger.info(f"Excel文件已保存: {output_file}")

        except Exception as e:
            logger.error(f"创建Excel文件失败: {str(e)}")
            raise

    def _get_gender_from_extractions(self, extracted_fields: Dict) -> str:
        """从提取字段中获取性别信息"""
        if extracted_fields.get("性别男", {}).get("checked", False):
            return "男"
        elif extracted_fields.get("性别女", {}).get("checked", False):
            return "女"
        return ""

    def _get_clinical_symptoms_from_extractions(self, extracted_fields: Dict) -> str:
        """从提取字段中获取临床症状信息"""
        if extracted_fields.get("临床症状无", {}).get("checked", False):
            return "无"
        elif extracted_fields.get("临床症状有", {}).get("checked", False):
            return "有"
        return ""

    def _get_checkbox_value_from_extractions(self, extracted_fields: Dict, field_names: List[str]) -> str:
        """从提取字段中获取复选框值"""
        for field_name in field_names:
            if extracted_fields.get(field_name, {}).get("checked", False):
                # 简化字段名显示
                if "否" in field_name:
                    return "否"
                elif "是" in field_name:
                    return "是"
                elif "已戒" in field_name:
                    return "已戒"
                elif "当前" in field_name:
                    return "当前"
                else:
                    return field_name.replace("吸烟", "").replace("饮酒", "").replace("卒中史", "")
        return ""
