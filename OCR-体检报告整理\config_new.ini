[PATHS]
# 路径配置
pdf_input_dir = input_pdfs
temp_images_dir = temp_images
aligned_images_dir = aligned_images
output_dir = output
logs_dir = logs

[PDF_PROCESSING]
# PDF文件处理配置
dpi = 300
image_format = png
# 是否处理所有PDF文件（true）还是单个文件（false）
process_all_pdfs = true

[IMAGE_ALIGNMENT]
# 图像对齐配置
target_width = 2480
target_height = 3507
# 表格检测参数
gaussian_blur_kernel = 5,5
canny_low = 50
canny_high = 150
contour_min_area = 50000
# 调试图像控制
save_debug_images = false
# 保存裁剪后的标框图片（用于检查识别效果）
save_roi_images = true
# 在Excel中嵌入ROI图片（仅在大框识别模式下生效）
embed_roi_in_excel = true

[FIELD_CALIBRATION]
# 字段标定配置
calibration_config_file = field_calibration4.json
# 如果标定文件不存在，是否自动启动交互式标定工具
auto_launch_calibrator = true

[OCR_SETTINGS]
# OCR识别配置
use_gpu = True
language = ch
confidence_threshold = 0.05
use_angle_cls = true
rec_batch_num = 5
# 手写字识别优化
enable_handwriting_mode = true
enable_digit_optimization = true
# 数字识别模式配置
digit_recognition_mode = large_box  # single_box: 单个数字框, large_box: 大框识别
# 复选框检测配置
checkbox_detection_method = visual_only
enable_checkbox_comparison = true

# 标准OCR引擎配置（用于文本字段）
[OCR_STANDARD]
use_gpu = True
det_db_thresh = 0.2
det_db_box_thresh = 0.3
det_db_unclip_ratio = 2.0
rec_batch_num = 5
max_text_length = 50
drop_score = 0.1

# 数字专用OCR引擎配置（用于数字框字段）- 优化后的高精度模式
[OCR_DIGIT]
use_gpu = True
det_db_thresh = 0.05
det_db_box_thresh = 0.1
det_db_unclip_ratio = 2.5
rec_batch_num = 5
max_text_length = 50
drop_score = 0.0


# 数字识别前处理配置
enable_morphology_processing = False
erosion_kernel_size = 2
dilation_kernel_size = 1

# 手写体OCR引擎配置（用于手写字段如姓名）
[OCR_HANDWRITING]
use_gpu = True
language = ch
det_db_thresh = 0.15
det_db_box_thresh = 0.25
det_db_unclip_ratio = 2.5
rec_batch_num = 5
max_text_length = 35
drop_score = 0.08

[FIELD_EXTRACTION]
# 字段提取配置
# 是否在提取前进行图像对齐
align_before_extraction = False
# 数据验证配置
enable_data_validation = False
# 图像质量检查
enable_quality_check = true
min_image_width = 1000
min_image_height = 1000

[OUTPUT]
# 输出配置
output_format = xlsx
excel_filename = medical_form_results.xlsx
# 是否保存详细的JSON结果
save_json_results = true
json_filename = extraction_results.json
# 是否生成文本报告
generate_text_report = true
report_filename = extraction_report.txt

[LOGGING]
# 日志配置
log_level = INFO
log_file = logs/medical_extractor.log
enable_console_log = true
# 是否保存处理统计
save_processing_stats = true
