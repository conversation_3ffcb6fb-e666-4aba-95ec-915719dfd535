import logging
import os
import sys
from configparser import ConfigParser
from ocr_engine import OCREngine
from pdf_processor import PDFProcessor
from excel_handler import ExcelHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('PDFExtractor')

def main():
    # 加载配置文件
    config = ConfigParser()
    config.read('config.ini', encoding='utf-8')
    
    # 修改配置，启用强制覆盖模式
    config.set('OCR', 'force_pipeline_override', 'true')
    
    logger.info("已启用强制二次识别覆盖模式")
    logger.info("force_pipeline_override = %s", config.get('OCR', 'force_pipeline_override'))
    
    # 创建OCR引擎
    ocr_engine = OCREngine(config)
    
    # 创建Excel处理器
    excel_handler = ExcelHandler(config)
    
    # 创建PDF处理器
    pdf_processor = PDFProcessor(config, ocr_engine, excel_handler)
    
    try:
        # 处理所有PDF
        pdf_processor.process_pdfs()
        
        # 保存Excel
        excel_handler.save()
        logger.info("处理完成，结果已保存到Excel文件")
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main() 