import os
import logging
import openpyxl
from openpyxl.styles import Alignment, PatternFill, Font
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image as XlImage
from PIL import Image
import io
from configparser import ConfigParser
from typing import Dict, List, Any

logger = logging.getLogger('PDFExtractor')

class ExcelHandler:
    def __init__(self, config: ConfigParser):
        self.config = config
        self.output_file = config.get('DEFAULT', 'output_excel_path')
        self.insert_images = config.getboolean('EXCEL', 'insert_images', fallback=False)
        self.header_fields = self._get_header_fields()
        self.data_records = []
        
    def _get_header_fields(self) -> List[str]:
        """获取表头字段列表 - 根据新需求定义"""
        return [
            # 基本信息
            '姓名', '性别', '年龄', '总检日期', '吸烟', '饮酒', '既往史', '现病史', '家族史',

            # 体征信息
            '血压_收缩压', '血压_舒张压', '身高', '体重', 'BMI',

            # 血液检查
            '血色素', '总胆固醇(TC)', '甘油三酯(TG)',
            '高密度脂蛋白胆固醇(HDL-C)', '低密度脂蛋白胆固醇(LDL-C)',
            '糖化血红蛋白', '肌酸激酶(CK)', '肌酸激酶同工酶(CK-MB)',

            # 检查结果
            '冠状动脉CTA(增强)诊断小结',
            '超声心动图_超声所见', '超声心动图_超声提示',
            '心电图诊断小结', '心电图图片',

            # 文件信息
            'PDF文件名', '处理状态', '提取时间'
        ]
    
    def add_record(self, record_data: Dict[str, Any]):
        """添加一条记录数据，实现智能合并逻辑"""
        name = record_data.get('姓名', '未知')
        logger.info(f'添加记录: {name}')

        # 检查是否有相同姓名的记录
        if name and name != '未知':
            for existing_record in self.data_records:
                if existing_record.get('姓名') == name:
                    # 检查是否有冲突数据
                    conflicts = self._check_conflicts(existing_record, record_data)
                    if conflicts:
                        logger.warning(f'发现数据冲突，为{name}创建单独记录: {conflicts}')
                        # 有冲突时创建新记录
                        self.data_records.append(record_data)
                        return
                    else:
                        # 无冲突时合并记录
                        logger.info(f'发现相同患者记录，合并信息: {name}')
                        self._merge_records(existing_record, record_data)
                        return

        # 如果没有找到匹配的记录，添加新记录
        self.data_records.append(record_data)
        logger.info(f'添加新记录: {name}')

    def _check_conflicts(self, existing_record: Dict[str, Any], new_record: Dict[str, Any]) -> List[str]:
        """检查两条记录是否有冲突的数据"""
        conflicts = []
        # 关键字段，如果这些字段有不同值则认为是冲突
        key_fields = ['性别', '年龄', '总检日期', '身高', '体重']

        for field in key_fields:
            existing_val = existing_record.get(field)
            new_val = new_record.get(field)

            if existing_val and new_val and existing_val != new_val:
                # 对于年龄，允许1岁的差异（可能是不同年份体检）
                if field == '年龄':
                    try:
                        if abs(float(existing_val) - float(new_val)) <= 1:
                            continue
                    except (ValueError, TypeError):
                        pass
                conflicts.append(f'{field}: {existing_val} vs {new_val}')

        return conflicts

    def _merge_records(self, existing_record: Dict[str, Any], new_record: Dict[str, Any]):
        """合并两条记录，保留更完整的信息"""
        for field in self.header_fields:
            existing_val = existing_record.get(field)
            new_val = new_record.get(field)

            # 如果现有记录没有该字段值，使用新记录的值
            if not existing_val and new_val:
                existing_record[field] = new_val
            # 如果新记录有更详细的信息，则更新
            elif new_val and existing_val:
                # 对于文本字段，选择更长的内容
                if isinstance(new_val, str) and isinstance(existing_val, str):
                    if len(new_val) > len(existing_val):
                        existing_record[field] = new_val
                # 对于数值字段，保留非零值
                elif field in ['身高', '体重', 'BMI', '血压_收缩压', '血压_舒张压']:
                    try:
                        if float(new_val) > 0 and (not existing_val or float(existing_val) == 0):
                            existing_record[field] = new_val
                    except (ValueError, TypeError):
                        pass
    
    def save_output(self):
        """保存Excel文件"""
        if not self.data_records:
            logger.warning('没有数据记录，跳过Excel文件保存')
            return
            
        logger.info(f'开始保存Excel文件: {self.output_file}')
        
        # 确保输出目录存在
        output_dir = os.path.dirname(self.output_file)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "体检数据"
        
        # 写入表头
        for col_idx, field in enumerate(self.header_fields, 1):
            cell = ws.cell(row=1, column=col_idx, value=field)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # 写入数据
        for row_idx, record in enumerate(self.data_records, 2):
            for col_idx, field in enumerate(self.header_fields, 1):
                value = record.get(field, '')

                # 处理心电图图片字段
                if field == '心电图图片' and value and isinstance(value, str) and os.path.exists(value):
                    try:
                        # 嵌入图片
                        self._embed_image(ws, row_idx, col_idx, value)
                        # 设置单元格值为图片路径（可选）
                        ws.cell(row=row_idx, column=col_idx, value='[心电图图片]')
                    except Exception as e:
                        logger.error(f'嵌入心电图图片失败: {e}')
                        ws.cell(row=row_idx, column=col_idx, value=f'图片错误: {str(e)}')
                else:
                    ws.cell(row=row_idx, column=col_idx, value=value)
        
        # 调整列宽
        for col_idx, field in enumerate(self.header_fields, 1):
            column_letter = get_column_letter(col_idx)
            ws.column_dimensions[column_letter].width = min(max(len(field) + 2, 10), 30)
        
        # 保存文件
        wb.save(self.output_file)
        logger.info(f'Excel文件保存完成: {self.output_file}')
        logger.info(f'共保存 {len(self.data_records)} 条记录')

    def _embed_image(self, worksheet, row, col, image_path):
        """在Excel单元格中嵌入图片"""
        try:
            # 打开并调整图片大小
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 调整图片大小以适应单元格 - 提高清晰度
                max_width, max_height = 300, 225  # 增加尺寸提高清晰度

                # 获取原始尺寸
                orig_width, orig_height = img.size

                # 计算缩放比例，保持宽高比
                width_ratio = max_width / orig_width
                height_ratio = max_height / orig_height
                scale_ratio = min(width_ratio, height_ratio)

                # 计算新尺寸
                new_width = int(orig_width * scale_ratio)
                new_height = int(orig_height * scale_ratio)

                # 使用高质量重采样方法
                try:
                    # 使用LANCZOS算法进行高质量缩放
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                except AttributeError:
                    # 兼容旧版本PIL
                    img = img.resize((new_width, new_height), Image.LANCZOS)

                # 保存到内存，使用高质量PNG格式
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='PNG', optimize=False, compress_level=1)  # 最小压缩保持质量
                img_buffer.seek(0)

                # 创建Excel图片对象
                xl_img = XlImage(img_buffer)

                # 设置图片位置
                cell_address = f'{get_column_letter(col)}{row}'
                xl_img.anchor = cell_address

                # 添加到工作表
                worksheet.add_image(xl_img)

                # 调整行高以适应更大的图片
                worksheet.row_dimensions[row].height = new_height * 0.75  # 根据实际图片高度调整

                logger.info(f'成功嵌入图片到单元格 {cell_address}')

        except Exception as e:
            logger.error(f'嵌入图片失败 {image_path}: {e}')
            raise
