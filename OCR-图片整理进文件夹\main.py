import os
import sys
import logging
from configparser import ConfigParser
from datetime import datetime

from image_processor import ImageProcessor
from excel_handler import ExcelHandler
from ocr_engine import OCREngine


def main():
    # 读取配置文件
    config = ConfigParser()
    config.read('config/config.ini', encoding='utf-8')
    
    # 初始化日志记录器
    log_file = config.get('Logging', 'log_file_path')
    log_level = config.get('Logging', 'log_level')
    
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    logging.basicConfig(
        filename=log_file,
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    logger = logging.getLogger('MedicalHelper')
    logger.info('程序启动，初始化各模块...')
    
    # 初始化各模块
    ocr_engine = OCREngine(config)
    excel_handler = ExcelHandler(config)
    image_processor = ImageProcessor(config, ocr_engine, excel_handler)
    logger.info('模块初始化完成')
    
    # 处理图片
    logger.info('开始处理图片...')
    image_processor.process_images()
    logger.info('图片处理完成')
    
    # 保存Excel
    logger.info('开始保存Excel文件...')
    excel_handler.save_output()
    logger.info('Excel文件保存完成')


if __name__ == '__main__':
    main()

    # test
    # import numpy as np
    # img_path ="D://data//临床信息图片//1//IMG_20250211_111207.jpg"
    # img_path = ".//1.jpg"
    # img = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
    # img = cv2.imread(img_path)
