# OCR-PDF混合内容抽取整理

一个专业的医疗体检PDF文档智能处理系统，支持文字直接提取和图片OCR识别的混合内容处理。

## 🎯 项目概述

本项目是针对医疗体检PDF文档的智能信息提取系统，能够：
- **智能识别内容类型**：自动区分文字内容和图片内容
- **混合处理策略**：对大部分字段使用直接文字提取，对特定医疗图片（如心超、心电图）使用OCR识别
- **专业医疗优化**：针对医疗体检报告的格式和术语进行专门优化
- **高效批量处理**：支持批量处理整个文件夹的PDF文档

## ✨ 核心功能特性

### 📄 文字内容提取
- **基础信息**：姓名、性别、年龄、身份证号、总检日期
- **体格检查**：身高、体重、血压、心率
- **血液指标**：血色素、总胆固醇、甘油三酯、血糖等
- **生活习惯**：吸烟、饮酒等
- **病史信息**：既往史、现病史、家族史

### 🖼️ 图片OCR识别
- **心超报告**：自动识别心脏超声图片，提取"超声所见"和"超声提示"
- **心电图处理**：识别心电图图片并嵌入到Excel单元格中
- **智能触发**：只对包含相关关键词的PDF进行图片处理，提高效率

### 📊 数据处理与输出
- **记录合并**：同名人员的多次体检记录智能合并，冲突时分别记录
- **Excel输出**：生成格式化的Excel文件，支持图片嵌入
- **数据验证**：自动检测和标记数据冲突

## 🏗️ 技术架构

### 核心模块
```
OCR-PDF混合内容抽取整理/
├── main.py                 # 主程序入口
├── config.ini             # 配置文件
├── pdf_processor.py       # PDF处理核心模块
├── echo_ocr_engine.py     # 心超OCR专用引擎
├── excel_handler.py       # Excel输出处理模块
├── output/               # 输出目录
├── logs/                 # 日志目录
└── temp_images/          # 临时图片目录
```

### 技术栈
- **PDF处理**：PyMuPDF (fitz) - 高效的PDF文本提取和图片提取
- **OCR引擎**：PaddleOCR - 专业的中文OCR识别
- **Excel处理**：OpenPyXL - 支持图片嵌入的Excel操作
- **图像处理**：OpenCV + NumPy - 图像预处理和中文路径支持
- **配置管理**：ConfigParser - 灵活的参数配置

## 📋 提取字段详情

### 文字直接提取字段
- **基础信息**：姓名、性别、年龄、身份证号、总检日期
- **体格检查**：身高、体重、血压（收缩压、舒张压）
- **生活习惯**：吸烟、饮酒
- **病史信息**：既往史、现病史、家族史
- **血液检查指标**：
  - 血色素 (Hb)
  - 总胆固醇 (TC)
  - 甘油三酯 (TG)
  - 高密度脂蛋白胆固醇 (HDL-C)
  - 低密度脂蛋白胆固醇 (LDL-C)
  - 糖化血红蛋白 (HbA1c)
  - 肌酸激酶 (CK)
  - 肌酸激酶同工酶 (CK-MB)
- **检查结果**：
  - 冠状动脉CTA(增强)诊断小结
  - 心电图诊断小结

### 图片OCR提取字段
- **超声心动图**：
  - 超声所见（详细的心脏结构和功能描述）
  - 超声提示（诊断结论和建议）
- **心电图图片**：直接嵌入Excel单元格

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 依赖包：`pip install PyMuPDF paddlepaddle paddleocr opencv-python openpyxl numpy`

### 使用步骤

1. **准备PDF文件**
   ```bash
   # 将PDF文件放入指定目录（默认：../files/7.28体检/）
   ```

2. **配置参数**
   ```bash
   # 编辑 config.ini 文件，设置输入输出路径
   ```

3. **运行处理**
   ```bash
   python main.py
   ```

4. **查看结果**
   ```bash
   # 查看 output/medical_data_mixed.xlsx 文件
   ```

## ⚙️ 配置说明

### config.ini 主要配置项
```ini
[DEFAULT]
# 输入PDF文件路径
input_pdf_path = ../files/7.28体检

# 输出Excel文件路径
output_excel_path = ./output/medical_data_mixed.xlsx

# 日志文件路径
log_file_path = ./logs/pdf_extractor_mixed.log

# OCR相关配置
ocr_use_gpu = false
ocr_lang = ch
```

## 🔄 处理流程

### 智能处理策略
1. **PDF解析**：使用PyMuPDF高效解析PDF文件结构
2. **文字提取**：直接提取所有页面的文字内容
3. **字段匹配**：使用优化的正则表达式匹配目标字段
4. **关键词检测**：智能检测是否包含心超/心电图关键词
5. **图片处理**：仅对包含相关关键词的PDF进行图片OCR
6. **心超识别**：专门处理心脏超声图片，提取医疗信息
7. **记录合并**：智能合并同名人员的多次体检记录
8. **Excel输出**：生成包含图片嵌入的专业Excel报告

### 性能优化特性
- **条件触发**：只对包含特定关键词的PDF进行图片处理
- **反向扫描**：从后往前扫描页面，优先处理医疗图片
- **中文路径支持**：完美支持包含中文的文件路径
- **内存管理**：及时清理临时图片文件

## 📊 输出格式

生成的Excel文件包含以下主要列：

### 基础信息
- 姓名、性别、年龄、身份证号
- 总检日期、PDF文件名
- 处理状态、提取时间

### 体格检查
- 身高、体重、血压（收缩压/舒张压）
- 吸烟、饮酒等生活习惯

### 血液检查指标
- 血色素、总胆固醇、甘油三酯
- 高密度脂蛋白胆固醇、低密度脂蛋白胆固醇
- 糖化血红蛋白、肌酸激酶等

### 专科检查
- 超声心动图_超声所见、超声心动图_超声提示
- 冠状动脉CTA诊断小结、心电图诊断小结
- 心电图图片（直接嵌入单元格）

## ⚠️ 注意事项

### 环境配置
- 确保Python 3.7+环境
- 正确安装PaddleOCR及其依赖
- 建议使用虚拟环境避免依赖冲突

### 文件处理
- PDF文件应放置在配置的输入目录中
- 确保PDF文件可正常打开且不加密
- 支持中文文件名和路径

### 系统要求
- 建议至少4GB内存用于OCR处理
- 处理大量PDF时建议关闭其他占用内存的程序
- 首次运行会下载OCR模型，需要网络连接

## 🎯 项目特色

### 技术创新
- **混合内容处理**：业界领先的文字+图片混合处理策略
- **智能触发机制**：基于关键词的条件OCR处理，大幅提升效率
- **专业医疗优化**：针对医疗术语和格式的专门优化

### 实用价值
- **高准确率**：经过大量医疗PDF测试验证
- **高效处理**：批量处理能力，适合医疗机构使用
- **易于扩展**：模块化设计，便于添加新的提取字段

---

**开发团队**：医疗AI助手项目组
**版本**：v1.0
**更新日期**：2025-08-01
