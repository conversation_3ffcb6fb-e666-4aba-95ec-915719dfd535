# 临床信息图片自动提取与整理归档系统

**1. 项目概述**

本项目旨在开发一个Python应用程序，用于自动化处理存储在指定文件路径下的数百张临床信息图片。这些图片以包含日期和时间戳的格式命名（例如 `IMG_YYYYMMDD_HHMMSS.jpg`），内容为手机拍摄的电脑屏幕显示的患者临床信息。系统的核心目标是：

- 从一个**外部指定的Excel文件**中读取表头定义（包括大类和具体字段）。
- 利用光学字符识别（OCR）技术从图片中**针对性地提取**与表头字段相对应的信息。
- 将提取的数据结构化地填充到**符合该表头定义**的新Excel表格中。
- 根据提取的关键信息（如患者姓名）对原始图片进行重命名和分类归档。

**2. 项目目标**

- **动态表头驱动的信息提取：** 从图片中自动识别并提取由外部Excel文件定义的特定文本信息字段。
- **结构化数据输出：** 将提取的、与表头匹配的信息整理并填充到符合输入表头格式的新Excel工作表（"信息表"）中。
- **图片智能归档：** 根据提取的关键信息（如姓名或住院号），将处理过的图片副本或重命名后的原图片，分类存储到对应的子文件夹中。
- **提高效率与准确性：** 显著减少人工录入数据的时间和潜在错误，专注于提取有价值的信息。
- **适应性处理：** 能够处理因手机拍摄电脑屏幕而可能产生的图像质量问题（如畸变、反光、摩尔纹等）。

**3. 功能需求**

**3.1 输入处理**

- **图片输入路径：** 系统应能接受一个指定的文件夹路径作为输入，该路径下包含所有待处理的图片文件。
- **表头定义文件路径：** 系统应能接受一个指定的**Excel文件路径**作为输入，该文件包含目标输出表格的表头结构。
- **文件识别与排序：** 系统应能识别并处理符合 `IMG_YYYYMMDD_HHMMSS.jpg` 等常见格式的图片文件，并根据文件名（时间顺序）进行排序。

**3.2 表头定义读取**

- **读取机制：** 系统需要读取指定的表头定义Excel文件（假设目标工作表名为 "信息表" 或可配置）。
- **结构解析：** 解析该工作表的第一行（大类，可能包含合并单元格）和第二行（具体字段名称），将其作为后续信息提取和数据输出的模板。

**3.3 图像预处理**

- **必要性：** 针对手机拍摄屏幕的特点进行预处理，提高OCR准确率。
- **处理步骤（同前）：** 透视变换/畸变校正、亮度/对比度调整、去噪、锐化、二值化/灰度化、（可选）摩尔纹/屏幕炫光抑制等。

**3.4 光学字符识别 (OCR)**

- **引擎选择：** 调用开源OCR引擎（如 Tesseract OCR, PaddleOCR 等）。
- **语言支持：** 必须支持**中文**识别。
- **结果输出：** 输出识别到的文本内容。

**3.5 核心：信息提取与表头匹配**

- **目标驱动提取：** 系统的核心任务是根据**从表头定义文件加载的字段列表**，在OCR识别出的文本中查找并提取对应的值。忽略图片中存在但表头未定义的冗余信息。
- **匹配策略设计（重点）：**
    - 需要设计健壮的逻辑来将OCR文本块与表头字段准确关联。
    - **方法可能包括：**
        - **关键词定位：** 查找与表头字段名称高度相似的关键词（如 "姓名:", "住院号:" 等），并提取其附近或后续的文本作为值。需要处理关键词的可能变体（冒号、空格等）。
        - **格式模式匹配：** 对特定格式的字段（如日期 `YYYY-MM-DD`、身份证号、特定格式的编号）使用正则表达式进行匹配和提取。
        - **相对位置分析（如果布局稳定）：** 如果某类信息的布局相对固定，可以结合关键词和大致位置进行判断。
        - **上下文关联：** 利用图片的时间顺序和内容关联性（如连续图片出现相同姓名/住院号），辅助判断信息归属和填充同一行数据。
- **数据清洗：** 对成功匹配并提取的值进行必要的清洗（去空格、简单纠错等）。
- **关联性判断（同前）：** 确定处理单元，将属于同一患者/记录的多张图片信息整合。

**3.6 数据结构化输出 (Excel)**

- **文件生成：** 生成一个新的 `.xlsx` 格式的Excel文件（例如 `临床信息汇总表_输出.xlsx`）。
- **工作表创建：** 创建一个名为 "信息表" 的工作表。
- **表头写入：** 将从**表头定义文件**中读取的第一行（大类，含合并单元格）和第二行（字段名）**完全一致地写入**新生成的Excel文件中。
- **数据填充：**
    - 将针对同一记录提取并匹配好的信息，按照表头定义的字段顺序，填充到 "信息表" 的**一行**中。
    - 对于表头中定义但未能在图片中成功提取到对应信息的字段，单元格留空或标记为特定值（如 "未识别"）。

**3.7 图片整理与归档**

- **目标与方式（同前）：** 按姓名/住院号创建文件夹，存放图片副本或重命名后的图片。
- **路径配置：** 可配置输出归档文件夹的根目录。

**4. 非功能性需求**

- **性能：** 处理速度应满足实际应用需求。
- **准确性：** OCR识别和**字段匹配**的准确率是关键，需要有效的错误处理。
- **易用性：** 易于配置输入（图片路径、表头文件路径）和输出路径，易于运行。
- **健壮性：** 能处理文件读取错误、OCR失败、匹配不到关键信息等异常，提供日志。
- **可维护性：** 代码结构清晰、模块化。
- **平台：** **基于 Python 3.x 开发和运行即可**，暂不强制要求跨平台兼容性。

**5. 技术约束与假设**

- **开发语言：** Python 3.x。
- **核心技术：** 开源OCR库，图像处理库，Excel操作库。
- **输入文件格式：** 图片（`.jpg`, `.png`, `.jpeg`），表头定义文件（`.xlsx`）。
- **表头文件假设：** 假设输入的表头定义Excel文件格式正确，第一行为大类（可合并），第二行为具体字段名，位于名为"信息表"的工作表（或可配置）。
- **内容假设：** 假设图片中包含了与表头字段对应的关键词或可识别模式，并且图像质量经预处理后足以进行有效的OCR和信息匹配。
- **信息布局假设：** 假设信息在屏幕上的布局具有一定的规律性或包含清晰的关键词，有助于实现OCR结果与表头字段的匹配。

**6. 交付成果**

- **Python源代码：** 功能完整、结构良好、注释清晰的代码。
- **依赖说明：** 清晰的库依赖列表。
- **使用说明：** 如何配置（图片目录、表头文件、输出目录）和运行程序。