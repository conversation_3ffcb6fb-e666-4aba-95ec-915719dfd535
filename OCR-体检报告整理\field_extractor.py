#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段提取器
基于标定的坐标进行精确的字段提取
"""

import cv2
import numpy as np
import os
import json
from typing import Dict, List, Tuple, Optional, Any
import logging
from paddleocr import PaddleOCR
from image_alignment import ImageAligner
from PIL import Image

# 配置日志 - 使用与主程序一致的logger
logger = logging.getLogger('MedicalFormProcessor.FieldExtractor')
# 防止重复输出
logger.propagate = False
# 确保日志级别
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

class FieldExtractor:
    """字段提取器"""
    
    def __init__(self, config_file: Optional[str] = None, ocr_config: Optional[Dict] = None,
                 main_config: Optional[Any] = None):
        """
        初始化字段提取器

        Args:
            config_file: 标定配置文件路径
            ocr_config: OCR配置参数
            main_config: 主配置对象（ConfigParser）
        """
        # 初始化OCR引擎
        self._init_ocr_engines(ocr_config, main_config)
        self.aligner = ImageAligner()
        self.field_config = {}

        # 保存ROI图片的配置
        self.save_roi_images = main_config.getboolean('IMAGE_ALIGNMENT', 'save_roi_images', fallback=False) if main_config else False
        if self.save_roi_images:
            self.roi_output_dir = "roi_images"
            os.makedirs(self.roi_output_dir, exist_ok=True)
            logger.info(f"ROI图片将保存到: {self.roi_output_dir}")

        # 数字识别模式配置
        self.digit_recognition_mode = main_config.get('OCR_SETTINGS', 'digit_recognition_mode', fallback='single_box') if main_config else 'single_box'

        # 数字识别前处理配置
        if main_config:
            self.enable_morphology = main_config.getboolean('OCR_DIGIT', 'enable_morphology_processing', fallback=True)
            self.erosion_kernel_size = main_config.getint('OCR_DIGIT', 'erosion_kernel_size', fallback=2)
            self.dilation_kernel_size = main_config.getint('OCR_DIGIT', 'dilation_kernel_size', fallback=3)
        else:
            self.enable_morphology = True
            self.erosion_kernel_size = 2
            self.dilation_kernel_size = 3

        logger.info(f"数字识别模式: {self.digit_recognition_mode}")
        logger.info(f"形态学处理: {self.enable_morphology}, 腐蚀核: {self.erosion_kernel_size}, 膨胀核: {self.dilation_kernel_size}")

        if config_file and os.path.exists(config_file):
            self.load_config(config_file)

    def _init_ocr_engines(self, ocr_config: Optional[Dict] = None, main_config: Optional[Any] = None):
        """
        初始化OCR引擎，支持多种配置优化

        Args:
            ocr_config: OCR配置参数
        """
        # 默认配置
        default_config = {
            'confidence_threshold': 0.1,  # 降低置信度阈值
            'use_gpu': False,
            'use_angle_cls': True,
            'lang': 'ch'
        }

        # 合并用户配置
        if ocr_config:
            default_config.update(ocr_config)

        try:
            # 禁用PaddleOCR的调试日志
            import logging as paddle_logging
            paddle_logging.getLogger('ppocr').setLevel(paddle_logging.WARNING)

            # 从配置文件读取OCR参数
            if main_config:
                # 标准OCR引擎配置
                standard_config = {
                    'det_db_thresh': main_config.getfloat('OCR_STANDARD', 'det_db_thresh', fallback=0.2),
                    'det_db_box_thresh': main_config.getfloat('OCR_STANDARD', 'det_db_box_thresh', fallback=0.3),
                    'det_db_unclip_ratio': main_config.getfloat('OCR_STANDARD', 'det_db_unclip_ratio', fallback=2.0),
                    'rec_batch_num': main_config.getint('OCR_STANDARD', 'rec_batch_num', fallback=1),
                    'max_text_length': main_config.getint('OCR_STANDARD', 'max_text_length', fallback=50),
                    'drop_score': main_config.getfloat('OCR_STANDARD', 'drop_score', fallback=0.1)
                }

                # 数字OCR引擎配置
                digit_config = {
                    'language': main_config.get('OCR_DIGIT', 'language', fallback='ch'),
                    'det_db_thresh': main_config.getfloat('OCR_DIGIT', 'det_db_thresh', fallback=0.0),
                    'det_db_box_thresh': main_config.getfloat('OCR_DIGIT', 'det_db_box_thresh', fallback=0.0),
                    'det_db_unclip_ratio': main_config.getfloat('OCR_DIGIT', 'det_db_unclip_ratio', fallback=2.0),
                    'rec_batch_num': main_config.getint('OCR_DIGIT', 'rec_batch_num', fallback=1),
                    'use_space_char': main_config.getboolean('OCR_DIGIT', 'use_space_char', fallback=False),
                    'max_text_length': main_config.getint('OCR_DIGIT', 'max_text_length', fallback=20),
                    'drop_score': main_config.getfloat('OCR_DIGIT', 'drop_score', fallback=0.0)
                }

                # 手写体OCR引擎配置
                handwriting_config = {
                    'language': main_config.get('OCR_HANDWRITING', 'language', fallback='ch'),
                    'det_db_thresh': main_config.getfloat('OCR_HANDWRITING', 'det_db_thresh', fallback=0.15),
                    'det_db_box_thresh': main_config.getfloat('OCR_HANDWRITING', 'det_db_box_thresh', fallback=0.25),
                    'det_db_unclip_ratio': main_config.getfloat('OCR_HANDWRITING', 'det_db_unclip_ratio', fallback=2.5),
                    'rec_batch_num': main_config.getint('OCR_HANDWRITING', 'rec_batch_num', fallback=1),
                    'max_text_length': main_config.getint('OCR_HANDWRITING', 'max_text_length', fallback=20),
                    'drop_score': main_config.getfloat('OCR_HANDWRITING', 'drop_score', fallback=0.08)
                }
            else:
                # 使用默认配置
                standard_config = {'det_db_thresh': 0.2, 'det_db_box_thresh': 0.3, 'det_db_unclip_ratio': 2.0,
                                 'rec_batch_num': 1, 'max_text_length': 50, 'drop_score': 0.1}
                digit_config = {'language': 'ch', 'det_db_thresh': 0.0, 'det_db_box_thresh': 0.0,
                              'det_db_unclip_ratio': 2.0, 'rec_batch_num': 1, 'use_space_char': False,
                              'max_text_length': 20, 'drop_score': 0.0}
                handwriting_config = {'language': 'ch', 'det_db_thresh': 0.15, 'det_db_box_thresh': 0.25,
                                    'det_db_unclip_ratio': 2.5, 'rec_batch_num': 1, 'max_text_length': 20,
                                    'drop_score': 0.08}

            # 标准OCR引擎 - 用于文本字段（如姓名、临床症状具体等）
            self.ocr = PaddleOCR(
                use_angle_cls=default_config['use_angle_cls'],
                lang=default_config['lang'],
                use_gpu=default_config['use_gpu'],
                det_db_thresh=standard_config['det_db_thresh'],
                det_db_box_thresh=standard_config['det_db_box_thresh'],
                det_db_unclip_ratio=standard_config['det_db_unclip_ratio'],
                rec_batch_num=standard_config['rec_batch_num'],
                use_space_char=True,
                max_text_length=standard_config['max_text_length'],
                drop_score=standard_config['drop_score'],
            )

            # 数字专用OCR引擎 - 用于数字框字段（单个数字框和连续数字）
            self.digit_ocr = PaddleOCR(
                use_angle_cls=True,
                lang=digit_config['language'],
                use_gpu=default_config['use_gpu'],
                det_db_thresh=digit_config['det_db_thresh'],
                det_db_box_thresh=digit_config['det_db_box_thresh'],
                det_db_unclip_ratio=digit_config['det_db_unclip_ratio'],
                rec_batch_num=digit_config['rec_batch_num'],
                use_space_char=digit_config['use_space_char'],
                max_text_length=digit_config['max_text_length'],
                drop_score=digit_config['drop_score'],
            )

            # 手写体OCR引擎 - 用于手写字段（如姓名）
            self.handwriting_ocr = PaddleOCR(
                use_angle_cls=True,
                lang=handwriting_config['language'],
                use_gpu=default_config['use_gpu'],
                det_db_thresh=handwriting_config['det_db_thresh'],
                det_db_box_thresh=handwriting_config['det_db_box_thresh'],
                det_db_unclip_ratio=handwriting_config['det_db_unclip_ratio'],
                rec_batch_num=handwriting_config['rec_batch_num'],
                use_space_char=True,
                max_text_length=handwriting_config['max_text_length'],
                drop_score=handwriting_config['drop_score'],
            )

            self.confidence_threshold = default_config['confidence_threshold']
            logger.info(f"OCR引擎初始化成功，置信度阈值: {self.confidence_threshold}")
            logger.info(f"标准OCR配置: {standard_config}")
            logger.info(f"数字OCR配置: {digit_config}")
            logger.info(f"手写体OCR配置: {handwriting_config}")

        except Exception as e:
            logger.error(f"OCR引擎初始化失败: {str(e)}")
            # 回退到简单配置
            self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')
            self.digit_ocr = self.ocr  # 使用相同的引擎
            self.handwriting_ocr = self.ocr  # 使用相同的引擎
            self.confidence_threshold = 0.5

    def _select_ocr_engine(self, field_name: str):
        """
        根据字段名称选择合适的OCR引擎

        Args:
            field_name: 字段名称

        Returns:
            选择的OCR引擎
        """
        # 姓名字段使用手写体OCR
        if "姓名" in field_name:
            return self.handwriting_ocr

        # 数字相关字段使用数字OCR
        digit_fields = ["出生日期", "体重", "身高", "血压", "心率", "手机号码"]
        if any(digit_field in field_name for digit_field in digit_fields):
            return self.digit_ocr

        # 其他字段使用标准OCR
        return self.ocr

    def _add_white_border(self, roi: np.ndarray, border_size: int = 40) -> np.ndarray:
        """
        为ROI图像添加白色边框，提高OCR检测率

        Args:
            roi: 原始ROI图像
            border_size: 边框大小（像素）

        Returns:
            添加白边后的图像
        """
        try:
            # 确保是3通道图像
            if len(roi.shape) == 2:
                roi = cv2.cvtColor(roi, cv2.COLOR_GRAY2BGR)
            elif len(roi.shape) == 3 and roi.shape[2] == 1:
                roi = cv2.cvtColor(roi, cv2.COLOR_GRAY2BGR)

            # 添加白色边框 - 在四个方向扩展白色部分
            bordered = cv2.copyMakeBorder(
                roi,
                border_size, border_size, border_size, border_size,
                cv2.BORDER_CONSTANT,
                value=[255, 255, 255]  # 白色边框
            )

            return bordered

        except Exception as e:
            logger.warning(f"添加白边失败: {str(e)}")
            return roi

    def _save_roi_image(self, roi: np.ndarray, field_name: str, field_type: str, image_name: str = ""):
        """
        保存ROI图片用于检查

        Args:
            roi: 处理后的ROI图像
            field_name: 字段名称
            field_type: 字段类型
            image_name: 图像名称
        """
        if not self.save_roi_images:
            return

        try:
            # 生成文件名
            safe_field_name = field_name.replace("/", "_").replace("\\", "_").replace(":", "_")
            safe_image_name = image_name.replace("/", "_").replace("\\", "_").replace(":", "_")
            filename = f"{safe_image_name}_{safe_field_name}_{field_type}.png"
            filepath = os.path.join(self.roi_output_dir, filename)
            
            # 保存图像
            pil_img = Image.fromarray(roi)
            pil_img.save(filepath, quality=95)  # 可调压缩质量
            # cv2.imwrite(filepath, roi)
            logger.debug(f"保存ROI图片: {filepath}")

        except Exception as e:
            logger.warning(f"保存ROI图片失败: {str(e)}")

    def _preprocess_roi_for_text(self, roi: np.ndarray) -> np.ndarray:
        """
        针对文本识别的ROI预处理 - 简化版本

        Args:
            roi: 原始ROI图像

        Returns:
            预处理后的图像
        """
        try:
            # 添加白色边框（关键优化）
            bordered = self._add_white_border(roi, border_size=200)

            # 简单的对比度增强（保持图像质量）
            if len(bordered.shape) == 3:
                gray = cv2.cvtColor(bordered, cv2.COLOR_BGR2GRAY)
            else:
                gray = bordered.copy()

            # 轻微的对比度增强
            enhanced = cv2.convertScaleAbs(gray, alpha=1.1, beta=10)

            return enhanced

        except Exception as e:
            logger.warning(f"文本ROI预处理失败: {str(e)}")
            return roi

    def _preprocess_roi_for_digits(self, roi: np.ndarray) -> np.ndarray:
        """
        针对数字识别的ROI预处理 - 简化版本

        Args:
            roi: 原始ROI图像

        Returns:
            预处理后的图像
        """
        try:
            # 添加白色边框（关键优化）
            bordered = self._add_white_border(roi, border_size=200)

            # 适度放大以提高小数字识别率
            scale_factor = 2
            height, width = bordered.shape[:2]
            enlarged = cv2.resize(bordered, (width * scale_factor, height * scale_factor),
                                interpolation=cv2.INTER_CUBIC)

            # 简单的对比度增强
            if len(enlarged.shape) == 3:
                gray = cv2.cvtColor(enlarged, cv2.COLOR_BGR2GRAY)
            else:
                gray = enlarged.copy()

            enhanced = cv2.convertScaleAbs(gray, alpha=1.2, beta=15)

            # 形态学处理 - 消除手写数字的下划线和字符间竖线
            if self.enable_morphology:
                enhanced = self._apply_morphology_processing(enhanced)

            return enhanced

        except Exception as e:
            logger.warning(f"数字ROI预处理失败: {str(e)}")
            return roi

    def _apply_morphology_processing(self, image: np.ndarray) -> np.ndarray:
        """
        应用形态学处理来改善数字识别

        Args:
            image: 输入图像

        Returns:
            处理后的图像
        """
        try:
            # 确保是灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 腐蚀操作 - 消除细线（如下划线、字符间竖线）
            erosion_kernel = np.ones((self.erosion_kernel_size, self.erosion_kernel_size), np.uint8)
            eroded = cv2.erode(binary, erosion_kernel, iterations=1)

            # 膨胀操作 - 恢复字符主体
            dilation_kernel = np.ones((self.dilation_kernel_size, self.dilation_kernel_size), np.uint8)
            dilated = cv2.dilate(eroded, dilation_kernel, iterations=1)

            logger.debug(f"形态学处理完成: 腐蚀核{self.erosion_kernel_size}x{self.erosion_kernel_size}, 膨胀核{self.dilation_kernel_size}x{self.dilation_kernel_size}")
            return dilated

        except Exception as e:
            logger.warning(f"形态学处理失败: {str(e)}")
            return image

    def _preprocess_roi_for_checkbox(self, roi: np.ndarray) -> np.ndarray:
        """
        针对复选框的ROI预处理 - 重点优化白边扩展

        Args:
            roi: 原始ROI图像

        Returns:
            预处理后的图像
        """
        try:
            # 添加白色边框（最关键的优化）
            bordered = self._add_white_border(roi, border_size=200)

            # 保持原始图像质量，最小化预处理
            if len(bordered.shape) == 3:
                gray = cv2.cvtColor(bordered, cv2.COLOR_BGR2GRAY)
            else:
                gray = bordered.copy()

            # 轻微增强对比度
            enhanced = cv2.convertScaleAbs(gray, alpha=1.1, beta=5)

            return enhanced

        except Exception as e:
            logger.warning(f"复选框ROI预处理失败: {str(e)}")
            return roi

    def _detect_checkbox_by_pixels(self, roi: np.ndarray) -> Dict[str, Any]:
        """
        基于像素密度的复选框检测

        Args:
            roi: 预处理后的ROI图像

        Returns:
            检测结果
        """
        try:
            # 计算黑色像素比例
            total_pixels = roi.size
            black_pixels = np.sum(roi == 0)
            black_ratio = black_pixels / total_pixels

            # 动态阈值：根据图像大小调整
            area = roi.shape[0] * roi.shape[1]
            if area < 400:  # 小复选框
                threshold = 0.25
            elif area < 900:  # 中等复选框
                threshold = 0.20
            else:  # 大复选框
                threshold = 0.15

            threshold = 0.08
            is_checked = black_ratio > threshold
            confidence = min(black_ratio * 2, 1.0) if is_checked else min((1 - black_ratio) * 2, 1.0)

            return {
                "method": "像素密度",
                "checked": is_checked,
                "confidence": confidence,
                "black_ratio": black_ratio,
                "threshold": threshold
            }

        except Exception as e:
            return {"method": "像素密度", "checked": False, "confidence": 0, "error": str(e)}

    def _detect_checkbox_by_contours(self, roi: np.ndarray) -> Dict[str, Any]:
        """
        基于轮廓检测的复选框检测

        Args:
            roi: 预处理后的ROI图像

        Returns:
            检测结果
        """
        try:
            # 查找轮廓
            contours, _ = cv2.findContours(roi, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return {"method": "轮廓检测", "checked": False, "confidence": 0.8}

            # 分析轮廓特征
            total_area = roi.shape[0] * roi.shape[1]
            filled_area = 0
            significant_contours = 0

            for contour in contours:
                area = cv2.contourArea(contour)
                if area > total_area * 0.05:  # 忽略太小的轮廓
                    filled_area += area
                    significant_contours += 1

            fill_ratio = filled_area / total_area

            # 判断逻辑：有显著轮廓且填充比例合理
            is_checked = significant_contours > 0 and fill_ratio > 0.1
            confidence = min(fill_ratio * 3 + significant_contours * 0.2, 1.0)

            return {
                "method": "轮廓检测",
                "checked": is_checked,
                "confidence": confidence,
                "fill_ratio": fill_ratio,
                "contour_count": significant_contours
            }

        except Exception as e:
            return {"method": "轮廓检测", "checked": False, "confidence": 0, "error": str(e)}

    def _detect_checkbox_by_edges(self, roi: np.ndarray) -> Dict[str, Any]:
        """
        基于边缘检测的复选框检测

        Args:
            roi: 预处理后的ROI图像

        Returns:
            检测结果
        """
        try:
            # Canny边缘检测
            edges = cv2.Canny(roi, 50, 150)

            # 计算边缘像素密度
            total_pixels = edges.size
            edge_pixels = np.sum(edges > 0)
            edge_ratio = edge_pixels / total_pixels

            # 检测直线（勾选标记通常有直线特征）
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=10,
                                   minLineLength=5, maxLineGap=3)

            line_count = len(lines) if lines is not None else 0

            # 判断逻辑：边缘密度适中且有直线特征
            is_checked = edge_ratio > 0.05 and line_count > 0
            confidence = min(edge_ratio * 5 + line_count * 0.1, 1.0)

            return {
                "method": "边缘检测",
                "checked": is_checked,
                "confidence": confidence,
                "edge_ratio": edge_ratio,
                "line_count": line_count
            }

        except Exception as e:
            return {"method": "边缘检测", "checked": False, "confidence": 0, "error": str(e)}

    def _detect_checkbox_by_comparison(self, image: np.ndarray, region: List[int],
                                     group_regions: List[List[int]]) -> Dict[str, Any]:
        """
        基于同组复选框对比的检测方法

        Args:
            image: 完整图像
            region: 当前复选框区域
            group_regions: 同组所有复选框区域

        Returns:
            检测结果
        """
        try:
            black_ratios = []

            # 计算所有同组复选框的黑色像素比例
            for reg in group_regions:
                x, y, w, h = reg
                roi = image[y:y+h, x:x+w]

                if roi.size > 0:
                    # 预处理
                    processed = self._preprocess_roi_for_checkbox(roi)
                    # 计算黑色像素比例
                    total_pixels = processed.size
                    black_pixels = np.sum(processed == 0)
                    black_ratio = black_pixels / total_pixels
                    black_ratios.append(black_ratio)

            if len(black_ratios) < 2:
                return {"method": "对比分析", "checked": False, "confidence": 0.5}

            # 当前区域的黑色像素比例
            x, y, w, h = region
            current_roi = image[y:y+h, x:x+w]
            current_processed = self._preprocess_roi_for_checkbox(current_roi)
            current_black_ratio = np.sum(current_processed == 0) / current_processed.size

            # 找到当前区域在列表中的位置
            current_index = -1
            for i, reg in enumerate(group_regions):
                if reg == region:
                    current_index = i
                    break

            if current_index == -1:
                return {"method": "对比分析", "checked": False, "confidence": 0.5}

            # 对比分析：选择黑色像素最多的选项
            max_ratio = max(black_ratios)
            max_index = black_ratios.index(max_ratio)

            # 如果当前区域的黑色像素比例是最高的，且明显高于其他选项
            is_checked = (current_index == max_index and
                         current_black_ratio > 0.1 and
                         current_black_ratio > np.mean(black_ratios) * 1.5)

            confidence = current_black_ratio / max_ratio if max_ratio > 0 else 0.5

            return {
                "method": "对比分析",
                "checked": is_checked,
                "confidence": confidence,
                "current_ratio": current_black_ratio,
                "group_ratios": black_ratios,
                "max_ratio": max_ratio
            }

        except Exception as e:
            return {"method": "对比分析", "checked": False, "confidence": 0, "error": str(e)}

    def _combine_checkbox_results(self, results: List[Dict[str, Any]], field_name: str) -> Dict[str, Any]:
        """
        综合多种检测方法的结果

        Args:
            results: 各种检测方法的结果列表
            field_name: 字段名称

        Returns:
            最终检测结果
        """
        try:
            if not results:
                return {"checked": False, "confidence": 0, "error": "无检测结果"}

            # 过滤有效结果
            valid_results = [r for r in results if "error" not in r]

            if not valid_results:
                return {"checked": False, "confidence": 0, "error": "所有检测方法都失败"}

            # 权重设置（根据方法可靠性）
            method_weights = {
                "对比分析": 0.4,    # 最可靠
                "轮廓检测": 0.3,    # 较可靠
                "像素密度": 0.2,    # 一般可靠
                "边缘检测": 0.1     # 辅助方法
            }

            # 加权投票
            total_weight = 0
            weighted_score = 0
            method_details = []

            for result in valid_results:
                method = result.get("method", "未知")
                checked = result.get("checked", False)
                confidence = result.get("confidence", 0)
                weight = method_weights.get(method, 0.1)

                # 计算加权分数
                score = confidence if checked else (1 - confidence)
                weighted_score += score * weight
                total_weight += weight

                method_details.append({
                    "method": method,
                    "checked": checked,
                    "confidence": confidence,
                    "weight": weight
                })

            # 最终判断
            if total_weight > 0:
                final_score = weighted_score / total_weight
                is_checked = final_score > 0.5
                final_confidence = final_score if is_checked else (1 - final_score)
            else:
                is_checked = False
                final_confidence = 0.5

            return {
                "checked": bool(is_checked),
                "confidence": float(final_confidence),
                "detection_methods": method_details,
                "final_score": float(final_score) if total_weight > 0 else 0.5,
                "field_name": field_name,
                "region": results[0].get("region") if results else None
            }

        except Exception as e:
            logger.error(f"复选框结果综合失败: {str(e)}")
            return {"checked": False, "confidence": 0, "error": str(e)}
    
    def load_config(self, config_file: str) -> bool:
        """
        加载标定配置
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            是否加载成功
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            if "calibrated_regions" in config_data:
                self.field_config = config_data["calibrated_regions"]
                logger.info(f"已加载 {len(self.field_config)} 个字段配置")
                return True
            else:
                logger.error("配置文件格式错误")
                return False
                
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            return False
    
    def extract_text_field(self, image: np.ndarray, region: List[int], field_name: str = "", image_identifier: str = "current_image") -> Dict[str, Any]:
        """
        提取文本字段 - 支持智能OCR引擎选择

        Args:
            image: 输入图像
            region: 区域坐标 [x, y, width, height]
            field_name: 字段名称，用于选择合适的OCR引擎

        Returns:
            提取结果
        """
        try:
            x, y, w, h = region

            # 裁剪区域
            roi = image[y:y+h, x:x+w]

            if roi.size == 0:
                return {"text": "", "confidence": 0, "error": "区域为空"}

            # 预处理ROI（添加白边）
            roi = self._preprocess_roi_for_text(roi)

            # 保存ROI图片用于检查
            self._save_roi_image(roi, field_name, "text", image_identifier)

            # 根据字段类型选择OCR引擎
            ocr_engine = self._select_ocr_engine(field_name)

            # OCR识别
            result = ocr_engine.ocr(roi, cls=True)
            
            if result and result[0]:
                # 合并所有识别的文本
                texts = []
                confidences = []
                
                for line in result[0]:
                    if len(line) >= 2 and len(line[1]) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]
                        texts.append(text)
                        confidences.append(confidence)
                
                combined_text = " ".join(texts)
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                # 输出识别过程信息
                logger.info(f"标框：{field_name}，结果：{combined_text}，置信度：{avg_confidence:.3f}")

                return {
                    "text": combined_text,
                    "confidence": float(avg_confidence),
                    "region": region,
                    "raw_results": result[0]
                }
            else:
                # 输出识别过程信息
                logger.info(f"标框：{field_name}，结果：（空），置信度：0.000")
                return {"text": "", "confidence": 0, "region": region}
                
        except Exception as e:
            logger.error(f"文本字段提取失败: {str(e)}")
            return {"text": "", "confidence": 0, "error": str(e)}
    
    def extract_checkbox_field(self, image: np.ndarray, region: List[int], field_name: str = "",
                              all_extracted_fields: Dict = None) -> Dict[str, Any]:
        """
        提取复选框字段 - 纯视觉检测，不使用OCR

        Args:
            image: 输入图像
            region: 区域坐标 [x, y, width, height]
            field_name: 字段名称
            group_regions: 同组复选框的区域列表（用于对比分析）

        Returns:
            提取结果
        """
        try:
            x, y, w, h = region

            # 裁剪区域
            roi = image[y:y+h, x:x+w]

            if roi.size == 0:
                return {"checked": False, "confidence": 0, "error": "区域为空"}

            # 转换为灰度图
            if len(roi.shape) == 3:
                gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            else:
                gray_roi = roi.copy()

            # 二值化处理
            _, binary_roi = cv2.threshold(gray_roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 保存ROI图片用于检查
            # self._save_roi_image(binary_roi, field_name, "checkbox", "current_image")

            # 计算黑色像素数量
            total_pixels = binary_roi.size
            black_pixels = np.sum(binary_roi == 0)
            black_ratio = black_pixels / total_pixels

            # 基于像素数量的判断逻辑
            threshold = 0.08  # 统一使用0.08阈值

            # 竞对选框智能选择逻辑
            final_is_checked, final_confidence, selection_method = self._apply_competitive_selection(
                field_name, black_ratio, threshold, all_extracted_fields, image
            )

            # 输出识别过程信息
            logger.info(f"标框：{field_name}，黑色像素比例：{black_ratio:.3f}，阈值：{threshold:.3f}，结果：{'选中' if final_is_checked else '未选中'}，方法：{selection_method}")

            return {
                "checked": bool(final_is_checked),
                "confidence": float(final_confidence),
                "black_ratio": float(black_ratio),
                "threshold": float(threshold),
                "detection_method": selection_method,
                "field_name": field_name,
                "region": region
            }

        except Exception as e:
            logger.error(f"复选框字段提取失败: {str(e)}")
            return {"checked": False, "confidence": 0, "error": str(e)}

    def _apply_competitive_selection(self, field_name: str, black_ratio: float, threshold: float,
                                   all_extracted_fields: Dict, image: np.ndarray) -> tuple:
        """
        应用竞对选框的智能选择逻辑

        Args:
            field_name: 当前字段名称
            black_ratio: 当前字段的黑色像素比例
            threshold: 阈值
            all_extracted_fields: 所有已提取的字段（用于查找竞对字段）
            image: 原始图像

        Returns:
            (is_checked, confidence, selection_method)
        """
        try:
            # 基础判断
            is_checked_by_threshold = black_ratio > threshold
            base_confidence = min(black_ratio * 2, 1.0) if is_checked_by_threshold else min((1 - black_ratio) * 2, 1.0)

            # 查找竞对字段
            competing_fields = self._find_competing_fields(field_name)

            if not competing_fields or all_extracted_fields is None:
                return is_checked_by_threshold, base_confidence, "threshold_only"

            # 计算竞对字段的黑色像素比例
            competing_ratios = {}
            for comp_field in competing_fields:
                if comp_field in all_extracted_fields:
                    comp_ratio = all_extracted_fields[comp_field].get("black_ratio", 0)
                    competing_ratios[comp_field] = comp_ratio
                else:
                    # 如果竞对字段还未处理，临时计算其黑色像素比例
                    comp_ratio = self._calculate_black_ratio_for_field(comp_field, image)
                    if comp_ratio is not None:
                        competing_ratios[comp_field] = comp_ratio

            # 添加当前字段
            competing_ratios[field_name] = black_ratio

            # 应用竞对选择逻辑
            return self._select_winner_from_competitors(field_name, competing_ratios, threshold)

        except Exception as e:
            logger.warning(f"竞对选择逻辑失败: {str(e)}")
            return is_checked_by_threshold, base_confidence, "threshold_fallback"

    def _find_competing_fields(self, field_name: str) -> List[str]:
        """
        查找竞对字段

        Args:
            field_name: 字段名称

        Returns:
            竞对字段列表
        """
        # 定义竞对字段组
        competing_groups = [
            ["性别男", "性别女"],
            ["临床症状无", "临床症状有"],
            ["吸烟否", "吸烟已戒烟", "吸烟当前吸烟"],
            ["饮酒否", "饮酒已戒酒", "饮酒当前饮酒"],
            ["心肌梗死病史否", "心肌梗死病史是"],
            ["卒中史否", "卒中史是", "卒中史脑梗死", "卒中史脑出血", "卒中史不详"],
            ["高血压病史否", "高血压病史是"],
            ["血脂异常病史否", "血脂异常病史是"],
            ["糖尿病否", "糖尿病是"],
            ["外周血管病否", "外周血管病是"],
            ["抗血小板药物否", "抗血小板药物是"],
            ["他汀类否", "他汀类是"],
            ["降压药物否", "降压药物是"],
            ["降糖药物否", "降糖药物是"],
            ["胰岛素否", "胰岛素是"]
        ]

        # 查找当前字段所属的竞对组
        for group in competing_groups:
            if field_name in group:
                # 返回除当前字段外的其他竞对字段
                return [f for f in group if f != field_name]

        return []

    def _calculate_black_ratio_for_field(self, field_name: str, image: np.ndarray) -> Optional[float]:
        """
        为指定字段计算黑色像素比例

        Args:
            field_name: 字段名称
            image: 原始图像

        Returns:
            黑色像素比例，如果计算失败返回None
        """
        try:
            if field_name not in self.field_config:
                return None

            field_info = self.field_config[field_name]
            if field_info.get("type") != "checkbox":
                return None

            region = field_info["region"]
            x, y, w, h = region

            # 裁剪区域
            roi = image[y:y+h, x:x+w]
            if roi.size == 0:
                return None

            # 转换为灰度图并二值化
            if len(roi.shape) == 3:
                gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            else:
                gray_roi = roi.copy()

            _, binary_roi = cv2.threshold(gray_roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 计算黑色像素比例
            total_pixels = binary_roi.size
            black_pixels = np.sum(binary_roi == 0)
            return black_pixels / total_pixels

        except Exception as e:
            logger.warning(f"计算字段 {field_name} 黑色像素比例失败: {str(e)}")
            return None

    def _select_winner_from_competitors(self, current_field: str, competing_ratios: Dict[str, float],
                                      threshold: float) -> tuple:
        """
        从竞对字段中选择获胜者

        Args:
            current_field: 当前字段名称
            competing_ratios: 所有竞对字段的黑色像素比例
            threshold: 阈值

        Returns:
            (is_checked, confidence, selection_method)
        """
        current_ratio = competing_ratios[current_field]

        # 找到黑色像素比例最高的字段
        max_field = max(competing_ratios.keys(), key=lambda k: competing_ratios[k])
        max_ratio = competing_ratios[max_field]

        # 找到黑色像素比例第二高的字段
        sorted_ratios = sorted(competing_ratios.items(), key=lambda x: x[1], reverse=True)
        second_ratio = sorted_ratios[1][1] if len(sorted_ratios) > 1 else 0

        # 应用竞对选择逻辑
        if max_ratio > second_ratio * 1.25:  # 差值大于小者的25%
            # 选择黑色像素比例最高的字段
            is_winner = (current_field == max_field)
            confidence = max_ratio if is_winner else (1 - current_ratio)
            method = "competitive_selection"
        else:
            # 使用阈值判断
            is_winner = current_ratio > threshold
            confidence = min(current_ratio * 2, 1.0) if is_winner else min((1 - current_ratio) * 2, 1.0)
            method = "threshold_with_competition"

        return is_winner, confidence, method

    def extract_digit_box_field(self, image: np.ndarray, region: List[int], field_name: str = "", image_identifier: str = "current_image") -> Dict[str, Any]:
        """
        提取数字框字段 - 单个数字框，优化手写数字识别

        Args:
            image: 输入图像
            region: 区域坐标 [x, y, width, height]
            field_name: 字段名称

        Returns:
            提取结果
        """
        try:
            x, y, w, h = region

            # 裁剪区域
            roi = image[y:y+h, x:x+w]

            if roi.size == 0:
                return {"digit": "", "confidence": 0, "error": "区域为空"}

            # 针对数字的特殊预处理
            roi = self._preprocess_roi_for_digits(roi)

            # 保存ROI图片用于检查
            self._save_roi_image(roi, field_name, "digit", image_identifier)

            # 使用数字专用OCR引擎
            result = self.digit_ocr.ocr(roi, cls=True)

            if result and result[0]:
                # 提取数字
                digits = []
                confidences = []

                for line in result[0]:
                    if len(line) >= 2 and len(line[1]) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]

                        # 只保留数字字符
                        digit_chars = ''.join(c for c in text if c.isdigit())
                        if digit_chars:
                            digits.append(digit_chars)
                            confidences.append(confidence)

                if digits:
                    # 取第一个识别到的数字
                    digit = digits[0][:1]  # 只取第一个数字字符
                    avg_confidence = sum(confidences) / len(confidences)

                    # 输出识别过程信息
                    logger.info(f"标框：{field_name}，结果：{digit}，置信度：{avg_confidence:.3f}")

                    return {
                        "digit": digit,
                        "confidence": float(avg_confidence),
                        "region": region,
                        "raw_text": ''.join(digits)
                    }

            # 未识别到数字，可能是空框
            # 输出识别过程信息
            logger.info(f"标框：{field_name}，结果：（空），置信度：0.800")
            return {
                "digit": "",
                "confidence": 0.8,  # 空框的置信度设为0.8
                "region": region,
                "is_empty": True
            }

        except Exception as e:
            logger.error(f"数字框字段提取失败: {str(e)}")
            return {"digit": "", "confidence": 0, "error": str(e)}

    def extract_digit_large_box_field(self, image: np.ndarray, region: List[int], field_name: str = "", image_identifier: str = "current_image") -> Dict[str, Any]:
        """
        提取大框数字字段（如整个出生日期、血压、体重等）

        Args:
            image: 输入图像
            region: 目标区域 [x, y, w, h]
            field_name: 字段名称

        Returns:
            包含识别结果的字典
        """
        logger.info(f"提取字段: {field_name} (digit_large_box)")

        try:
            x, y, w, h = region

            # 裁剪ROI
            roi = image[y:y+h, x:x+w]
            if roi.size == 0:
                logger.warning(f"ROI区域为空: {region}")
                return {"text": "", "confidence": 0, "is_empty": True}

            # 针对大框数字的特殊预处理
            roi = self._preprocess_roi_for_large_digits(roi)

            # 保存ROI图片用于检查
            self._save_roi_image(roi, field_name, "digit_large_box", image_identifier)

            # 使用数字专用OCR引擎
            result = self.digit_ocr.ocr(roi, cls=True)

            # 添加结果验证，防止崩溃
            if not result:
                logger.warning(f"OCR返回空结果: {field_name}")
                result = [[]]
            elif not result[0]:
                logger.warning(f"OCR返回空列表: {field_name}")
                result = [[]]

            if result and result[0]:
                # 提取所有识别到的文本
                all_text = ""
                total_confidence = 0
                text_count = 0

                for line in result[0]:
                    if len(line) >= 2 and len(line[1]) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]

                        # 过滤数字和常见符号，并进行字符修正
                        filtered_text = self._correct_digit_characters(text)
                        if filtered_text:
                            all_text += filtered_text
                            total_confidence += confidence
                            text_count += 1

                # 计算平均置信度
                avg_confidence = total_confidence / text_count if text_count > 0 else 0

                # 后处理：根据字段类型格式化结果
                formatted_text = self._format_large_box_result(field_name, all_text)

                logger.info(f"标框：{field_name}，结果：{formatted_text}，置信度：{avg_confidence:.3f}")

                return {
                    "text": formatted_text,
                    "confidence": float(avg_confidence),
                    "raw_text": all_text,
                    "is_empty": len(formatted_text.strip()) == 0,
                    "field_name": field_name,
                    "region": region,
                    "field_type": "digit_large_box"
                }
            else:
                logger.info(f"标框：{field_name}，结果：（空），置信度：0.000")
                return {
                    "text": "",
                    "confidence": 0.0,
                    "raw_text": "",
                    "is_empty": True,
                    "field_name": field_name,
                    "region": region,
                    "field_type": "digit_large_box"
                }

        except Exception as e:
            logger.error(f"大框数字字段提取失败: {str(e)}")
            return {"text": "", "confidence": 0, "error": str(e), "is_empty": True}

    def _preprocess_roi_for_large_digits(self, roi: np.ndarray) -> np.ndarray:
        """
        针对大框数字的特殊预处理

        Args:
            roi: 输入ROI图像

        Returns:
            预处理后的图像
        """
        try:
            # 确保是3通道图像
            if len(roi.shape) == 2:
                roi = cv2.cvtColor(roi, cv2.COLOR_GRAY2BGR)
            elif len(roi.shape) == 3 and roi.shape[2] == 1:
                roi = cv2.cvtColor(roi, cv2.COLOR_GRAY2BGR)

            # 添加白色边框 - 针对大框数字字段优化
            bordered = self._add_white_border(roi, border_size=300)

            # 形态学处理 - 消除手写数字的下划线和字符间竖线
            if self.enable_morphology:
                bordered = self._apply_morphology_processing_for_large_box(bordered)

            return bordered

        except Exception as e:
            logger.warning(f"大框数字预处理失败: {str(e)}")
            return roi

    def _correct_digit_characters(self, text: str) -> str:
        """修正常见的数字识别错误"""
        # 常见的OCR数字识别错误映射
        corrections = {
            'O': '0', 'o': '0', 'Q': '0', 'D': '0',
            'I': '1', 'l': '1', '|': '1', 'i': '1',
            'Z': '2', 'z': '2',
            'S': '5', 's': '5',
            'G': '6', 'g': '6',
            'T': '7', 't': '7',
            'B': '8', 'b': '8',
            'g': '9', 'q': '9'
        }

        # 应用修正并过滤
        corrected = ""
        for char in text:
            if char.isdigit():
                corrected += char
            elif char in corrections:
                corrected += corrections[char]
            elif char in '.-/':
                corrected += char

        return corrected

    def _apply_morphology_processing_for_large_box(self, image: np.ndarray) -> np.ndarray:
        """
        针对大框数字的形态学处理

        Args:
            image: 输入图像

        Returns:
            处理后的图像
        """
        try:
            # 确保是灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 针对大框的特殊处理：使用更大的核来处理连续数字
            # 水平腐蚀 - 消除水平线（如下划线）
            h_kernel = np.ones((1, self.erosion_kernel_size * 2), np.uint8)
            h_eroded = cv2.erode(binary, h_kernel, iterations=1)

            # 垂直腐蚀 - 消除垂直线（如字符间分隔线）
            v_kernel = np.ones((self.erosion_kernel_size, 1), np.uint8)
            v_eroded = cv2.erode(h_eroded, v_kernel, iterations=1)

            # 膨胀操作 - 恢复字符主体
            dilation_kernel = np.ones((self.dilation_kernel_size, self.dilation_kernel_size), np.uint8)
            dilated = cv2.dilate(v_eroded, dilation_kernel, iterations=1)

            # 转换回3通道
            if len(image.shape) == 3:
                result = cv2.cvtColor(dilated, cv2.COLOR_GRAY2BGR)
            else:
                result = dilated

            logger.debug(f"大框形态学处理完成")
            return result

        except Exception as e:
            logger.warning(f"大框形态学处理失败: {str(e)}")
            return image

    def _format_large_box_result(self, field_name: str, raw_text: str) -> str:
        """
        根据字段类型格式化大框识别结果

        Args:
            field_name: 字段名称
            raw_text: 原始识别文本

        Returns:
            格式化后的文本
        """
        try:
            # 清理文本：只保留数字和常见符号
            cleaned_text = ''.join(c for c in raw_text if c.isdigit() or c in '.-/')

            if "出生日期" in field_name:
                # 出生日期格式化：尝试识别为YYYYMMDD格式
                digits_only = ''.join(c for c in cleaned_text if c.isdigit())
                if len(digits_only) >= 8:
                    # 取前8位作为出生日期
                    date_str = digits_only[:8]
                    return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                elif len(digits_only) >= 6:
                    # 6位数字，可能是YYMMDD格式
                    date_str = digits_only[:6]
                    year = "19" + date_str[:2] if int(date_str[:2]) > 50 else "20" + date_str[:2]
                    return f"{year}-{date_str[2:4]}-{date_str[4:6]}"
                else:
                    return cleaned_text

            elif "血压" in field_name:
                # 血压格式化：尝试识别收缩压/舒张压
                digits_only = ''.join(c for c in cleaned_text if c.isdigit())
                if len(digits_only) >= 4:
                    # 尝试分割为收缩压和舒张压
                    if len(digits_only) == 4:
                        return f"{digits_only[:2]}/{digits_only[2:]}"
                    elif len(digits_only) == 5:
                        return f"{digits_only[:3]}/{digits_only[3:]}"
                    elif len(digits_only) == 6:
                        return f"{digits_only[:3]}/{digits_only[3:]}"
                    else:
                        return cleaned_text
                else:
                    return cleaned_text

            elif "体重" in field_name or "身高" in field_name:
                # 体重/身高：保留小数点
                if '.' in cleaned_text:
                    return cleaned_text
                else:
                    # 如果没有小数点，尝试添加
                    digits_only = ''.join(c for c in cleaned_text if c.isdigit())
                    if len(digits_only) >= 2:
                        if "体重" in field_name and len(digits_only) >= 3:
                            # 体重：如果是3位数字，在倒数第一位前加小数点
                            return f"{digits_only[:-1]}.{digits_only[-1]}"
                        elif "身高" in field_name and len(digits_only) >= 3:
                            # 身高：通常是整数
                            return digits_only
                    return cleaned_text

            elif "心率" in field_name:
                # 心率：通常是整数
                digits_only = ''.join(c for c in cleaned_text if c.isdigit())
                return digits_only

            else:
                # 其他字段：返回清理后的文本
                return cleaned_text

        except Exception as e:
            logger.warning(f"格式化大框结果失败: {str(e)}")
            return raw_text

    def validate_extracted_data(self, field_name: str, field_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证提取的数据

        Args:
            field_name: 字段名称
            field_result: 提取结果

        Returns:
            验证后的结果（包含验证信息）
        """
        validation_result = field_result.copy()
        validation_result["validation"] = {
            "is_valid": True,
            "warnings": [],
            "should_highlight": False
        }

        try:
            # 出生日期验证
            if field_name.startswith("出生日期"):
                digit = field_result.get("digit", "")
                if digit and not digit.isdigit():
                    validation_result["validation"]["is_valid"] = False
                    validation_result["validation"]["warnings"].append("非数字字符")
                    validation_result["validation"]["should_highlight"] = True

            # 血压验证
            elif field_name in ["血压收缩压", "血压舒张压"]:
                digit = field_result.get("digit", "")
                if digit:
                    try:
                        value = int(digit)
                        if field_name == "血压收缩压" and (value < 80 or value > 250):
                            validation_result["validation"]["warnings"].append("收缩压值异常")
                            validation_result["validation"]["should_highlight"] = True
                        elif field_name == "血压舒张压" and (value < 40 or value > 150):
                            validation_result["validation"]["warnings"].append("舒张压值异常")
                            validation_result["validation"]["should_highlight"] = True
                    except ValueError:
                        validation_result["validation"]["is_valid"] = False
                        validation_result["validation"]["warnings"].append("血压值格式错误")
                        validation_result["validation"]["should_highlight"] = True

            # 体重验证
            elif field_name.startswith("体重"):
                digit = field_result.get("digit", "")
                if digit and not digit.isdigit():
                    validation_result["validation"]["is_valid"] = False
                    validation_result["validation"]["warnings"].append("体重非数字")
                    validation_result["validation"]["should_highlight"] = True

            # 身高验证
            elif field_name.startswith("身高"):
                digit = field_result.get("digit", "")
                if digit and not digit.isdigit():
                    validation_result["validation"]["is_valid"] = False
                    validation_result["validation"]["warnings"].append("身高非数字")
                    validation_result["validation"]["should_highlight"] = True

            # 心率验证
            elif field_name == "心率":
                text = field_result.get("text", "")
                if text:
                    # 提取数字
                    import re
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        try:
                            heart_rate = int(numbers[0])
                            if heart_rate < 40 or heart_rate > 200:
                                validation_result["validation"]["warnings"].append("心率值异常")
                                validation_result["validation"]["should_highlight"] = True
                        except ValueError:
                            validation_result["validation"]["warnings"].append("心率格式错误")
                            validation_result["validation"]["should_highlight"] = True

        except Exception as e:
            logger.error(f"数据验证失败: {str(e)}")
            validation_result["validation"]["warnings"].append(f"验证异常: {str(e)}")

        return validation_result

    def check_image_quality(self, image: np.ndarray, image_path: str) -> Dict[str, Any]:
        """
        检查图像质量

        Args:
            image: 输入图像
            image_path: 图像路径

        Returns:
            质量检查结果
        """
        quality_check = {
            "is_valid": True,
            "warnings": [],
            "should_highlight_row": False,
            "image_info": {}
        }

        try:
            height, width = image.shape[:2]
            quality_check["image_info"] = {
                "width": width,
                "height": height,
                "path": image_path
            }

            # 检查图像尺寸
            min_width, min_height = 1000, 1000  # 最小尺寸要求
            expected_width, expected_height = 2480, 3507  # 期望尺寸

            if width < min_width or height < min_height:
                quality_check["is_valid"] = False
                quality_check["warnings"].append(f"图像尺寸过小: {width}x{height}")
                quality_check["should_highlight_row"] = True

            # 检查是否为期望尺寸
            if (width, height) != (expected_width, expected_height):
                quality_check["warnings"].append(f"图像尺寸非标准: {width}x{height}, 期望: {expected_width}x{expected_height}")

            # 检查图像清晰度
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()

            if laplacian_var < 50:  # 清晰度阈值
                quality_check["warnings"].append("图像可能模糊")
                quality_check["should_highlight_row"] = True

            quality_check["image_info"]["sharpness"] = float(laplacian_var)

        except Exception as e:
            logger.error(f"图像质量检查失败: {str(e)}")
            quality_check["is_valid"] = False
            quality_check["warnings"].append(f"质量检查异常: {str(e)}")
            quality_check["should_highlight_row"] = True

        return quality_check
    
    def extract_all_fields(self, image_path: str, align_image: bool = True) -> Dict[str, Any]:
        """
        提取所有字段
        
        Args:
            image_path: 图像路径
            align_image: 是否先进行图像对齐
            
        Returns:
            提取结果
        """
        try:
            if not self.field_config:
                return {"error": "未加载字段配置"}

            # 生成唯一的图像标识符
            import time
            import hashlib
            filename = os.path.basename(image_path)
            timestamp = str(int(time.time() * 1000))  # 毫秒级时间戳
            # 使用文件名和时间戳生成唯一标识符
            unique_id = hashlib.md5(f"{filename}_{timestamp}".encode()).hexdigest()[:8]
            image_identifier = f"{os.path.splitext(filename)[0]}_{unique_id}"

            logger.info(f"处理图像: {filename}, 标识符: {image_identifier}")

            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                return {"error": f"无法读取图像: {image_path}"}

            # 检查图像质量
            quality_check = self.check_image_quality(image, image_path)

            # 图像对齐（可选）
            if align_image:
                aligned_image, align_info = self.aligner.align_image(image_path)
                if aligned_image is not None:
                    image = aligned_image
                    logger.info("使用对齐后的图像进行字段提取")
                else:
                    logger.warning("图像对齐失败，使用原始图像")
            
            # 提取所有字段
            extraction_results = {
                "image_path": image_path,
                "total_fields": len(self.field_config),
                "extracted_fields": {},
                "image_quality": quality_check,
                "summary": {
                    "text_fields": 0,
                    "checkbox_fields": 0,
                    "digit_box_fields": 0,
                    "successful_extractions": 0,
                    "failed_extractions": 0,
                    "validation_warnings": 0
                }
            }
            
            for field_name, field_info in self.field_config.items():
                field_type = field_info["type"]
                region = field_info["region"]
                
                logger.info(f"提取字段: {field_name} ({field_type})")
                
                if field_type == "text_input":
                    result = self.extract_text_field(image, region, field_name, image_identifier)
                    extraction_results["summary"]["text_fields"] += 1
                elif field_type == "checkbox":
                    result = self.extract_checkbox_field(image, region, field_name, extraction_results["extracted_fields"])
                    extraction_results["summary"]["checkbox_fields"] += 1
                elif field_type == "digit_box":
                    result = self.extract_digit_box_field(image, region, field_name, image_identifier)
                    extraction_results["summary"]["digit_box_fields"] += 1
                elif field_type == "digit_large_box":
                    result = self.extract_digit_large_box_field(image, region, field_name, image_identifier)
                    extraction_results["summary"]["digit_box_fields"] += 1
                else:
                    result = {"error": f"未知字段类型: {field_type}"}
                
                # 添加字段信息
                result["field_name"] = field_name
                result["field_type"] = field_type
                result["category"] = field_info.get("category", "unknown")

                # 数据验证
                if "error" not in result:
                    result = self.validate_extracted_data(field_name, result)
                    if result.get("validation", {}).get("warnings"):
                        extraction_results["summary"]["validation_warnings"] += 1

                extraction_results["extracted_fields"][field_name] = result
                
                # 统计成功/失败
                if "error" not in result:
                    extraction_results["summary"]["successful_extractions"] += 1
                else:
                    extraction_results["summary"]["failed_extractions"] += 1
            
            # 处理组合字段
            extraction_results = self._process_combined_fields(extraction_results)

            logger.info(f"字段提取完成: 成功 {extraction_results['summary']['successful_extractions']}, "
                       f"失败 {extraction_results['summary']['failed_extractions']}, "
                       f"验证警告 {extraction_results['summary']['validation_warnings']}")

            return extraction_results
            
        except Exception as e:
            logger.error(f"字段提取失败: {str(e)}")
            return {"error": str(e)}

    def _process_combined_fields(self, extraction_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理组合字段（如出生日期、体重、身高、血压）

        Args:
            extraction_results: 提取结果

        Returns:
            处理后的结果
        """
        try:
            extracted_fields = extraction_results["extracted_fields"]
            combined_fields = {}

            # 处理出生日期 (8个数字框组合)
            birth_date_digits = []
            for i in range(1, 9):
                field_name = f"出生日期{i}"
                if field_name in extracted_fields:
                    digit = extracted_fields[field_name].get("digit", "")
                    birth_date_digits.append(digit if digit else "X")

            if birth_date_digits:
                # 格式化为 YYYY/MM/DD
                if len(birth_date_digits) >= 8:
                    year = "".join(birth_date_digits[:4])
                    month = "".join(birth_date_digits[4:6])
                    day = "".join(birth_date_digits[6:8])
                    birth_date = f"{year}/{month}/{day}"

                    # 验证日期合理性
                    warnings = []
                    if "X" in birth_date:
                        warnings.append("出生日期包含空值")

                    try:
                        from datetime import datetime
                        if "X" not in birth_date:
                            datetime.strptime(birth_date, "%Y/%m/%d")
                    except ValueError:
                        warnings.append("出生日期格式无效")

                    combined_fields["出生日期"] = {
                        "value": birth_date,
                        "raw_digits": birth_date_digits,
                        "validation": {
                            "warnings": warnings,
                            "should_highlight": len(warnings) > 0
                        }
                    }

            # 处理体重 (4个数字框组合)
            weight_digits = []
            for i in range(1, 5):
                field_name = f"体重{i}"
                if field_name in extracted_fields:
                    digit = extracted_fields[field_name].get("digit", "")
                    weight_digits.append(digit if digit else "X")

            if weight_digits:
                # 格式化为 XXX.X kg
                if len(weight_digits) >= 4:
                    weight_str = "".join(weight_digits[:3]) + "." + weight_digits[3]

                    warnings = []
                    if "X" in weight_str:
                        warnings.append("体重包含空值")

                    try:
                        if "X" not in weight_str:
                            weight_val = float(weight_str)
                            if weight_val < 20 or weight_val > 200:
                                warnings.append("体重值异常")
                    except ValueError:
                        warnings.append("体重格式错误")

                    combined_fields["体重"] = {
                        "value": weight_str + " kg",
                        "raw_digits": weight_digits,
                        "validation": {
                            "warnings": warnings,
                            "should_highlight": len(warnings) > 0
                        }
                    }

            # 处理身高 (3个数字框组合)
            height_digits = []
            for i in range(1, 4):
                field_name = f"身高{i}"
                if field_name in extracted_fields:
                    digit = extracted_fields[field_name].get("digit", "")
                    height_digits.append(digit if digit else "X")

            if height_digits:
                # 格式化为 XXX cm
                if len(height_digits) >= 3:
                    height_str = "".join(height_digits)

                    warnings = []
                    if "X" in height_str:
                        warnings.append("身高包含空值")

                    try:
                        if "X" not in height_str:
                            height_val = int(height_str)
                            if height_val < 100 or height_val > 250:
                                warnings.append("身高值异常")
                    except ValueError:
                        warnings.append("身高格式错误")

                    combined_fields["身高"] = {
                        "value": height_str + " cm",
                        "raw_digits": height_digits,
                        "validation": {
                            "warnings": warnings,
                            "should_highlight": len(warnings) > 0
                        }
                    }

            # 处理血压 (2个数字框组合)
            systolic = extracted_fields.get("血压收缩压", {}).get("digit", "")
            diastolic = extracted_fields.get("血压舒张压", {}).get("digit", "")

            if systolic or diastolic:
                systolic_str = systolic if systolic else "X"
                diastolic_str = diastolic if diastolic else "X"
                blood_pressure = f"{systolic_str}/{diastolic_str} mmHg"

                warnings = []
                if "X" in blood_pressure:
                    warnings.append("血压包含空值")

                combined_fields["血压"] = {
                    "value": blood_pressure,
                    "systolic": systolic_str,
                    "diastolic": diastolic_str,
                    "validation": {
                        "warnings": warnings,
                        "should_highlight": len(warnings) > 0
                    }
                }

            # 添加组合字段到结果中
            extraction_results["combined_fields"] = combined_fields

            return extraction_results

        except Exception as e:
            logger.error(f"组合字段处理失败: {str(e)}")
            extraction_results["combined_fields"] = {}
            return extraction_results
    
    def save_extraction_results(self, results: Dict[str, Any], output_path: str):
        """
        保存提取结果
        
        Args:
            results: 提取结果
            output_path: 输出文件路径
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"提取结果已保存: {output_path}")
            
        except Exception as e:
            logger.error(f"保存提取结果失败: {str(e)}")
    
    def create_extraction_report(self, results: Dict[str, Any]) -> str:
        """
        创建提取报告

        Args:
            results: 提取结果

        Returns:
            报告文本
        """
        if "error" in results:
            return f"提取失败: {results['error']}"

        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("医疗表单字段提取报告")
        report_lines.append("=" * 60)
        report_lines.append(f"图像文件: {results['image_path']}")
        report_lines.append(f"总字段数: {results['total_fields']}")
        report_lines.append(f"成功提取: {results['summary']['successful_extractions']}")
        report_lines.append(f"提取失败: {results['summary']['failed_extractions']}")
        report_lines.append(f"验证警告: {results['summary']['validation_warnings']}")
        report_lines.append(f"文本字段: {results['summary']['text_fields']}")
        report_lines.append(f"复选框字段: {results['summary']['checkbox_fields']}")
        report_lines.append(f"数字框字段: {results['summary']['digit_box_fields']}")

        # 图像质量信息
        if "image_quality" in results:
            quality = results["image_quality"]
            if quality["should_highlight_row"]:
                report_lines.append("⚠️  图像质量警告: " + ", ".join(quality["warnings"]))
            else:
                report_lines.append("✅ 图像质量: 正常")

        report_lines.append("")

        # 按分类组织结果
        categories = {}
        for field_name, field_result in results["extracted_fields"].items():
            category = field_result.get("category", "其他")
            if category not in categories:
                categories[category] = []
            categories[category].append((field_name, field_result))

        # 定义分类顺序
        category_order = ["基本信息", "危险因素及既往病史", "用药情况", "其他"]

        for category in category_order:
            if category in categories:
                fields = categories[category]
                report_lines.append(f"【{category}】")
                report_lines.append("-" * 40)

                for field_name, field_result in fields:
                    field_type = field_result["field_type"]

                    # 检查是否需要标红
                    validation = field_result.get("validation", {})
                    should_highlight = validation.get("should_highlight", False)
                    warnings = validation.get("warnings", [])

                    if field_type == "text_input":
                        text = field_result.get("text", "")
                        confidence = field_result.get("confidence", 0)
                        prefix = "🔴 " if should_highlight else "  "
                        report_lines.append(f"{prefix}{field_name}: {text} (置信度: {confidence:.2f})")
                    elif field_type == "checkbox":
                        checked = field_result.get("checked", False)
                        confidence = field_result.get("confidence", 0)
                        method = field_result.get("detection_method", "")
                        status = "☑" if checked else "☐"
                        prefix = "🔴 " if should_highlight else "  "
                        report_lines.append(f"{prefix}{field_name}: {status} (置信度: {confidence:.2f}, 方法: {method})")
                    elif field_type == "digit_box":
                        digit = field_result.get("digit", "")
                        confidence = field_result.get("confidence", 0)
                        is_empty = field_result.get("is_empty", False)
                        prefix = "🔴 " if should_highlight else "  "
                        empty_note = " [空]" if is_empty else ""
                        report_lines.append(f"{prefix}{field_name}: {digit}{empty_note} (置信度: {confidence:.2f})")

                    # 显示验证警告
                    if warnings:
                        for warning in warnings:
                            report_lines.append(f"    ⚠️  {warning}")

                    if "error" in field_result:
                        report_lines.append(f"    ❌ 错误: {field_result['error']}")

                report_lines.append("")

        # 显示组合字段
        if "combined_fields" in results and results["combined_fields"]:
            report_lines.append("【组合字段结果】")
            report_lines.append("-" * 40)

            for field_name, field_info in results["combined_fields"].items():
                value = field_info.get("value", "")
                validation = field_info.get("validation", {})
                warnings = validation.get("warnings", [])
                should_highlight = validation.get("should_highlight", False)

                prefix = "🔴 " if should_highlight else "  "
                report_lines.append(f"{prefix}{field_name}: {value}")

                # 显示原始数据
                if "raw_digits" in field_info:
                    raw_data = " ".join(field_info["raw_digits"])
                    report_lines.append(f"    原始数据: [{raw_data}]")

                # 显示验证警告
                if warnings:
                    for warning in warnings:
                        report_lines.append(f"    ⚠️  {warning}")

            report_lines.append("")

        return "\n".join(report_lines)


def main():
    """主函数 - 用于测试"""
    # 创建一个示例配置用于测试
    sample_config = {
        "calibrated_regions": {
            "姓名输入框": {
                "category": "基本信息",
                "type": "text_input",
                "color": "red",
                "region": [37, 314, 111, 55]
            },
            "性别男": {
                "category": "基本信息", 
                "type": "checkbox",
                "color": "blue",
                "region": [200, 600, 30, 30]
            }
        }
    }
    
    # 保存示例配置
    with open("sample_field_config.json", 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, ensure_ascii=False, indent=2)
    
    # 测试字段提取
    extractor = FieldExtractor("sample_field_config.json")
    
    test_image = "debug_output/aligned_SCAN0018_p1_img1.png"
    if os.path.exists(test_image):
        results = extractor.extract_all_fields(test_image, align_image=False)
        
        # 生成报告
        report = extractor.create_extraction_report(results)
        print(report)
        
        # 保存结果
        extractor.save_extraction_results(results, "extraction_results.json")
    else:
        print(f"测试图像不存在: {test_image}")


if __name__ == "__main__":
    main()
