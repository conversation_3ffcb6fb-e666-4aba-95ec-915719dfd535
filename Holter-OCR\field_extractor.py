"""
Field Extraction Module for Holter PDF Reports
Extracts specific medical data fields from OCR text and maps to Excel columns
"""

import re
import os
import logging
from typing import Dict, List, Optional, Any
import pandas as pd


class HolterFieldExtractor:
    """Extracts specific fields from Holter report OCR text"""
    
    def __init__(self):
        """Initialize the field extractor with pattern mappings"""
        self.logger = logging.getLogger(__name__)
        
        # Define the Excel column headers and their corresponding extraction patterns
        self.field_patterns = {
            '姓名': [
                r'姓名[：:]\s*([^\s]+)',
                r'姓名\s*([^\s]+)'
            ],
            '总心搏数': [
                r'总心搏数[：:]\s*(\d+)次?',
                r'总心搏数\s*(\d+)次?'
            ],
            'RR间期＞2.0s次数': [
                r'RR间期[>＞]\s*2\.0秒[，,]\s*共\s*(\d+)次',
                r'RR间期[>＞]2\.0s[，,]\s*共\s*(\d+)次',
                r'RR间期[>＞]\s*2\.0秒.*?(\d+)次'
            ],
            '最长RR间期(s)': [
                r'最长RR间期[：:]\s*(\d+\.?\d*)秒',
                r'最长RR间期\s*(\d+\.?\d*)秒'
            ],
            '平均心率（bpm）': [
                r'平均心率[：:]\s*(\d+)次/分',
                r'平均心率\s*(\d+)次/分'
            ],
            '最快心率（bpm）': [
                r'最快心率[：:]\s*(\d+)次/分',
                r'最快心率\s*(\d+)次/分'
            ],
            '最慢心率（bpm）': [
                r'最慢心率[：:]\s*(\d+)次/分',
                r'最慢心率\s*(\d+)次/分'
            ],
            '窦性心搏总数': [
                r'窦性心搏总数[：:]\s*(\d+)次?',
                r'窦性心搏总数\s*(\d+)次?'
            ],
            '窦性平均心率（bpm）': [
                r'窦性平均心率[：:]\s*(\d+)次/分',
                r'窦性平均心率\s*(\d+)次/分'
            ],
            '窦性最快心率（bpm）': [
                r'窦性最快心率[：:]\s*(\d+)次/分',
                r'窦性最快心率\s*(\d+)次/分'
            ],
            '窦性最慢心率（bpm）': [
                r'窦性最慢心率[：:]\s*(\d+)次/分',
                r'窦性最慢心率\s*(\d+)次/分'
            ],
            '房早总数': [
                r'房早总数[：:]\s*(\d+)次',
                r'房早总数\s*(\d+)次'
            ],
            '单发': [
                r'单发[：:]\s*(\d+)次',
                r'单发\s*(\d+)次'
            ],
            '成对': [
                r'成对[：:]\s*(\d+)对',
                r'成对\s*(\d+)对'
            ],
            '二联律': [
                r'二联律[：:]\s*(\d+)',
                r'二联律\s*(\d+)'
            ],
            '三联律': [
                r'三联律[：:]\s*(\d+)',
                r'三联律\s*(\d+)'
            ],
            '房早未下传': [
                r'房早未下传[：:]\s*(\d+)',
                r'房早未下传\s*(\d+)'
            ],
            '室早总数': [
                r'室早总数[：:]\s*(\d+)次',
                r'室早总数\s*(\d+)次'
            ],
            'SDNN': [
                r'SDNN[：:]\s*(\d+)ms',
                r'SDNN\s*(\d+)ms'
            ],
            'SDANN': [
                r'SDANN\s*[：:]\s*(\d+)ms',
                r'SDANN\s*(\d+)ms'
            ],
            'SDNNIndex': [
                r'SDNNIndex[：:]\s*(\d+)ms',
                r'SDNNIndex\s*(\d+)ms'
            ],
            'rMSSD': [
                r'rMSSD[：:]\s*(\d+)ms',
                r'MSSD[：:]\s*(\d+)ms',  # Sometimes abbreviated
                r'rMSSD\s*(\d+)ms'
            ],
            'pNN50': [
                r'pNN50[：:]\s*(\d+\.?\d*)%',
                r'pNN50\s*(\d+\.?\d*)%'
            ],
            '三角指数': [
                r'三角指数[：:]\s*(\d+\.?\d*)',
                r'三角指数\s*(\d+\.?\d*)'
            ]
        }
        
        # Additional patterns for fields that might have different names in reports
        self.alternative_patterns = {
            '单发.1': '单发',  # For 室早单发
            '成对.1': '成对',  # For 室早成对
            '二联律.1': '二联律',  # For 室早二联律
            '三联律.1': '三联律',  # For 室早三联律
            '室早未下传': '室早未下传',
            '心率变异性': 'SDNN'  # General HRV indicator
        }
    
    def extract_field_value(self, text: str, field_name: str) -> Optional[str]:
        """
        Extract a specific field value from text using regex patterns
        
        Args:
            text: Full text content
            field_name: Name of field to extract
            
        Returns:
            Extracted value as string or None if not found
        """
        patterns = self.field_patterns.get(field_name, [])
        
        for pattern in patterns:
            try:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    value = match.group(1).strip()
                    self.logger.debug(f"Found {field_name}: {value}")
                    return value
            except Exception as e:
                self.logger.warning(f"Error matching pattern {pattern} for {field_name}: {e}")
        
        return None
    
    def extract_name_from_filename(self, filename: str) -> str:
        """
        Extract patient name from PDF filename
        
        Args:
            filename: PDF filename
            
        Returns:
            Patient name
        """
        # Remove .pdf extension and path
        name = os.path.splitext(os.path.basename(filename))[0]
        return name
    
    def extract_all_fields(self, text: str, filename: str = None) -> Dict[str, Any]:
        """
        Extract all fields from OCR text
        
        Args:
            text: Full OCR text content
            filename: PDF filename for name extraction
            
        Returns:
            Dictionary with field names as keys and extracted values
        """
        extracted_data = {}
        
        # Extract name from filename if provided
        if filename:
            extracted_data['姓名'] = self.extract_name_from_filename(filename)
        
        # Extract all other fields
        for field_name in self.field_patterns.keys():
            if field_name != '姓名':  # Skip name as we get it from filename
                value = self.extract_field_value(text, field_name)
                extracted_data[field_name] = value
        
        # Handle alternative field mappings
        for alt_field, base_field in self.alternative_patterns.items():
            if alt_field not in extracted_data and base_field in extracted_data:
                extracted_data[alt_field] = extracted_data[base_field]
        
        return extracted_data
    
    def clean_and_validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean and validate extracted data
        
        Args:
            data: Raw extracted data
            
        Returns:
            Cleaned and validated data
        """
        cleaned_data = {}
        
        for field, value in data.items():
            if value is None:
                cleaned_data[field] = None
                continue
            
            # Convert numeric fields
            if field in ['总心搏数', 'RR间期＞2.0s次数', '平均心率（bpm）', '最快心率（bpm）', 
                        '最慢心率（bpm）', '窦性心搏总数', '窦性平均心率（bpm）', '窦性最快心率（bpm）',
                        '窦性最慢心率（bpm）', '房早总数', '单发', '成对', '二联律', '三联律', 
                        '房早未下传', '室早总数', '单发.1', '成对.1', '二联律.1', '三联律.1', 
                        '室早未下传', 'SDNN', 'SDANN', 'SDNNIndex', 'rMSSD']:
                try:
                    cleaned_data[field] = int(value) if value.isdigit() else None
                except (ValueError, AttributeError):
                    cleaned_data[field] = None
            
            # Convert float fields
            elif field in ['最长RR间期(s)', 'pNN50', '三角指数']:
                try:
                    cleaned_data[field] = float(value)
                except (ValueError, AttributeError):
                    cleaned_data[field] = None
            
            # Keep string fields as is
            else:
                cleaned_data[field] = str(value).strip() if value else None
        
        return cleaned_data
    
    def get_excel_columns(self) -> List[str]:
        """
        Get the list of Excel column headers
        
        Returns:
            List of column names matching Excel template
        """
        return [
            '姓名', '总心搏数', 'RR间期＞2.0s次数', '最长RR间期(s)', '平均心率（bpm）',
            '最快心率（bpm）', '最慢心率（bpm）', '窦性心搏总数', '窦性平均心率（bpm）',
            '窦性最快心率（bpm）', '窦性最慢心率（bpm）', '房早总数', '单发', '成对',
            '二联律', '三联律', '房早未下传', '室早总数', '单发.1', '成对.1', '二联律.1',
            '三联律.1', '室早未下传', '心率变异性', 'SDNN', 'SDANN', 'SDNNIndex',
            'rMSSD', 'pNN50', '三角指数'
        ]


if __name__ == "__main__":
    # Test the field extractor
    import os
    
    extractor = HolterFieldExtractor()
    
    # Test with sample text
    sample_text = """
    姓名：丁刚
    总心搏数：92787次
    RR间期>2.0秒，共0次
    最长RR间期：1.669秒
    平均心率：65次/分
    最快心率：157次/分
    最慢心率：47次/分
    窦性心搏总数：92641次
    窦性平均心率：65次/分
    窦性最快心率：106次/分
    窦性最慢心率：47次/分
    房早总数：106次
    单发：47次
    成对：1对
    二联律：0
    三联律：0
    房早未下传：0
    室早总数：40次
    单发：38次
    成对：1对
    SDNN: 141ms
    SDANN : 127ms
    SDNNIndex:50ms
    rMSSD: 17ms
    pNN50:1.82%
    三角指数：47.38
    """
    
    extracted = extractor.extract_all_fields(sample_text, "丁刚.pdf")
    cleaned = extractor.clean_and_validate_data(extracted)
    
    print("Extracted fields:")
    for field, value in cleaned.items():
        if value is not None:
            print(f"{field}: {value}")
