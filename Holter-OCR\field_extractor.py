"""
Field Extraction Module for Holter PDF Reports
Extracts specific medical data fields from OCR text and maps to Excel columns
"""

import re
import os
import logging
from typing import Dict, List, Optional, Any
import pandas as pd


class HolterFieldExtractor:
    """Extracts specific fields from Holter report OCR text"""
    
    def __init__(self):
        """Initialize the field extractor with pattern mappings"""
        self.logger = logging.getLogger(__name__)
        
        # Define the Excel column headers and their corresponding extraction patterns
        # Enhanced patterns with more flexibility and alternatives
        self.field_patterns = {
            '姓名': [
                r'姓名[：:]\s*([^\s\n]+)',
                r'姓名\s*([^\s\n]+)',
                r'患者[：:]?\s*([^\s\n]+)',
                r'病人[：:]?\s*([^\s\n]+)'
            ],
            '总心搏数': [
                r'总心搏数[：:]\s*(\d+)次?',
                r'总心搏数\s*(\d+)次?',
                r'心搏总数[：:]\s*(\d+)次?',
                r'总心搏[：:]\s*(\d+)次?',
                r'心搏数[：:]\s*(\d+)次?',
                r'总搏动[：:]\s*(\d+)次?'
            ],
            'RR间期＞2.0s次数': [
                r'RR间期[>＞]\s*2\.0秒[，,]?\s*共\s*(\d+)次',
                r'RR间期[>＞]2\.0s[，,]?\s*共\s*(\d+)次',
                r'RR间期[>＞]\s*2\.0秒.*?(\d+)次',
                r'RR[>＞]2\.0秒.*?(\d+)次',
                r'RR[>＞]2\.0s.*?(\d+)次',
                r'RR间期大于2\.0秒.*?(\d+)次',
                r'RR间期超过2\.0秒.*?(\d+)次'
            ],
            '最长RR间期(s)': [
                r'最长RR间期[：:]\s*(\d+\.?\d*)秒',
                r'最长RR间期\s*(\d+\.?\d*)秒',
                r'最长RR[：:]\s*(\d+\.?\d*)秒',
                r'RR间期最长[：:]\s*(\d+\.?\d*)秒',
                r'最大RR间期[：:]\s*(\d+\.?\d*)秒'
            ],
            '平均心率（bpm）': [
                r'平均心率[：:]\s*(\d+)次[/／]分',
                r'平均心率\s*(\d+)次[/／]分',
                r'心率平均[：:]\s*(\d+)次[/／]分',
                r'平均心率[：:]\s*(\d+)bpm',
                r'平均心率[：:]\s*(\d+)\s*次',
                r'平均[：:]\s*(\d+)次[/／]分'
            ],
            '最快心率（bpm）': [
                r'最快心率[：:]\s*(\d+)次[/／]分',
                r'最快心率\s*(\d+)次[/／]分',
                r'最高心率[：:]\s*(\d+)次[/／]分',
                r'心率最快[：:]\s*(\d+)次[/／]分',
                r'最大心率[：:]\s*(\d+)次[/／]分',
                r'最快[：:]\s*(\d+)次[/／]分'
            ],
            '最慢心率（bpm）': [
                r'最慢心率[：:]\s*(\d+)次[/／]分',
                r'最慢心率\s*(\d+)次[/／]分',
                r'最低心率[：:]\s*(\d+)次[/／]分',
                r'心率最慢[：:]\s*(\d+)次[/／]分',
                r'最小心率[：:]\s*(\d+)次[/／]分',
                r'最慢[：:]\s*(\d+)次[/／]分'
            ],
            '窦性心搏总数': [
                r'窦性心搏总数[：:]\s*(\d+)次?',
                r'窦性心搏总数\s*(\d+)次?',
                r'窦性搏动总数[：:]\s*(\d+)次?',
                r'窦性心搏[：:]\s*(\d+)次?',
                r'窦律心搏[：:]\s*(\d+)次?'
            ],
            '窦性平均心率（bpm）': [
                r'窦性平均心率[：:]\s*(\d+)次[/／]分',
                r'窦性平均心率\s*(\d+)次[/／]分',
                r'窦律平均心率[：:]\s*(\d+)次[/／]分',
                r'窦性心率平均[：:]\s*(\d+)次[/／]分'
            ],
            '窦性最快心率（bpm）': [
                r'窦性最快心率[：:]\s*(\d+)次[/／]分',
                r'窦性最快心率\s*(\d+)次[/／]分',
                r'窦律最快心率[：:]\s*(\d+)次[/／]分',
                r'窦性最高心率[：:]\s*(\d+)次[/／]分'
            ],
            '窦性最慢心率（bpm）': [
                r'窦性最慢心率[：:]\s*(\d+)次[/／]分',
                r'窦性最慢心率\s*(\d+)次[/／]分',
                r'窦律最慢心率[：:]\s*(\d+)次[/／]分',
                r'窦性最低心率[：:]\s*(\d+)次[/／]分'
            ],
            '房早总数': [
                r'房早总数[：:]\s*(\d+)次',
                r'房早总数\s*(\d+)次',
                r'房性早搏总数[：:]\s*(\d+)次',
                r'房早[：:]\s*(\d+)次',
                r'房性早搏[：:]\s*(\d+)次'
            ],
            '单发': [
                r'单发[：:]\s*(\d+)次',
                r'单发\s*(\d+)次',
                r'单个[：:]\s*(\d+)次',
                r'孤立性[：:]\s*(\d+)次'
            ],
            '成对': [
                r'成对[：:]\s*(\d+)对',
                r'成对\s*(\d+)对',
                r'成双[：:]\s*(\d+)对',
                r'配对[：:]\s*(\d+)对',
                r'成对[：:]\s*(\d+)次'
            ],
            '二联律': [
                r'二联律[：:]\s*(\d+)',
                r'二联律\s*(\d+)',
                r'双联律[：:]\s*(\d+)',
                r'2联律[：:]\s*(\d+)'
            ],
            '三联律': [
                r'三联律[：:]\s*(\d+)',
                r'三联律\s*(\d+)',
                r'3联律[：:]\s*(\d+)',
                r'三重律[：:]\s*(\d+)'
            ],
            '房早未下传': [
                r'房早未下传[：:]\s*(\d+)',
                r'房早未下传\s*(\d+)',
                r'未下传房早[：:]\s*(\d+)',
                r'房早阻滞[：:]\s*(\d+)'
            ],
            '室早总数': [
                r'室早总数[：:]\s*(\d+)次',
                r'室早总数\s*(\d+)次',
                r'室性早搏总数[：:]\s*(\d+)次',
                r'室早[：:]\s*(\d+)次',
                r'室性早搏[：:]\s*(\d+)次'
            ],
            'SDNN': [
                r'SDNN[：:]\s*(\d+)ms',
                r'SDNN\s*(\d+)ms',
                r'SDNN[：:]\s*(\d+)',
                r'SDNN\s*(\d+)',
                r'标准差[：:]\s*(\d+)ms'
            ],
            'SDANN': [
                r'SDANN\s*[：:]\s*(\d+)ms',
                r'SDANN\s*(\d+)ms',
                r'SDANN[：:]\s*(\d+)',
                r'SDANN\s*(\d+)'
            ],
            'SDNNIndex': [
                r'SDNNIndex[：:]\s*(\d+)ms',
                r'SDNNIndex\s*(\d+)ms',
                r'SDNNIndex[：:]\s*(\d+)',
                r'SDNNIndex\s*(\d+)',
                r'SDNN指数[：:]\s*(\d+)'
            ],
            'rMSSD': [
                r'rMSSD[：:]\s*(\d+)ms',
                r'MSSD[：:]\s*(\d+)ms',
                r'rMSSD\s*(\d+)ms',
                r'MSSD\s*(\d+)ms',
                r'rMSSD[：:]\s*(\d+)',
                r'MSSD[：:]\s*(\d+)'
            ],
            'pNN50': [
                r'pNN50[：:]\s*(\d+\.?\d*)%',
                r'pNN50\s*(\d+\.?\d*)%',
                r'pNN50[：:]\s*(\d+\.?\d*)',
                r'pNN50\s*(\d+\.?\d*)',
                r'NN50百分比[：:]\s*(\d+\.?\d*)%'
            ],
            '三角指数': [
                r'三角指数[：:]\s*(\d+\.?\d*)',
                r'三角指数\s*(\d+\.?\d*)',
                r'三角形指数[：:]\s*(\d+\.?\d*)',
                r'HRV三角指数[：:]\s*(\d+\.?\d*)'
            ]
        }
        
        # Additional patterns for fields that might have different names in reports
        self.alternative_patterns = {
            '单发.1': '单发',  # For 室早单发
            '成对.1': '成对',  # For 室早成对
            '二联律.1': '二联律',  # For 室早二联律
            '三联律.1': '三联律',  # For 室早三联律
            '室早未下传': '室早未下传',
            '心率变异性': 'SDNN'  # General HRV indicator
        }
    
    def extract_field_value(self, text: str, field_name: str) -> Optional[str]:
        """
        Extract a specific field value from text using regex patterns
        
        Args:
            text: Full text content
            field_name: Name of field to extract
            
        Returns:
            Extracted value as string or None if not found
        """
        patterns = self.field_patterns.get(field_name, [])
        
        for pattern in patterns:
            try:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    value = match.group(1).strip()
                    self.logger.debug(f"Found {field_name}: {value}")
                    return value
            except Exception as e:
                self.logger.warning(f"Error matching pattern {pattern} for {field_name}: {e}")
        
        return None
    
    def extract_name_from_filename(self, filename: str) -> str:
        """
        Extract patient name from PDF filename
        
        Args:
            filename: PDF filename
            
        Returns:
            Patient name
        """
        # Remove .pdf extension and path
        name = os.path.splitext(os.path.basename(filename))[0]
        return name
    
    def extract_report_conclusion(self, text: str) -> Optional[str]:
        """
        Extract report conclusion/diagnosis from OCR text

        Args:
            text: Full OCR text content

        Returns:
            Report conclusion text or None if not found
        """
        conclusion_patterns = [
            # Pattern 1: 结论: followed by content
            r'结论[：:]\s*(.*?)(?=报告医师|报告日期|$)',
            # Pattern 2: 报告结论 followed by content
            r'报告结论[：:]?\s*(.*?)(?=报告医师|报告日期|$)',
            # Pattern 3: 诊断结论 followed by content
            r'诊断结论[：:]?\s*(.*?)(?=报告医师|报告日期|$)',
            # Pattern 4: 临床诊断 followed by content
            r'临床诊断[：:]?\s*(.*?)(?=报告医师|报告日期|$)',
            # Pattern 5: Multiple numbered conclusions
            r'结论[：:]?\s*\n?\s*(\d+\..*?)(?=报告医师|报告日期|$)',
            # Pattern 6: Conclusion after summary section
            r'(?:全天为|可见|偶发).*?(?=报告医师|报告日期|$)'
        ]

        for pattern in conclusion_patterns:
            try:
                match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
                if match:
                    conclusion = match.group(1).strip()
                    # Clean up the conclusion text
                    conclusion = re.sub(r'\s+', ' ', conclusion)  # Replace multiple spaces/newlines
                    conclusion = conclusion.replace('（本报告仅反映该受检者本次受检时情况，重要资料遗失不补。', '')
                    conclusion = conclusion.strip()

                    if len(conclusion) > 10:  # Only return if substantial content
                        self.logger.debug(f"Found conclusion: {conclusion[:50]}...")
                        return conclusion
            except Exception as e:
                self.logger.warning(f"Error matching conclusion pattern {pattern}: {e}")

        return None

    def extract_all_fields(self, text: str, filename: str = None) -> Dict[str, Any]:
        """
        Extract all fields from OCR text including report conclusion

        Args:
            text: Full OCR text content
            filename: PDF filename for name extraction

        Returns:
            Dictionary with field names as keys and extracted values
        """
        extracted_data = {}

        # Extract name from filename if provided
        if filename:
            extracted_data['姓名'] = self.extract_name_from_filename(filename)

        # Extract all other fields
        for field_name in self.field_patterns.keys():
            if field_name != '姓名':  # Skip name as we get it from filename
                value = self.extract_field_value(text, field_name)
                extracted_data[field_name] = value

        # Extract report conclusion
        conclusion = self.extract_report_conclusion(text)
        extracted_data['报告结论'] = conclusion

        # Handle alternative field mappings
        for alt_field, base_field in self.alternative_patterns.items():
            if alt_field not in extracted_data and base_field in extracted_data:
                extracted_data[alt_field] = extracted_data[base_field]

        return extracted_data
    
    def clean_and_validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean and validate extracted data
        
        Args:
            data: Raw extracted data
            
        Returns:
            Cleaned and validated data
        """
        cleaned_data = {}
        
        for field, value in data.items():
            if value is None:
                cleaned_data[field] = None
                continue
            
            # Convert numeric fields
            if field in ['总心搏数', 'RR间期＞2.0s次数', '平均心率（bpm）', '最快心率（bpm）', 
                        '最慢心率（bpm）', '窦性心搏总数', '窦性平均心率（bpm）', '窦性最快心率（bpm）',
                        '窦性最慢心率（bpm）', '房早总数', '单发', '成对', '二联律', '三联律', 
                        '房早未下传', '室早总数', '单发.1', '成对.1', '二联律.1', '三联律.1', 
                        '室早未下传', 'SDNN', 'SDANN', 'SDNNIndex', 'rMSSD']:
                try:
                    cleaned_data[field] = int(value) if value.isdigit() else None
                except (ValueError, AttributeError):
                    cleaned_data[field] = None
            
            # Convert float fields
            elif field in ['最长RR间期(s)', 'pNN50', '三角指数']:
                try:
                    cleaned_data[field] = float(value)
                except (ValueError, AttributeError):
                    cleaned_data[field] = None
            
            # Keep string fields as is
            else:
                cleaned_data[field] = str(value).strip() if value else None
        
        return cleaned_data
    
    def get_excel_columns(self) -> List[str]:
        """
        Get the list of Excel column headers including report conclusion

        Returns:
            List of column names matching Excel template plus conclusion
        """
        return [
            '姓名', '总心搏数', 'RR间期＞2.0s次数', '最长RR间期(s)', '平均心率（bpm）',
            '最快心率（bpm）', '最慢心率（bpm）', '窦性心搏总数', '窦性平均心率（bpm）',
            '窦性最快心率（bpm）', '窦性最慢心率（bpm）', '房早总数', '单发', '成对',
            '二联律', '三联律', '房早未下传', '室早总数', '单发.1', '成对.1', '二联律.1',
            '三联律.1', '室早未下传', '心率变异性', 'SDNN', 'SDANN', 'SDNNIndex',
            'rMSSD', 'pNN50', '三角指数', '报告结论'
        ]


if __name__ == "__main__":
    # Test the field extractor
    import os
    
    extractor = HolterFieldExtractor()
    
    # Test with sample text
    sample_text = """
    姓名：丁刚
    总心搏数：92787次
    RR间期>2.0秒，共0次
    最长RR间期：1.669秒
    平均心率：65次/分
    最快心率：157次/分
    最慢心率：47次/分
    窦性心搏总数：92641次
    窦性平均心率：65次/分
    窦性最快心率：106次/分
    窦性最慢心率：47次/分
    房早总数：106次
    单发：47次
    成对：1对
    二联律：0
    三联律：0
    房早未下传：0
    室早总数：40次
    单发：38次
    成对：1对
    SDNN: 141ms
    SDANN : 127ms
    SDNNIndex:50ms
    rMSSD: 17ms
    pNN50:1.82%
    三角指数：47.38
    """
    
    extracted = extractor.extract_all_fields(sample_text, "丁刚.pdf")
    cleaned = extractor.clean_and_validate_data(extracted)
    
    print("Extracted fields:")
    for field, value in cleaned.items():
        if value is not None:
            print(f"{field}: {value}")
