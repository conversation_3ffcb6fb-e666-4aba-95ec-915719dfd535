import openpyxl
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter
import pandas as pd
from configparser import ConfigParser


class ExcelHandler:
    def __init__(self, config: ConfigParser):
        self.config = config
        self.header_file = config.get('DEFAULT', 'excel_header_path')
        self.output_file = config.get('DEFAULT', 'output_excel_path')
        self.header_data = None
        self.output_data = []

    def load_header(self):
        """加载表头定义文件"""
        try:
            # 使用 pandas 读取 Excel 文件
            df = pd.read_excel(self.header_file, sheet_name='信息表', header=None)
            
            self.header_data = {
                'categories': df.iloc[0].tolist(),  # 第一行作为大类
                'fields': df.iloc[1].tolist()       # 第二行作为字段
            }
            
            return self.header_data
            
        except Exception as e:
            raise Exception(f"无法加载Excel文件 {self.header_file}: {str(e)}")

    def add_record(self, record_data: dict):
        """添加一条记录数据"""
        # 根据字段顺序整理数据
        ordered_data = []
        for field in self.header_data['fields']:
            ordered_data.append(record_data.get(field, ''))
        
        # 检查是否有相同姓名记录
        name_field = '姓名'
        if name_field in record_data:
            current_name = record_data[name_field]
            for i, existing_data in enumerate(self.output_data):
                existing_name = existing_data[self.header_data['fields'].index(name_field)]
                # 如果姓名相似度超过阈值(3字中有2字相同)
                if self._name_similarity(current_name, existing_name) >= 0.66:
                    # 合并记录，保留更完整的信息
                    for j, field in enumerate(self.header_data['fields']):
                        if not existing_data[j] and ordered_data[j]:
                            existing_data[j] = ordered_data[j]
                    return
        
        self.output_data.append(ordered_data)
        
    def _name_similarity(self, name1: str, name2: str) -> float:
        """计算两个姓名的相似度(0-1)"""
        if not name1 or not name2:
            return 0
            
        # 简单实现: 计算相同字符数/最大长度
        common_chars = len(set(name1) & set(name2))
        max_len = max(len(name1), len(name2))
        return common_chars / max_len if max_len > 0 else 0

    def save_output(self):
        """保存结果到Excel文件"""
        if not self.output_data:
            return
            
        # 日志记录
        import logging
        logger = logging.getLogger('MedicalHelper')
        logger.info(f'准备保存Excel文件，共{len(self.output_data)}条记录')
        
        try:
            # 确保目录存在
            import os
            output_dir = os.path.dirname(self.output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = '信息表'
            
            # 写入表头
            ws.append(self.header_data['categories'])
            ws.append(self.header_data['fields'])
            
            # 写入数据
            for data in self.output_data:
                ws.append(data)
            
            # 设置单元格格式
            for col in range(1, len(self.header_data['fields']) + 1):
                ws.column_dimensions[get_column_letter(col)].width = 20
                for cell in ws[get_column_letter(col)]:
                    cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # 尝试保存文件
            try:
                wb.save(self.output_file)
            except PermissionError as e:
                # 如果权限错误，尝试在当前目录保存
                import os
                fallback_path = os.path.join(os.getcwd(), os.path.basename(self.output_file))
                print(f"警告：无法保存到{self.output_file}，将尝试保存到当前目录: {fallback_path}")
                wb.save(str(fallback_path)+'.xlsx')
                
        except Exception as e:
            raise Exception(f"保存Excel文件失败: {str(e)}")