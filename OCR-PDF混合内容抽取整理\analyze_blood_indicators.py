#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import fitz
import re
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_blood_indicators():
    """分析所有PDF中的血液指标情况"""
    
    input_path = '../files/7.28体检'
    
    if not os.path.exists(input_path):
        logger.error(f'输入目录不存在: {input_path}')
        return
    
    pdf_files = [f for f in os.listdir(input_path) if f.lower().endswith('.pdf')]
    logger.info(f'找到 {len(pdf_files)} 个PDF文件')
    
    tg_count = 0
    hdl_count = 0
    total_count = 0
    
    tg_files = []
    hdl_files = []
    
    for pdf_file in pdf_files:
        pdf_path = os.path.join(input_path, pdf_file)
        total_count += 1
        
        try:
            doc = fitz.open(pdf_path)
            
            # 提取所有文本
            all_text = ""
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()
                all_text += page_text + "\n"
            
            doc.close()
            
            # 检查甘油三酯
            tg_patterns = [
                r'甘油三酯\(TG\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'甘油三酯[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'三酰甘油\(TG\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'(?<!HDL)TG[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'甘油三酯[:\s]*(\d+(?:\.\d+)?)',
                r'(?<!HDL)TG[:\s]*(\d+(?:\.\d+)?)',
                r'TRIG[:\s]*(\d+(?:\.\d+)?)',
                r'甘油三酯\s+(\d+(?:\.\d+)?)\s+mmol/L',
                r'TG\s+(\d+(?:\.\d+)?)\s+mmol/L'
            ]
            
            tg_found = False
            for pattern in tg_patterns:
                match = re.search(pattern, all_text, re.IGNORECASE)
                if match:
                    try:
                        value = float(match.group(1))
                        if 0.1 <= value <= 10.0:
                            tg_count += 1
                            tg_files.append(pdf_file)
                            tg_found = True
                            break
                    except ValueError:
                        continue
            
            # 检查高密度脂蛋白胆固醇
            hdl_patterns = [
                r'高密度脂蛋白胆固醇\(HDL-C\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'高密度脂蛋白胆固醇[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'高密度脂蛋白[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'HDL-C[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'高密度脂蛋白胆固醇[:\s]*(\d+(?:\.\d+)?)',
                r'HDL-C[:\s]*(\d+(?:\.\d+)?)',
                r'(?<!L)HDL(?!C)[:\s]*(\d+(?:\.\d+)?)',
                r'高密度脂蛋白胆固醇\s+(\d+(?:\.\d+)?)\s+mmol/L',
                r'HDL-C\s+(\d+(?:\.\d+)?)\s+mmol/L'
            ]
            
            hdl_found = False
            for pattern in hdl_patterns:
                match = re.search(pattern, all_text, re.IGNORECASE)
                if match:
                    try:
                        value = float(match.group(1))
                        if 0.5 <= value <= 5.0:
                            hdl_count += 1
                            hdl_files.append(pdf_file)
                            hdl_found = True
                            break
                    except ValueError:
                        continue
            
            # 检查是否包含血液检查相关关键词
            blood_keywords = ['血液检查', '血常规', '生化检查', '血脂', '胆固醇', '血糖']
            has_blood_test = any(keyword in all_text for keyword in blood_keywords)
            
            if has_blood_test and not tg_found and not hdl_found:
                # 查看是否有其他血脂指标
                other_lipids = ['总胆固醇', '低密度脂蛋白', 'TC', 'LDL']
                has_other_lipids = any(keyword in all_text for keyword in other_lipids)
                if has_other_lipids:
                    logger.debug(f'{pdf_file}: 有血脂检查但缺少TG/HDL')
            
        except Exception as e:
            logger.error(f'处理 {pdf_file} 时出错: {str(e)}')
    
    print("\n" + "=" * 80)
    print("血液指标分析结果")
    print("=" * 80)
    print(f"总PDF文件数: {total_count}")
    print(f"包含甘油三酯(TG)的PDF数: {tg_count} ({tg_count/total_count*100:.1f}%)")
    print(f"包含高密度脂蛋白胆固醇(HDL-C)的PDF数: {hdl_count} ({hdl_count/total_count*100:.1f}%)")
    
    if tg_files:
        print(f"\n包含甘油三酯的PDF文件:")
        for i, file in enumerate(tg_files[:10], 1):  # 只显示前10个
            print(f"  {i}. {file}")
        if len(tg_files) > 10:
            print(f"  ... 还有 {len(tg_files)-10} 个文件")
    
    if hdl_files:
        print(f"\n包含HDL-C的PDF文件:")
        for i, file in enumerate(hdl_files[:10], 1):  # 只显示前10个
            print(f"  {i}. {file}")
        if len(hdl_files) > 10:
            print(f"  ... 还有 {len(hdl_files)-10} 个文件")
    
    print("=" * 80)

if __name__ == "__main__":
    analyze_blood_indicators()
