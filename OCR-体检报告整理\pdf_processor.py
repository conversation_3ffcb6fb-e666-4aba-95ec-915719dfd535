#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF处理器模块
负责将PDF文件转换为图像，并协调OCR识别和数据提取过程
"""

import os
import logging
import fitz  # PyMuPDF
import cv2
import numpy as np
from typing import List, Dict, Optional, Tuple
from configparser import ConfigParser
from pathlib import Path

from ocr_engine import OCREngine
from excel_handler import ExcelHandler

logger = logging.getLogger('ClinicalExtractor')


class PDFProcessor:
    """PDF处理器"""
    
    def __init__(self, config: ConfigParser, ocr_engine: OCREngine, excel_handler: ExcelHandler):
        self.config = config
        self.ocr_engine = ocr_engine
        self.excel_handler = excel_handler
        
        # 获取配置参数
        self.input_path = config.get('DEFAULT', 'input_pdf_path')
        self.temp_images_path = config.get('DEFAULT', 'temp_images_path')
        
        # 创建临时图片目录
        os.makedirs(self.temp_images_path, exist_ok=True)
        
        # 延迟导入模板匹配器以避免循环导入
        from template_matcher import TemplateMatcher
        self.template_matcher = TemplateMatcher(config)
        self.template_matcher.set_ocr_engine(ocr_engine)
    
    def process_pdfs(self):
        """处理PDF文件"""
        try:
            # 获取PDF文件列表
            pdf_files = self._get_pdf_files()
            logger.info(f'找到 {len(pdf_files)} 个待处理PDF文件')
            
            if not pdf_files:
                logger.warning('未找到任何PDF文件')
                return
            
            # 逐个处理PDF文件
            for pdf_path in pdf_files:
                logger.info(f'开始处理PDF文件: {os.path.basename(pdf_path)}')
                self._process_single_pdf(pdf_path)
            
            logger.info('所有PDF处理完成')
            
        except Exception as e:
            logger.error(f'PDF处理失败: {str(e)}', exc_info=True)
            raise
    
    def _get_pdf_files(self) -> List[str]:
        """获取所有待处理的PDF文件"""
        pdf_files = []
        
        if os.path.isfile(self.input_path):
            # 单个PDF文件
            if self.input_path.lower().endswith('.pdf'):
                pdf_files.append(self.input_path)
            else:
                logger.warning(f'输入文件不是PDF格式: {self.input_path}')
        elif os.path.isdir(self.input_path):
            # 目录中的所有PDF文件
            for file in os.listdir(self.input_path):
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(self.input_path, file))
        else:
            raise FileNotFoundError(f'输入路径不存在: {self.input_path}')
        
        # 按文件名排序
        pdf_files.sort(key=lambda x: os.path.basename(x))
        return pdf_files
    
    def _process_single_pdf(self, pdf_path: str):
        """处理单个PDF文件"""
        try:
            # 提取PDF中的图片
            image_paths = self._extract_images_from_pdf(pdf_path)
            
            if not image_paths:
                logger.warning(f'未能从PDF中提取图片: {pdf_path}')
                return
            
            # 对每个页面进行OCR识别和数据提取
            for page_num, img_path in enumerate(image_paths, 1):
                logger.info(f'处理第 {page_num} 页: {os.path.basename(img_path)}')
                
                # OCR识别
                text_blocks = self.ocr_engine.recognize_text(img_path)
                
                # 数据提取
                extracted_data = self.template_matcher.extract_data(img_path, text_blocks)
                
                # 添加元数据
                extracted_data['PDF文件'] = os.path.basename(pdf_path)
                extracted_data['页码'] = page_num
                extracted_data['图片路径'] = img_path
                
                # 添加到Excel处理器
                if extracted_data:
                    logger.info(f'从第{page_num}页提取到数据: {len([k for k, v in extracted_data.items() if v])}个字段')
                    self.excel_handler.add_record(extracted_data)
                else:
                    logger.warning(f'未能从第{page_num}页提取有效数据')
                    
        except Exception as e:
            logger.error(f'处理PDF文件失败 {pdf_path}: {str(e)}', exc_info=True)
    
    def _extract_images_from_pdf(self, pdf_path: str) -> List[str]:
        """从PDF中提取图片"""
        image_paths = []
        
        try:
            doc = fitz.open(pdf_path)
            pdf_name = Path(pdf_path).stem
            
            logger.info(f'PDF包含 {len(doc)} 页')
            
            for page_index in range(len(doc)):
                page = doc[page_index]
                
                # 记录页面的原始旋转属性
                original_rotation = page.rotation
                logger.debug(f'第{page_index+1}页原始旋转属性: {original_rotation}度')
                
                # 如果页面有旋转，临时将其设为0以正确提取
                if original_rotation != 0:
                    page.set_rotation(0)
                
                # 检查是否有嵌入图片
                image_list = page.get_images(full=True)
                logger.debug(f'第{page_index+1}页包含 {len(image_list)} 张嵌入图片')
                
                if not image_list:
                    # 如果没有嵌入图片，将整个页面渲染为图片
                    img_path = self._render_page_as_image(page, pdf_name, page_index + 1)
                    if img_path:
                        image_paths.append(img_path)
                else:
                    # 提取嵌入图片
                    for img_index, img in enumerate(image_list):
                        img_path = self._extract_embedded_image(doc, img, pdf_name, page_index + 1, img_index + 1)
                        if img_path:
                            image_paths.append(img_path)
                
                # 恢复原始旋转属性
                if original_rotation != 0:
                    page.set_rotation(original_rotation)
            
            doc.close()
            logger.info(f'从PDF中提取了 {len(image_paths)} 张图片')
            return image_paths
            
        except Exception as e:
            logger.error(f'提取PDF图片失败: {str(e)}', exc_info=True)
            return []
    
    def _render_page_as_image(self, page, pdf_name: str, page_num: int) -> Optional[str]:
        """将PDF页面渲染为图片"""
        try:
            # 获取页面的原始旋转角度
            rotation = page.rotation
            logger.debug(f'页面原始旋转角度: {rotation}度')

            # 设置高DPI以获得足够的清晰度
            pix = page.get_pixmap(dpi=300, alpha=False)

            # 获取图像尺寸信息
            width, height = pix.width, pix.height
            logger.debug(f'渲染页面图像尺寸: {width}x{height}')

            # 保存图片
            img_path = os.path.join(self.temp_images_path, f"{pdf_name}_page{page_num}.png")
            pix.save(img_path)

            # 读取保存的图像并检查方向
            check_img = cv2.imread(img_path)
            if check_img is not None:
                h, w = check_img.shape[:2]
                logger.debug(f'保存的图像尺寸: {w}x{h}')

                # 如果图像是横向的（宽>高），可能需要旋转
                if w > h:
                    logger.warning(f'检测到横向图像 ({w}x{h})，可能需要旋转')

                    # 尝试旋转图像使其变为纵向
                    # 逆时针旋转90度
                    rotated_img = cv2.rotate(check_img, cv2.ROTATE_90_COUNTERCLOCKWISE)

                    # 保存旋转后的图像
                    cv2.imwrite(img_path, rotated_img)

                    # 验证旋转后的尺寸
                    final_h, final_w = rotated_img.shape[:2]
                    logger.info(f'图像已旋转: {w}x{h} -> {final_w}x{final_h}')

                return img_path
            else:
                logger.error(f'保存的图像无法读取: {img_path}')
                return None

        except Exception as e:
            logger.error(f'渲染页面图像失败: {str(e)}', exc_info=True)
            return None
    
    def _extract_embedded_image(self, doc, img_info, pdf_name: str, page_num: int, img_num: int) -> Optional[str]:
        """提取嵌入图片"""
        try:
            xref = img_info[0]
            base_image = doc.extract_image(xref)
            image_bytes = base_image["image"]

            # 使用CV2直接解码图像数据
            img_path = os.path.join(self.temp_images_path, f"{pdf_name}_p{page_num}_img{img_num}.png")

            # 使用numpy和cv2直接解码
            nparr = np.frombuffer(image_bytes, np.uint8)
            img_np = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if img_np is not None:
                h, w = img_np.shape[:2]
                logger.debug(f'提取的嵌入图像原始尺寸: {w}x{h}')

                # 检查图像方向并旋转
                if w > h:
                    logger.warning(f'检测到横向嵌入图像 ({w}x{h})，进行旋转')
                    # 逆时针旋转90度
                    img_np = cv2.rotate(img_np, cv2.ROTATE_90_COUNTERCLOCKWISE)
                    final_h, final_w = img_np.shape[:2]
                    logger.info(f'嵌入图像已旋转: {w}x{h} -> {final_w}x{final_h}')

                # 保存处理后的图像
                cv2.imwrite(img_path, img_np)

                # 验证保存后的图像
                saved_img = cv2.imread(img_path)
                if saved_img is not None:
                    saved_h, saved_w = saved_img.shape[:2]
                    logger.debug(f'最终保存的图像尺寸: {saved_w}x{saved_h}')
                    return img_path
                else:
                    logger.error(f'保存的嵌入图像无法读取: {img_path}')
                    return None
            else:
                logger.warning(f'无法解码嵌入图像数据: xref={xref}')
                return None

        except Exception as e:
            logger.error(f'提取嵌入图像失败: {str(e)}', exc_info=True)
            return None
