# OCR-PDF内容抽取整理

本项目用于从PDF文件中提取医疗信息，并将提取的信息整理到Excel表格中。

## 功能特点

- 自动从PDF文件中提取图片
- 支持翻转页面自动检测和旋转校正
- 图像预处理增强（对比度增强、锐化）提高OCR质量
- 使用OCR技术识别图片中的文字
- 支持跨行文本内容的识别和提取
- 从OCR结果中提取关键医疗信息
- 自动提取数值并去除单位
- 将提取的信息整理到Excel表格中
- 支持将原始图片嵌入到Excel中便于核验
- 支持多个PDF文件批量处理
- 自动合并同一患者的信息，并优先保留数值较大的记录

## 安装依赖

```bash
pip install paddlepaddle paddleocr pymupdf openpyxl pillow
```

## 目录结构

```
OCR-PDF内容抽取整理/
├── config.ini          # 配置文件
├── main.py             # 主程序
├── ocr_engine.py       # OCR引擎模块
├── pdf_processor.py    # PDF处理模块
├── excel_handler.py    # Excel处理模块
├── README.md           # 说明文档
└── 项目总结.md         # 项目总结
```

## 使用方法

1. 将待处理的PDF文件放入`files`目录中
2. 运行主程序：

```bash
python -m OCR-PDF内容抽取整理.main
```

3. 程序会自动处理PDF文件，并将结果保存到`output/medical_data.xlsx`文件中
4. 处理完成后会显示摘要信息，包括处理的PDF文件数和成功提取的字段信息

## 配置说明

配置文件`config.ini`中的主要参数：

### 基本配置
- `input_pdf_path`: PDF文件输入路径
- `output_excel_path`: Excel输出文件路径
- `temp_images_path`: 临时图片存储路径
- `log_path`: 日志存储路径
- `log_level`: 日志级别

### OCR相关配置
- `engine`: OCR引擎类型，目前支持`paddleocr`
- `language`: OCR识别语言，`ch`表示中文
- `confidence_threshold`: OCR识别置信度阈值

### 图像处理配置
- `auto_rotate`: 是否启用自动旋转检测
- `enhance_contrast`: 是否增强图像对比度
- `sharpen`: 是否对图像进行锐化处理

### Excel配置
- `insert_images`: 是否在Excel中插入原始图片
- `max_img_width`: 插入图片的最大宽度
- `max_img_height`: 插入图片的最大高度

## 提取字段

程序会自动提取以下字段信息：

### 基本信息
- 姓名、住院号、性别、年龄等

### 心超数据
- 心超结论、超声所见
- 室间隔厚度、舒张末期前后径、后壁厚度
- EF值

### 心电图
- 心电图结论

### 检验结果
- 血红蛋白、糖化血红蛋白、血糖
- 甘油三酯、总胆固醇
- 低密度脂蛋白胆固醇、高密度脂蛋白胆固醇
- 载脂蛋白A1、载脂蛋白B、脂蛋白(a)
- BNP、pro-BNP
- 高敏肌钙蛋白T、高敏肌钙蛋白I
- 肌红蛋白、肌酸激酶、肌酸激酶同功酶

## 注意事项

- PDF文件中需要包含清晰的图片才能正确识别
- OCR识别结果可能会有误差，需要人工核验
- 程序会在`temp_images`目录中保存提取的图片，可以用于核验
- 对于同一患者，如有重复检测值，会优先保留数值较大的记录
- 翻转页面的自动检测需要页面上有足够的文本内容

## 扩展功能

如需添加更多字段的提取规则，可以修改`pdf_processor.py`文件中的相关函数：

- `_extract_patient_info`: 提取患者基本信息
- `_extract_echo_data`: 提取心超数据
- `_extract_lab_values`: 提取检验结果
- `_extract_ecg_conclusion`: 提取心电图结论

## 常见问题解决

1. 如果识别质量不佳，可以调整配置文件中的图像处理参数
2. 对于特定字段无法识别，可以检查PDF中的具体表述方式，并在对应的提取函数中添加新的关键词
3. 如果Excel中图片显示异常，可以在配置中关闭图片插入功能
