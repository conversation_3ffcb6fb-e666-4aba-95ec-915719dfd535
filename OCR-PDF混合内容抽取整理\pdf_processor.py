import os
import re
import logging
import time
import fitz  # PyMuPDF
from typing import List, Dict, Optional, Tuple, Any
from configparser import ConfigParser
from echo_ocr_engine import EchoOCREngine
from excel_handler import ExcelHandler
from PIL import Image
import io
import numpy as np
import cv2
from datetime import datetime

logger = logging.getLogger('PDFExtractor')

class MixedContentPDFProcessor:
    def __init__(self, config: Config<PERSON>arser, echo_ocr_engine: EchoOCREngine, excel_handler: ExcelHandler):
        self.config = config
        self.echo_ocr_engine = echo_ocr_engine
        self.excel_handler = excel_handler
        self.input_path = config.get('DEFAULT', 'input_pdf_path')
        self.temp_images_path = config.get('DEFAULT', 'temp_images_path')

        # 字段提取配置
        self.text_fields = config.get('FIELD_EXTRACTION', 'text_fields').split(',')
        self.image_fields = config.get('FIELD_EXTRACTION', 'image_fields').split(',')
        self.ecg_keywords = config.get('FIELD_EXTRACTION', 'ecg_keywords').split(',')
        self.echo_keywords = config.get('FIELD_EXTRACTION', 'echo_keywords').split(',')
        self.cta_keywords = config.get('FIELD_EXTRACTION', 'cta_keywords').split(',')

        # 创建临时图片目录
        os.makedirs(self.temp_images_path, exist_ok=True)
        
    def process_pdfs(self):
        """处理所有PDF文件"""
        pdf_files = self._get_pdf_files()
        logger.info(f'找到 {len(pdf_files)} 个待处理PDF文件')
        
        for pdf_path in pdf_files:
            logger.info(f'开始处理PDF文件: {os.path.basename(pdf_path)}')
            try:
                record_data = self._process_single_pdf(pdf_path)
                if record_data:
                    self.excel_handler.add_record(record_data)
                    logger.info(f'成功处理PDF: {os.path.basename(pdf_path)}')
                else:
                    logger.warning(f'未能提取有效数据: {pdf_path}')
            except Exception as e:
                logger.error(f'处理PDF文件出错 {pdf_path}: {str(e)}', exc_info=True)
    
    def _get_pdf_files(self) -> List[str]:
        """获取所有PDF文件路径"""
        pdf_files = []
        if os.path.exists(self.input_path):
            for file in os.listdir(self.input_path):
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(self.input_path, file))
        return sorted(pdf_files)
    
    def _process_single_pdf(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """处理单个PDF文件"""
        record_data = {
            'PDF文件名': os.path.basename(pdf_path),
            '处理状态': '处理中',
            '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        try:
            doc = fitz.open(pdf_path)
            
            # 提取所有页面的文字内容
            all_text = ""
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()
                all_text += page_text + "\n"
            
            # 从文字中提取字段
            logger.info(f'开始提取文字字段，文本长度: {len(all_text)}字符')
            self._extract_text_fields(all_text, record_data)
            logger.info(f'文字字段提取完成')

            # 从图片中提取字段（主要是心超）
            logger.info(f'开始提取图片字段')
            self._extract_image_fields(doc, record_data)
            logger.info(f'图片字段提取完成')
            
            doc.close()
            record_data['处理状态'] = '完成'
            
        except Exception as e:
            logger.error(f'处理PDF出错: {str(e)}')
            import traceback
            traceback.print_exc()
            record_data['处理状态'] = f'错误: {str(e)}'
        
        return record_data
    
    def _extract_text_fields(self, text: str, record_data: Dict[str, Any]):
        """从文字中提取字段"""

        # 保存原始文本用于关键词检查
        record_data['原始文本'] = text

        # 提取姓名 - 多种格式
        name_patterns = [
            r'姓名[:\s]*([^\s\n]+)',
            r'姓名：([^\s\n]+)',
            r'姓名\s+([^\s\n]+)'
        ]
        for pattern in name_patterns:
            name_match = re.search(pattern, text)
            if name_match:
                record_data['姓名'] = name_match.group(1).strip()
                break

        # 提取性别
        gender_patterns = [
            r'性别[:\s]*(男|女)',
            r'性别：(男|女)',
            r'性别\s+(男|女)'
        ]
        for pattern in gender_patterns:
            gender_match = re.search(pattern, text)
            if gender_match:
                record_data['性别'] = gender_match.group(1).strip()
                break

        # 提取年龄
        age_patterns = [
            r'年龄[:\s]*(\d+)',
            r'年龄：(\d+)',
            r'年龄\s+(\d+)',
            r'(\d+)\s*岁'
        ]
        for pattern in age_patterns:
            age_match = re.search(pattern, text)
            if age_match:
                age = int(age_match.group(1))
                if 0 < age < 120:  # 年龄合理性检查
                    record_data['年龄'] = age
                    break

        # 提取总检日期 - 多种格式
        date_patterns = [
            r'总检日期[:\s]*(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)',
            r'总检日期：(\d{4}-\d{1,2}-\d{1,2})',
            r'体检日期[:\s]*(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)'
        ]
        for pattern in date_patterns:
            date_match = re.search(pattern, text)
            if date_match:
                record_data['总检日期'] = date_match.group(1).strip()
                break

        # 提取身高体重BMI - 改进模式匹配
        # 身高
        height_patterns = [
            r'身高[:\s]*(\d+(?:\.\d+)?)\s*cm',
            r'身高\s+(\d+(?:\.\d+)?)\s*cm',
            r'身高：(\d+(?:\.\d+)?)\s*cm'
        ]
        for pattern in height_patterns:
            height_match = re.search(pattern, text)
            if height_match:
                record_data['身高'] = height_match.group(1)
                break

        # 体重 - 改进模式匹配
        weight_patterns = [
            r'体重[:\s]*(\d+(?:\.\d+)?)\s*[kK][gG]',
            r'体重\s+(\d+(?:\.\d+)?)\s*[kK][gG]',
            r'体重：(\d+(?:\.\d+)?)\s*[kK][gG]',
            r'体重[:\s]*(\d+(?:\.\d+)?)\s*公斤',
            r'体重[:\s]*(\d+(?:\.\d+)?)\s*千克',
            r'体重[:\s]*(\d+(?:\.\d+)?)(?=\s|$)',  # 体重后直接跟数字
            r'体重\s*[：:]\s*(\d+(?:\.\d+)?)',
            r'体重\s*(\d+(?:\.\d+)?)\s*$'  # 行末的体重数值
        ]
        for pattern in weight_patterns:
            weight_match = re.search(pattern, text, re.IGNORECASE)
            if weight_match:
                weight_val = float(weight_match.group(1))
                if 20 <= weight_val <= 200:  # 体重合理性检查
                    record_data['体重'] = weight_match.group(1)
                    break

        # BMI
        bmi_patterns = [
            r'BMI[:\s]*(\d+(?:\.\d+)?)',
            r'BMI\s+(\d+(?:\.\d+)?)',
            r'BMI：(\d+(?:\.\d+)?)'
        ]
        for pattern in bmi_patterns:
            bmi_match = re.search(pattern, text)
            if bmi_match:
                record_data['BMI'] = bmi_match.group(1)
                break

        # 提取血压 - 改进模式
        bp_patterns = [
            r'血压\(收缩压\)[:\s]*(\d+)\s*mmHg',
            r'收缩压[:\s]*(\d+)\s*mmHg',
            r'血压.*?(\d+)/(\d+)\s*mmHg'  # 格式如 120/80 mmHg
        ]

        for pattern in bp_patterns:
            bp_match = re.search(pattern, text)
            if bp_match:
                if len(bp_match.groups()) == 2:  # 120/80格式
                    record_data['血压_收缩压'] = bp_match.group(1)
                    record_data['血压_舒张压'] = bp_match.group(2)
                else:
                    record_data['血压_收缩压'] = bp_match.group(1)
                break

        # 舒张压单独匹配
        if '血压_舒张压' not in record_data:
            bp_diastolic_patterns = [
                r'血压\(舒张压\)[:\s]*(\d+)\s*mmHg',
                r'舒张压[:\s]*(\d+)\s*mmHg'
            ]
            for pattern in bp_diastolic_patterns:
                bp_diastolic_match = re.search(pattern, text)
                if bp_diastolic_match:
                    record_data['血压_舒张压'] = bp_diastolic_match.group(1)
                    break

        # 提取心电图诊断 - 改进模式
        ecg_patterns = [
            r'【常规心电图[^】]*】[^:：]*[：:]([^【\n]+)',
            r'心电图.*?诊断小结[:\s]*([^\n【]+)',
            r'心电图.*?结论[:\s]*([^\n【]+)'
        ]
        for pattern in ecg_patterns:
            ecg_match = re.search(pattern, text)
            if ecg_match:
                record_data['心电图诊断小结'] = ecg_match.group(1).strip()
                break

        # 提取冠脉CTA诊断 - 改进模式
        cta_patterns = [
            r'【冠状动脉CTA[^】]*】[^:：]*[：:]([^【\n]+)',
            r'冠状动脉CTA.*?诊断小结[:\s]*([^\n【]+)',
            r'冠脉CTA.*?结论[:\s]*([^\n【]+)'
        ]
        for pattern in cta_patterns:
            cta_match = re.search(pattern, text)
            if cta_match:
                record_data['冠状动脉CTA(增强)诊断小结'] = cta_match.group(1).strip()
                break

        # 提取血液检查指标
        self._extract_blood_test_values(text, record_data)

        # 提取生活习惯
        self._extract_lifestyle_info(text, record_data)
    
    def _extract_blood_test_values(self, text: str, record_data: Dict[str, Any]):
        """提取血液检查数值 - 改进模式匹配，处理医院报告格式"""

        # 血色素/血红蛋白 - 处理多种格式
        hb_patterns = [
            r'血色素\(HGB\)[:\s]*(\d+(?:\.\d+)?)',  # 血色素(HGB):123
            r'血红蛋白\(HGB\)[:\s]*(\d+(?:\.\d+)?)',
            r'血色素[:\s]*(\d+(?:\.\d+)?)\s*g/L',
            r'血红蛋白[:\s]*(\d+(?:\.\d+)?)\s*g/L',
            r'HGB[:\s]*(\d+(?:\.\d+)?)\s*g/L',
            r'血色素[:\s]*(\d+(?:\.\d+)?)',
            r'血红蛋白[:\s]*(\d+(?:\.\d+)?)',
            r'HGB[:\s]*(\d+(?:\.\d+)?)',
            r'Hb[:\s]*(\d+(?:\.\d+)?)'
        ]
        for pattern in hb_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                value = float(match.group(1))
                if 50 <= value <= 200:  # 血色素合理范围
                    record_data['血色素'] = match.group(1)
                    break

        # 总胆固醇 - 处理医院报告格式
        tc_patterns = [
            r'总胆固醇\(TC\)\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+mmol/L',  # 总胆固醇(TC) 4.43 2.33-5.20 mmol/L
            r'总胆固醇\(TC\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L',  # 总胆固醇(TC):4.5mmol/L
            r'总胆固醇[:\s]*(\d+(?:\.\d+)?)\s*mmol/L',
            r'胆固醇\(TC\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L',
            r'TC[:\s]*(\d+(?:\.\d+)?)\s*mmol/L',
            r'总胆固醇[:\s]*(\d+(?:\.\d+)?)',
            r'TC[:\s]*(\d+(?:\.\d+)?)',
            r'CHOL[:\s]*(\d+(?:\.\d+)?)'
        ]
        for pattern in tc_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                value = float(match.group(1))
                if 1.0 <= value <= 15.0:  # 总胆固醇合理范围 mmol/L
                    record_data['总胆固醇(TC)'] = match.group(1)
                    break

        # 甘油三酯 - 处理医院报告格式
        tg_patterns = [
            r'甘油三酯\(TG\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',  # 甘油三酯(TG):0.44mmol/L↓
            r'甘油三酯[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
            r'三酰甘油\(TG\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
            r'(?<!HDL)TG[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',  # 避免匹配HDLTG
            r'甘油三酯[:\s]*(\d+(?:\.\d+)?)',
            r'(?<!HDL)TG[:\s]*(\d+(?:\.\d+)?)',  # 避免匹配HDLTG
            r'TRIG[:\s]*(\d+(?:\.\d+)?)'
        ]
        for pattern in tg_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                value = float(match.group(1))
                if 0.1 <= value <= 10.0:  # 甘油三酯合理范围 mmol/L
                    record_data['甘油三酯(TG)'] = match.group(1)
                    break

        # 高密度脂蛋白胆固醇 - 处理医院报告格式
        hdl_patterns = [
            r'高密度脂蛋白胆固醇\(HDL-C\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',  # 高密度脂蛋白胆固醇(HDL-C):2.03mmol/L↑
            r'高密度脂蛋白胆固醇[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
            r'高密度脂蛋白[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
            r'HDL-C[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
            r'高密度脂蛋白胆固醇[:\s]*(\d+(?:\.\d+)?)',
            r'HDL-C[:\s]*(\d+(?:\.\d+)?)',
            r'(?<!L)HDL(?!C)[:\s]*(\d+(?:\.\d+)?)'  # 避免匹配LHDL和HDLC
        ]
        for pattern in hdl_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                value = float(match.group(1))
                if 0.5 <= value <= 5.0:  # HDL-C合理范围 mmol/L
                    record_data['高密度脂蛋白胆固醇(HDL-C)'] = match.group(1)
                    break

        # 低密度脂蛋白胆固醇 - 处理医院报告格式
        ldl_patterns = [
            r'低密度脂蛋白胆固醇\(LDL-C\)\s+(\d+(?:\.\d+)?)\s+.*?mmol/L',  # 低密度脂蛋白胆固醇(LDL-C) 1.52 超高患者目标值 <1.4 极高患者目标值 <1.8 mmol/L
            r'低密度脂蛋白胆固醇\(LDL-C\)\s+(\d+(?:\.\d+)?)',  # 简化版本
            r'低密度脂蛋白胆固醇\(LDL-C\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
            r'低密度脂蛋白胆固醇[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
            r'低密度脂蛋白[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
            r'LDL-C[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
            r'低密度脂蛋白胆固醇[:\s]*(\d+(?:\.\d+)?)',
            r'LDL-C[:\s]*(\d+(?:\.\d+)?)',
            r'LDL[:\s]*(\d+(?:\.\d+)?)'
        ]
        for pattern in ldl_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                value = float(match.group(1))
                if 0.5 <= value <= 8.0:  # LDL-C合理范围 mmol/L
                    record_data['低密度脂蛋白胆固醇(LDL-C)'] = match.group(1)
                    break

        # 糖化血红蛋白
        hba1c_patterns = [
            r'糖化血红蛋白[:\s]*(\d+(?:\.\d+)?)',
            r'HbA1c[:\s]*(\d+(?:\.\d+)?)',
            r'糖化血红蛋白A1c[:\s]*(\d+(?:\.\d+)?)'
        ]
        for pattern in hba1c_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                record_data['糖化血红蛋白'] = match.group(1)
                break

        # 肌酸激酶 - 改进的正则表达式，确保匹配正确的数值
        ck_patterns = [
            r'肌酸激酶\(CK\)\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',  # 肌酸激酶(CK) 115 35-175 U/L
            r'肌酸激酶\(CK\)[:\s]*(\d+(?:\.\d+)?)\s*U/L',  # 肌酸激酶(CK):115 U/L
            r'肌酸激酶\(CK\)[:\s]*(\d+(?:\.\d+)?)',  # 肌酸激酶(CK):115
            r'肌酸激酶[^同工酶]*[:\s]+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',  # 排除同工酶，匹配完整格式
            r'肌酸激酶[^同工酶]*[:\s]*(\d+(?:\.\d+)?)\s*U/L',  # 排除同工酶
            r'(?<!同工)肌酸激酶[:\s]*(\d+(?:\.\d+)?)',  # 使用负向后查找排除同工酶
            r'CK[^-M\s]*\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',  # CK 115 35-175 U/L，排除CK-MB
            r'CK[^-M]*[:\s]*(\d+(?:\.\d+)?)\s*U/L'  # CK:115 U/L，排除CK-MB
        ]
        for pattern in ck_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    value = float(match.group(1))
                    if 10 <= value <= 1000:  # CK合理范围 U/L
                        record_data['肌酸激酶(CK)'] = match.group(1)
                        break
                except ValueError:
                    continue

        # 肌酸激酶同工酶 - 改进的正则表达式
        ckmb_patterns = [
            r'肌酸激酶同工酶\(CK-MB\)\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',  # 肌酸激酶同工酶(CK-MB) 10 0-25 U/L
            r'肌酸激酶同工酶\(CK-MB\)[:\s]*(\d+(?:\.\d+)?)\s*U/L',  # 肌酸激酶同工酶(CK-MB):10 U/L
            r'肌酸激酶同工酶\(CK-MB\)[:\s]*(\d+(?:\.\d+)?)',  # 肌酸激酶同工酶(CK-MB):10
            r'肌酸激酶同工酶[:\s]*(\d+(?:\.\d+)?)\s*U/L',
            r'肌酸激酶同工酶[:\s]*(\d+(?:\.\d+)?)',
            r'肌酸激酶同功酶[:\s]*(\d+(?:\.\d+)?)',
            r'CK-MB\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',  # CK-MB 10 0-25 U/L
            r'CK-MB[:\s]*(\d+(?:\.\d+)?)\s*U/L',  # CK-MB:10 U/L
            r'CK-MB[:\s]*(\d+(?:\.\d+)?)',  # CK-MB:10
            r'CKMB[:\s]*(\d+(?:\.\d+)?)'
        ]
        for pattern in ckmb_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    value = float(match.group(1))
                    if 0 <= value <= 100:  # CK-MB合理范围 U/L
                        record_data['肌酸激酶同工酶(CK-MB)'] = match.group(1)
                        break
                except ValueError:
                    continue
    
    def _extract_lifestyle_info(self, text: str, record_data: Dict[str, Any]):
        """提取生活习惯信息"""
        # 吸烟
        if re.search(r'吸烟[:\s]*是|吸烟史[:\s]*有', text):
            record_data['吸烟'] = '是'
        elif re.search(r'吸烟[:\s]*否|无吸烟', text):
            record_data['吸烟'] = '否'
            
        # 饮酒
        if re.search(r'饮酒[:\s]*是|饮酒史[:\s]*有', text):
            record_data['饮酒'] = '是'
        elif re.search(r'饮酒[:\s]*否|无饮酒', text):
            record_data['饮酒'] = '否'
    
    def _extract_image_fields(self, doc: fitz.Document, record_data: Dict[str, Any]):
        """从图片中提取字段（处理心超和心电图图片）"""
        # 先检查文本中是否包含心超或心电图关键词
        full_text = record_data.get('原始文本', '')
        echo_keywords = ['心超', '心脏超声', '超声心动图', '超声所见', '超声提示', '心脏彩超', '心脏B超', '超声检查']
        ecg_keywords = ['心电图', 'ECG', 'EKG']

        has_echo_keywords = any(keyword in full_text for keyword in echo_keywords)
        has_ecg_keywords = any(keyword in full_text for keyword in ecg_keywords)

        # 调试信息：显示找到的关键词
        found_echo_keywords = [kw for kw in echo_keywords if kw in full_text]
        found_ecg_keywords = [kw for kw in ecg_keywords if kw in full_text]

        logger.info(f'关键词检查结果:')
        logger.info(f'  发现的心超关键词: {found_echo_keywords}')
        logger.info(f'  发现的心电图关键词: {found_ecg_keywords}')
        logger.info(f'  原始文本长度: {len(full_text)}字符')

        if not has_echo_keywords and not has_ecg_keywords:
            logger.info(f'文本中未发现心超或心电图关键词，跳过图片处理')
            return

        logger.info(f'文本中发现关键词 - 心超: {has_echo_keywords}, 心电图: {has_ecg_keywords}，开始图片处理')

        total_images = 0
        echo_images_found = 0
        ecg_images_found = 0

        # 从后往前检索页面（心超和心电图通常在后面）
        for page_num in range(len(doc) - 1, -1, -1):
            page = doc[page_num]
            image_list = page.get_images()
            total_images += len(image_list)

            logger.debug(f'第{page_num+1}页包含{len(image_list)}张图片')

            # 如果已经找到了需要的图片，可以提前退出
            if (not has_echo_keywords or echo_images_found > 0) and (not has_ecg_keywords or ecg_images_found > 0):
                logger.info(f'已找到所需图片，跳过剩余页面')
                break

            for img_index, img in enumerate(image_list):
                try:
                    # 提取图片
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)

                    if pix.n - pix.alpha < 4:  # 确保不是CMYK
                        img_data = pix.tobytes("png")
                        pix = None

                        # 保存临时图片
                        temp_img_path = os.path.join(
                            self.temp_images_path,
                            f"{record_data['PDF文件名']}_page{page_num+1}_img{img_index+1}.png"
                        )

                        with open(temp_img_path, "wb") as f:
                            f.write(img_data)

                        # 根据文本关键词决定检查哪种类型的图片
                        if has_echo_keywords:
                            # 检查是否是心超图片（带超时保护）
                            is_echo = self.echo_ocr_engine.quick_check_echo_image(temp_img_path)
                            if is_echo:
                                logger.info(f'发现心超图片: {temp_img_path}')
                                echo_images_found += 1

                                # 只从第一张心超图片提取信息（避免重复）
                                if echo_images_found == 1:
                                    # 提取心超信息
                                    echo_info = self.echo_ocr_engine.extract_echo_info(temp_img_path)
                                    if echo_info:
                                        record_data.update(echo_info)
                                        logger.info(f'成功从心超图片提取信息: {list(echo_info.keys())}')
                                    else:
                                        logger.warning(f'心超图片未提取到有效信息: {temp_img_path}')
                                else:
                                    logger.info(f'跳过额外的心超图片（已有{echo_images_found-1}张）')

                        if has_ecg_keywords:
                            # 检查是否是心电图图片
                            is_ecg = self._check_ecg_image(temp_img_path)
                            if is_ecg:
                                logger.info(f'发现心电图图片: {temp_img_path}')
                                ecg_images_found += 1

                                # 只保存第一张心电图图片到Excel（避免重复）
                                if ecg_images_found == 1:
                                    # 保存心电图图片到永久位置
                                    ecg_image_path = self._save_ecg_image(temp_img_path, record_data.get('姓名', 'unknown'))
                                    if ecg_image_path:
                                        record_data['心电图图片'] = ecg_image_path
                                        logger.info(f'保存心电图图片到Excel: {ecg_image_path}')
                                else:
                                    logger.info(f'跳过额外的心电图图片（已有{ecg_images_found-1}张）')

                        # 清理临时文件
                        if os.path.exists(temp_img_path):
                            os.remove(temp_img_path)

                    if pix:
                        pix = None

                except Exception as e:
                    logger.error(f'处理图片出错: {str(e)}')

        logger.info(f'图片扫描完成: 总计{total_images}张图片，发现{echo_images_found}张心超图片，{ecg_images_found}张心电图图片')

    def _check_ecg_image(self, image_path: str) -> bool:
        """检查图片是否包含心电图内容"""
        try:
            # 首先进行视觉特征检测
            if self._check_ecg_visual_features(image_path):
                logger.info(f'心电图图片视觉特征检测通过: {image_path}')
                return True

            # 如果视觉特征检测不通过，再尝试OCR检测
            # 使用支持中文路径的图片读取
            img = self.echo_ocr_engine._read_image_chinese_path(image_path)
            if img is None:
                return False

            # 使用图片数组进行OCR
            result = self.echo_ocr_engine.ocr.ocr(img, cls=True)
            if result and result[0]:
                all_text = ''
                for line in result[0]:
                    if len(line) >= 2:
                        text, confidence = line[1]
                        if confidence >= 0.3:
                            all_text += text + ' '

                # 心电图报告单专用关键词 - 更精确的识别
                # 必须包含心电图报告的核心关键词
                ecg_report_keywords = [ '心电图报告']
                ecg_medical_keywords = ['窦性心律', 'QRS', 'PR间期', 'QT间期', '心率', 'bpm', '毫秒', 'ms']
                ecg_diagnosis_keywords = ['诊断', '结论', '提示', '所见', '正常心电图', '异常心电图']

                # 排除非心电图的医学图像
                exclude_keywords = ['超声', 'CT', 'MRI', 'X线', '胸片', '血管造影', '内镜']

                # 检查是否包含排除关键词
                has_exclude = any(keyword in all_text for keyword in exclude_keywords)
                if has_exclude:
                    logger.debug(f'图片包含非心电图关键词，跳过: {all_text[:100]}')
                    return False

                # 必须包含心电图核心关键词
                has_ecg_report = any(keyword in all_text for keyword in ecg_report_keywords)
                has_ecg_medical = any(keyword in all_text for keyword in ecg_medical_keywords)
                has_ecg_diagnosis = any(keyword in all_text for keyword in ecg_diagnosis_keywords)

                # 更严格的判断：必须是心电图报告单
                is_ecg_report = has_ecg_report and (has_ecg_medical or has_ecg_diagnosis)

                found_keywords = []
                if has_ecg_report:
                    found_keywords.extend([kw for kw in ecg_report_keywords if kw in all_text])
                if has_ecg_medical:
                    found_keywords.extend([kw for kw in ecg_medical_keywords if kw in all_text])
                if has_ecg_diagnosis:
                    found_keywords.extend([kw for kw in ecg_diagnosis_keywords if kw in all_text])

                if is_ecg_report:
                    logger.info(f'识别为心电图报告单，关键词: {found_keywords}')
                    return True
                else:
                    logger.debug(f'非心电图报告单，识别文本: {all_text[:100]}')
                    return False
        except Exception as e:
            logger.error(f'OCR检查心电图图片失败: {e}')
        return False

    def _check_ecg_visual_features(self, image_path: str) -> bool:
        """通过视觉特征检查是否是心电图图片"""
        try:
            import cv2
            import numpy as np

            # 使用支持中文路径的方法读取图片
            with open(image_path, 'rb') as f:
                img_data = f.read()
            img_array = np.frombuffer(img_data, np.uint8)
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

            if img is None:
                return False

            height, width = img.shape[:2]
            aspect_ratio = width / height

            # 心电图特征：
            # 1. 通常宽度大于高度（横向图）
            # 2. 图片不会太小
            # 3. 不会是正方形（通常是长方形）

            is_landscape = aspect_ratio > 1.2  # 宽度明显大于高度
            is_reasonable_size = width > 200 and height > 100  # 合理的尺寸
            is_not_square = abs(aspect_ratio - 1.0) > 0.3  # 不是正方形

            logger.debug(f'图片{image_path}特征: 尺寸{width}x{height}, 宽高比{aspect_ratio:.2f}, 横向:{is_landscape}, 尺寸合理:{is_reasonable_size}, 非正方形:{is_not_square}')

            # 简单的心电图特征检测
            return is_landscape and is_reasonable_size and is_not_square

        except Exception as e:
            logger.error(f'检查图片视觉特征失败: {e}')
            return False

    def _save_ecg_image(self, temp_image_path: str, patient_name: str) -> str:
        """保存心电图图片到永久位置"""
        try:
            # 创建ECG图片保存目录
            ecg_dir = os.path.join(self.temp_images_path, 'ecg_images')
            os.makedirs(ecg_dir, exist_ok=True)

            # 生成永久文件名
            timestamp = int(time.time())
            ecg_filename = f"{patient_name}_ecg_{timestamp}.png"
            ecg_path = os.path.join(ecg_dir, ecg_filename)

            # 复制文件
            import shutil
            shutil.copy2(temp_image_path, ecg_path)

            return ecg_path
        except Exception as e:
            logger.error(f'保存心电图图片失败: {e}')
            return None

