import streamlit as st
import cv2
import numpy as np
import os
import uuid
import configparser
from collections import OrderedDict
from PIL import Image

# --- 1. 定义处理步骤的基类和具体实现 ---

class ProcessingStep:
    """图像处理步骤的基类"""
    name = "基础步骤"
    description = "这是一个基础步骤"

    def __init__(self, step_id=None):
        self.id = step_id or f"{self.__class__.__name__}_{uuid.uuid4().hex[:6]}"
        # st.session_state会在每次交互后保留值，这是我们存储参数的地方
        if self.id not in st.session_state:
            st.session_state[self.id] = {}

    def render_params(self):
        """在Streamlit中渲染参数控件。应由子类实现。"""
        return {}

    def apply(self, image):
        """将处理应用于图像。应由子类实现。"""
        raise NotImplementedError

    def to_config(self, config: configparser.ConfigParser, section_name: str, params: dict):
        """将此步骤的配置写入ConfigParser对象。"""
        config[section_name] = {'type': self.__class__.__name__}
        for key, value in params.items():
            if isinstance(value, tuple):
                config[section_name][key] = ','.join(map(str, value))
            else:
                config[section_name][key] = str(value)

class Grayscale(ProcessingStep):
    name = "灰度化"
    description = "将彩色图像转换为灰度图像。"
    def apply(self, image):
        if len(image.shape) == 3:
            return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        return image

class ContrastEnhancement(ProcessingStep):
    name = "对比度增强 (CLAHE)"
    description = "使用自适应直方图均衡化增强图像对比度。"
    def render_params(self):
        st.session_state[self.id]['clip_limit'] = st.slider(
            "裁剪限制 (Clip Limit)", 1.0, 10.0, st.session_state[self.id].get('clip_limit', 2.0), 0.5, key=f"{self.id}_cl"
        )
        grid_size_val = st.session_state[self.id].get('grid_size', (8, 8))[0]
        grid_size = st.slider("网格大小 (Grid Size)", 1, 16, grid_size_val, key=f"{self.id}_gs")
        st.session_state[self.id]['grid_size'] = (grid_size, grid_size)
        return st.session_state[self.id]
    def apply(self, image):
        if len(image.shape) == 3:
            st.warning(f"'{self.name}' 需要单通道灰度图。请先添加 '灰度化' 步骤。")
            return image
        params = st.session_state.get(self.id, {})
        clahe = cv2.createCLAHE(clipLimit=params.get('clip_limit', 2.0), tileGridSize=params.get('grid_size', (8, 8)))
        return clahe.apply(image)

class Denoise(ProcessingStep):
    name = "降噪"
    description = "使用快速非局部均值算法去除噪声。"
    def render_params(self):
        st.session_state[self.id]['h'] = st.slider(
            "过滤强度 (h)", 1, 30, st.session_state[self.id].get('h', 10), key=f"{self.id}_h"
        )
        return st.session_state[self.id]
    def apply(self, image):
        if len(image.shape) == 3:
            st.warning(f"'{self.name}' 需要单通道灰度图。请先添加 '灰度化' 步骤。")
            return image
        if image.dtype != np.uint8:
            image = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        params = st.session_state.get(self.id, {})
        return cv2.fastNlMeansDenoising(image, None, params.get('h', 10), 7, 21)

class Sharpen(ProcessingStep):
    name = "锐化"
    description = "使用卷积核锐化图像，突出边缘。"
    def render_params(self):
        st.session_state[self.id]['strength'] = st.slider(
            "锐化强度", 1, 20, st.session_state[self.id].get('strength', 9), key=f"{self.id}_strength"
        )
        return st.session_state[self.id]
    def apply(self, image):
        params = st.session_state.get(self.id, {})
        strength = params.get('strength', 9)
        kernel = np.array([[-1, -1, -1], [-1, strength, -1], [-1, -1, -1]])
        return cv2.filter2D(image, -1, kernel)

class Binarize(ProcessingStep):
    name = "二值化"
    description = "将灰度图像转换为纯黑白图像。"
    def render_params(self):
        methods = ['otsu', 'adaptive', 'local']
        current_method = st.session_state[self.id].get('method', 'adaptive')
        st.session_state[self.id]['method'] = st.selectbox(
            "二值化方法", methods, index=methods.index(current_method), key=f"{self.id}_method"
        )
        if 'adaptive' in st.session_state[self.id]['method'] or 'local' in st.session_state[self.id]['method']:
            st.session_state[self.id]['block_size'] = st.slider(
                "块大小 (Block Size)", 3, 31, st.session_state[self.id].get('block_size', 11), 2, key=f"{self.id}_bs"
            )
            st.session_state[self.id]['c'] = st.slider(
                "常数 C", 1, 10, st.session_state[self.id].get('c', 2), key=f"{self.id}_c"
            )
        return st.session_state[self.id]
    def apply(self, image):
        if len(image.shape) == 3:
            st.warning(f"'{self.name}' 需要单通道灰度图。请先添加 '灰度化' 步骤。")
            return image
        params = st.session_state.get(self.id, {})
        method = params.get('method', 'adaptive')
        if method == 'otsu':
            _, processed = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        else:
            block_size = params.get('block_size', 11)
            c = params.get('c', 2)
            method_map = {
                'adaptive': cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                'local': cv2.ADAPTIVE_THRESH_MEAN_C
            }
            processed = cv2.adaptiveThreshold(image, 255, method_map[method], cv2.THRESH_BINARY, block_size, c)
        return processed

class Morphology(ProcessingStep):
    name = "形态学操作"
    description = "通过腐蚀、膨胀等操作改变图像中物体的形状。"
    def render_params(self):
        ops = ['erode', 'dilate', 'open', 'close']
        shapes = ['rect', 'ellipse', 'cross']
        st.session_state[self.id]['op_type'] = st.selectbox(
            "操作类型", ops, index=ops.index(st.session_state[self.id].get('op_type', 'close')), key=f"{self.id}_op"
        )
        st.session_state[self.id]['kernel_shape'] = st.selectbox(
            "核形状", shapes, index=shapes.index(st.session_state[self.id].get('kernel_shape', 'rect')), key=f"{self.id}_shape"
        )
        kernel_size_val = st.session_state[self.id].get('kernel_size', (2, 2))[0]
        kernel_size = st.slider("核大小", 1, 15, kernel_size_val, key=f"{self.id}_ksize")
        st.session_state[self.id]['kernel_size'] = (kernel_size, kernel_size)
        return st.session_state[self.id]
    def apply(self, image):
        params = st.session_state.get(self.id, {})
        op_type = params.get('op_type', 'close')
        kernel_shape = params.get('kernel_shape', 'rect')
        kernel_size = params.get('kernel_size', (2, 2))
        
        shape_map = {'rect': cv2.MORPH_RECT, 'ellipse': cv2.MORPH_ELLIPSE, 'cross': cv2.MORPH_CROSS}
        op_map = {'erode': cv2.MORPH_ERODE, 'dilate': cv2.MORPH_DILATE, 'open': cv2.MORPH_OPEN, 'close': cv2.MORPH_CLOSE}
        
        kernel = cv2.getStructuringElement(shape_map[kernel_shape], kernel_size)
        return cv2.morphologyEx(image, op_map[op_type], kernel)

# --- 2. 初始化和工具函数 ---

AVAILABLE_STEPS = {step.name: step for step in [Grayscale, ContrastEnhancement, Denoise, Sharpen, Binarize, Morphology]}
CONFIG_FILE = 'config.ini'

def initialize_session_state():
    if 'pipeline' not in st.session_state:
        st.session_state.pipeline = []
    if 'uploaded_file' not in st.session_state:
        st.session_state.uploaded_file = None

def save_pipeline_to_config():
    config = configparser.ConfigParser()
    # 读取现有配置以保留其他部分
    if os.path.exists(CONFIG_FILE):
        config.read(CONFIG_FILE, encoding='utf-8')

    # 清除旧的pipeline部分
    existing_sections = config.sections()
    for section in existing_sections:
        if section.startswith('PIPELINE_STEP_') or section == 'PIPELINE':
            config.remove_section(section)

    # 写入新的pipeline
    pipeline_steps = st.session_state.get('pipeline', [])
    step_ids = [step.id for step in pipeline_steps]
    config['PIPELINE'] = {'steps': ','.join(step_ids)}

    for step in pipeline_steps:
        section_name = step.id
        params = st.session_state.get(section_name, {})
        step.to_config(config, section_name, params)
    
    with open(CONFIG_FILE, 'w', encoding='utf-8') as configfile:
        config.write(configfile)
    st.success(f"配置已成功保存到 `{CONFIG_FILE}`！")

# --- 3. Streamlit 界面渲染 ---

st.set_page_config(layout="wide", page_title="可编排图像处理工具")
st.title("可编排图像处理与调试工具")
initialize_session_state()

# --- 侧边栏 ---
with st.sidebar:
    st.header("1. 加载图片")
    uploaded_file = st.file_uploader("选择一张图片", type=["png", "jpg", "jpeg", "bmp"])
    if uploaded_file:
        st.session_state.uploaded_file = uploaded_file

    st.header("2. 构建处理管线")
    selected_step_name = st.selectbox("选择要添加的步骤:", list(AVAILABLE_STEPS.keys()))
    if st.button("添加步骤"):
        step_class = AVAILABLE_STEPS[selected_step_name]
        st.session_state.pipeline.append(step_class())
        
    st.header("3. 保存配置")
    if st.button("保存当前管线到 config.ini"):
        save_pipeline_to_config()

# --- 主界面 ---
if not st.session_state.uploaded_file:
    st.info("请先在左侧上传一张图片。")
else:
    file_bytes = np.asarray(bytearray(st.session_state.uploaded_file.read()), dtype=np.uint8)
    original_image = cv2.imdecode(file_bytes, 1)
    
    col1, col2 = st.columns(2)
    with col1:
        st.subheader("原始图像")
        st.image(original_image, channels="BGR", use_column_width=True)

    # 处理管线UI
    st.subheader("处理管线")
    pipeline = st.session_state.pipeline
    processed_image = original_image.copy()

    for i, step in enumerate(pipeline):
        with st.expander(f"**步骤 {i+1}: {step.name}**", expanded=True):
            # 渲染参数控件
            step.render_params()
            
            # 渲染操作按钮
            c1, c2, c3, _ = st.columns([1, 1, 1, 5])
            if c1.button("🔼 上移", key=f"up_{step.id}", disabled=(i == 0)):
                pipeline.insert(i - 1, pipeline.pop(i))
                st.experimental_rerun()
            if c2.button("🔽 下移", key=f"down_{step.id}", disabled=(i == len(pipeline) - 1)):
                pipeline.insert(i + 1, pipeline.pop(i))
                st.experimental_rerun()
            if c3.button("❌ 移除", key=f"del_{step.id}"):
                pipeline.pop(i)
                # 清理session_state中存储的参数
                if step.id in st.session_state:
                    del st.session_state[step.id]
                st.experimental_rerun()

    # 应用所有处理步骤
    intermediate_images = {'原始图像': original_image}
    current_image = original_image
    for step in pipeline:
        try:
            current_image = step.apply(current_image)
            intermediate_images[step.name] = current_image
        except Exception as e:
            st.error(f"步骤 '{step.name}' 执行失败: {e}")
            break

    with col2:
        st.subheader("最终结果")
        st.image(current_image, channels="BGR" if len(current_image.shape) == 3 else "GRAY", use_column_width=True)

    st.subheader("分步预览")
    # 计算列数，最多4列
    num_steps = len(intermediate_images)
    cols = st.columns(min(num_steps, 4))
    
    step_idx = 0
    for caption, img in intermediate_images.items():
        with cols[step_idx % 4]:
            st.image(img, caption=caption, use_column_width=True, channels="BGR" if len(img.shape) == 3 else "GRAY")
        step_idx += 1 