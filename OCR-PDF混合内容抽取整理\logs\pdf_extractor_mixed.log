2025-07-31 16:35:04 - PDFExtractor - INFO - 日志系统初始化完成
2025-07-31 16:35:04 - PDFExtractor - INFO - ==================================================
2025-07-31 16:35:04 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-07-31 16:35:04 - PDFExtractor - INFO - 初始化OCR引擎...
2025-07-31 16:35:04 - PDFExtractor - INFO - 初始化OCR引擎: paddleocr, 语言: ch
2025-07-31 16:35:04 - PDFExtractor - INFO - 初始化Excel处理器...
2025-07-31 16:35:04 - PDFExtractor - INFO - 初始化PDF处理器...
2025-07-31 16:35:04 - PDFExtractor - INFO - 模块初始化完成
2025-07-31 16:35:04 - PDFExtractor - INFO - 开始处理PDF文件...
2025-07-31 16:35:04 - PDFExtractor - INFO - 找到 45 个待处理PDF文件
2025-07-31 16:35:04 - PDFExtractor - INFO - 开始处理PDF文件: 202403140123_张东_44082219660422041X.pdf
2025-07-31 16:35:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:05 - PDFExtractor - INFO - 添加记录: 张东
2025-07-31 16:35:05 - PDFExtractor - INFO - 添加新记录: 张东
2025-07-31 16:35:05 - PDFExtractor - INFO - 成功处理PDF: 202403140123_张东_44082219660422041X.pdf
2025-07-31 16:35:05 - PDFExtractor - INFO - 开始处理PDF文件: 202403140124_黄依依_440102195309300021.pdf
2025-07-31 16:35:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:06 - PDFExtractor - INFO - 添加记录: 黄依依
2025-07-31 16:35:06 - PDFExtractor - INFO - 添加新记录: 黄依依
2025-07-31 16:35:06 - PDFExtractor - INFO - 成功处理PDF: 202403140124_黄依依_440102195309300021.pdf
2025-07-31 16:35:06 - PDFExtractor - INFO - 开始处理PDF文件: 202403140125_罗端兰_440822196204150248.pdf
2025-07-31 16:35:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - INFO - 添加记录: 罗端兰
2025-07-31 16:35:08 - PDFExtractor - INFO - 添加新记录: 罗端兰
2025-07-31 16:35:08 - PDFExtractor - INFO - 成功处理PDF: 202403140125_罗端兰_440822196204150248.pdf
2025-07-31 16:35:08 - PDFExtractor - INFO - 开始处理PDF文件: 202403140126_郭雯锦_441423196509153349.pdf
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:10 - PDFExtractor - INFO - 添加记录: 郭雯锦
2025-07-31 16:35:10 - PDFExtractor - INFO - 添加新记录: 郭雯锦
2025-07-31 16:35:10 - PDFExtractor - INFO - 成功处理PDF: 202403140126_郭雯锦_441423196509153349.pdf
2025-07-31 16:35:10 - PDFExtractor - INFO - 开始处理PDF文件: 202403140127_刘伟贤_441423195809200018.pdf
2025-07-31 16:35:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:11 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:11 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:12 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:12 - PDFExtractor - INFO - 添加记录: 刘伟贤
2025-07-31 16:35:12 - PDFExtractor - INFO - 添加新记录: 刘伟贤
2025-07-31 16:35:12 - PDFExtractor - INFO - 成功处理PDF: 202403140127_刘伟贤_441423195809200018.pdf
2025-07-31 16:35:12 - PDFExtractor - INFO - 开始处理PDF文件: 202403260124-唐棣邦-44060119641113183X.pdf
2025-07-31 16:35:12 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:12 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:12 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:12 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:12 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:12 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - INFO - 添加记录: 唐棣邦
2025-07-31 16:35:13 - PDFExtractor - INFO - 添加新记录: 唐棣邦
2025-07-31 16:35:13 - PDFExtractor - INFO - 成功处理PDF: 202403260124-唐棣邦-44060119641113183X.pdf
2025-07-31 16:35:13 - PDFExtractor - INFO - 开始处理PDF文件: 202403260125-黄剑青-440683197007228030.pdf
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - INFO - 添加记录: 黄剑青
2025-07-31 16:35:14 - PDFExtractor - INFO - 添加新记录: 黄剑青
2025-07-31 16:35:14 - PDFExtractor - INFO - 成功处理PDF: 202403260125-黄剑青-440683197007228030.pdf
2025-07-31 16:35:14 - PDFExtractor - INFO - 开始处理PDF文件: 202403280156-袁圣丹-440924196908123182.pdf
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - INFO - 添加记录: 袁圣丹
2025-07-31 16:35:15 - PDFExtractor - INFO - 添加新记录: 袁圣丹
2025-07-31 16:35:15 - PDFExtractor - INFO - 成功处理PDF: 202403280156-袁圣丹-440924196908123182.pdf
2025-07-31 16:35:15 - PDFExtractor - INFO - 开始处理PDF文件: 202403280156_袁圣丹_440924196908123182.pdf
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - INFO - 添加记录: 袁圣丹
2025-07-31 16:35:16 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 袁圣丹
2025-07-31 16:35:16 - PDFExtractor - INFO - 成功处理PDF: 202403280156_袁圣丹_440924196908123182.pdf
2025-07-31 16:35:16 - PDFExtractor - INFO - 开始处理PDF文件: 202403280157_袁生_440602196710040010.pdf
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:16 - PDFExtractor - INFO - 添加记录: 袁生
2025-07-31 16:35:16 - PDFExtractor - INFO - 添加新记录: 袁生
2025-07-31 16:35:16 - PDFExtractor - INFO - 成功处理PDF: 202403280157_袁生_440602196710040010.pdf
2025-07-31 16:35:16 - PDFExtractor - INFO - 开始处理PDF文件: 202403281988-黄星-440902197007051616.pdf
2025-07-31 16:35:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:17 - PDFExtractor - INFO - 添加记录: 黄星
2025-07-31 16:35:17 - PDFExtractor - INFO - 添加新记录: 黄星
2025-07-31 16:35:17 - PDFExtractor - INFO - 成功处理PDF: 202403281988-黄星-440902197007051616.pdf
2025-07-31 16:35:17 - PDFExtractor - INFO - 开始处理PDF文件: 202404080339_劳承周_440924196207162998.pdf
2025-07-31 16:35:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:18 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:19 - PDFExtractor - INFO - 添加记录: 劳承周
2025-07-31 16:35:19 - PDFExtractor - INFO - 添加新记录: 劳承周
2025-07-31 16:35:19 - PDFExtractor - INFO - 成功处理PDF: 202404080339_劳承周_440924196207162998.pdf
2025-07-31 16:35:19 - PDFExtractor - INFO - 开始处理PDF文件: 202404080340_劳承文_440924196401172970.pdf
2025-07-31 16:35:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:20 - PDFExtractor - INFO - 添加记录: 劳承文
2025-07-31 16:35:20 - PDFExtractor - INFO - 添加新记录: 劳承文
2025-07-31 16:35:20 - PDFExtractor - INFO - 成功处理PDF: 202404080340_劳承文_440924196401172970.pdf
2025-07-31 16:35:20 - PDFExtractor - INFO - 开始处理PDF文件: 202404080341_袁华英_440924196210273189.pdf
2025-07-31 16:35:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:21 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:21 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:22 - PDFExtractor - INFO - 添加记录: 袁华英
2025-07-31 16:35:22 - PDFExtractor - INFO - 添加新记录: 袁华英
2025-07-31 16:35:22 - PDFExtractor - INFO - 成功处理PDF: 202404080341_袁华英_440924196210273189.pdf
2025-07-31 16:35:22 - PDFExtractor - INFO - 开始处理PDF文件: 202404190057_罗庆欢_441423196005222718.pdf
2025-07-31 16:35:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:23 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - INFO - 添加记录: 罗庆欢
2025-07-31 16:35:24 - PDFExtractor - INFO - 添加新记录: 罗庆欢
2025-07-31 16:35:24 - PDFExtractor - INFO - 成功处理PDF: 202404190057_罗庆欢_441423196005222718.pdf
2025-07-31 16:35:24 - PDFExtractor - INFO - 开始处理PDF文件: 202404190061_钟新梅_441425196808253908.pdf
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:25 - PDFExtractor - INFO - 添加记录: 钟新梅
2025-07-31 16:35:25 - PDFExtractor - INFO - 添加新记录: 钟新梅
2025-07-31 16:35:25 - PDFExtractor - INFO - 成功处理PDF: 202404190061_钟新梅_441425196808253908.pdf
2025-07-31 16:35:25 - PDFExtractor - INFO - 开始处理PDF文件: 202404190067_袁国泉_H60790359.pdf
2025-07-31 16:35:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - INFO - 添加记录: 袁国泉
2025-07-31 16:35:27 - PDFExtractor - INFO - 添加新记录: 袁国泉
2025-07-31 16:35:27 - PDFExtractor - INFO - 成功处理PDF: 202404190067_袁国泉_H60790359.pdf
2025-07-31 16:35:27 - PDFExtractor - INFO - 开始处理PDF文件: 202404190091-黄星-440902197007051616.pdf
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - INFO - 添加记录: 黄星
2025-07-31 16:35:27 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 黄星
2025-07-31 16:35:27 - PDFExtractor - INFO - 成功处理PDF: 202404190091-黄星-440902197007051616.pdf
2025-07-31 16:35:27 - PDFExtractor - INFO - 开始处理PDF文件: 202404221692_袁圣浩_440924196807213170.pdf
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:28 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - INFO - 添加记录: 袁圣浩
2025-07-31 16:35:29 - PDFExtractor - INFO - 添加新记录: 袁圣浩
2025-07-31 16:35:29 - PDFExtractor - INFO - 成功处理PDF: 202404221692_袁圣浩_440924196807213170.pdf
2025-07-31 16:35:29 - PDFExtractor - INFO - 开始处理PDF文件: 202404221693_张燕平_440924197102273661.pdf
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:30 - PDFExtractor - INFO - 添加记录: 张燕平
2025-07-31 16:35:30 - PDFExtractor - INFO - 添加新记录: 张燕平
2025-07-31 16:35:30 - PDFExtractor - INFO - 成功处理PDF: 202404221693_张燕平_440924197102273661.pdf
2025-07-31 16:35:30 - PDFExtractor - INFO - 开始处理PDF文件: 202404221695_袁圣泽_440924197404273173.pdf
2025-07-31 16:35:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:31 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:31 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:32 - PDFExtractor - INFO - 添加记录: 袁圣泽
2025-07-31 16:35:32 - PDFExtractor - INFO - 添加新记录: 袁圣泽
2025-07-31 16:35:32 - PDFExtractor - INFO - 成功处理PDF: 202404221695_袁圣泽_440924197404273173.pdf
2025-07-31 16:35:32 - PDFExtractor - INFO - 开始处理PDF文件: 202404221696_叶海燕_440924197402183422.pdf
2025-07-31 16:35:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:34 - PDFExtractor - INFO - 添加记录: 叶海燕
2025-07-31 16:35:34 - PDFExtractor - INFO - 添加新记录: 叶海燕
2025-07-31 16:35:34 - PDFExtractor - INFO - 成功处理PDF: 202404221696_叶海燕_440924197402183422.pdf
2025-07-31 16:35:34 - PDFExtractor - INFO - 开始处理PDF文件: 202404290137-袁圣丹-440924196908123182.pdf
2025-07-31 16:35:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - INFO - 添加记录: 袁圣丹
2025-07-31 16:35:35 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 袁圣丹
2025-07-31 16:35:35 - PDFExtractor - INFO - 成功处理PDF: 202404290137-袁圣丹-440924196908123182.pdf
2025-07-31 16:35:35 - PDFExtractor - INFO - 开始处理PDF文件: 202405150543_陈娟_321081197012200022.pdf
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:36 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:36 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:38 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:38 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:39 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:39 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:39 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:39 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - INFO - 添加记录: 陈娟
2025-07-31 16:35:40 - PDFExtractor - INFO - 添加新记录: 陈娟
2025-07-31 16:35:40 - PDFExtractor - INFO - 成功处理PDF: 202405150543_陈娟_321081197012200022.pdf
2025-07-31 16:35:40 - PDFExtractor - INFO - 开始处理PDF文件: 202405160114_徐海宁_440622196604195428.pdf
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:41 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:41 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:42 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:42 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:42 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:43 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:43 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:43 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:44 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:45 - PDFExtractor - INFO - 添加记录: 徐海宁
2025-07-31 16:35:45 - PDFExtractor - INFO - 添加新记录: 徐海宁
2025-07-31 16:35:45 - PDFExtractor - INFO - 成功处理PDF: 202405160114_徐海宁_440622196604195428.pdf
2025-07-31 16:35:45 - PDFExtractor - INFO - 开始处理PDF文件: 202405160123_吴长根_321081197002140310.pdf
2025-07-31 16:35:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:47 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:47 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:49 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - INFO - 添加记录: 吴长根
2025-07-31 16:35:50 - PDFExtractor - INFO - 添加新记录: 吴长根
2025-07-31 16:35:50 - PDFExtractor - INFO - 成功处理PDF: 202405160123_吴长根_321081197002140310.pdf
2025-07-31 16:35:50 - PDFExtractor - INFO - 开始处理PDF文件: 202405240209_何六女_440622195810051324.pdf
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:51 - PDFExtractor - INFO - 添加记录: 何六女
2025-07-31 16:35:51 - PDFExtractor - INFO - 添加新记录: 何六女
2025-07-31 16:35:51 - PDFExtractor - INFO - 成功处理PDF: 202405240209_何六女_440622195810051324.pdf
2025-07-31 16:35:51 - PDFExtractor - INFO - 开始处理PDF文件: 202405280142_梁全财_440601196507101512.pdf
2025-07-31 16:35:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:52 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:52 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:54 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - INFO - 添加记录: 梁全财
2025-07-31 16:35:55 - PDFExtractor - INFO - 添加新记录: 梁全财
2025-07-31 16:35:55 - PDFExtractor - INFO - 成功处理PDF: 202405280142_梁全财_440601196507101512.pdf
2025-07-31 16:35:55 - PDFExtractor - INFO - 开始处理PDF文件: 202405280143_黄锦颖_440682198509191723.pdf
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:55 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:56 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:56 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:57 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:57 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:58 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:58 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:59 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:35:59 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:00 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:00 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:01 - PDFExtractor - INFO - 添加记录: 黄锦颖
2025-07-31 16:36:01 - PDFExtractor - INFO - 添加新记录: 黄锦颖
2025-07-31 16:36:01 - PDFExtractor - INFO - 成功处理PDF: 202405280143_黄锦颖_440682198509191723.pdf
2025-07-31 16:36:01 - PDFExtractor - INFO - 开始处理PDF文件: 202405280145-叶宁-440602197305271549.pdf
2025-07-31 16:36:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:02 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:02 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:02 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:03 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:03 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:03 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:04 - PDFExtractor - INFO - 添加记录: 叶宁
2025-07-31 16:36:04 - PDFExtractor - INFO - 添加新记录: 叶宁
2025-07-31 16:36:04 - PDFExtractor - INFO - 成功处理PDF: 202405280145-叶宁-440602197305271549.pdf
2025-07-31 16:36:04 - PDFExtractor - INFO - 开始处理PDF文件: 202405280146_李仁声_450302196511280551.pdf
2025-07-31 16:36:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:05 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - INFO - 添加记录: 李仁声
2025-07-31 16:36:09 - PDFExtractor - INFO - 添加新记录: 李仁声
2025-07-31 16:36:09 - PDFExtractor - INFO - 成功处理PDF: 202405280146_李仁声_450302196511280551.pdf
2025-07-31 16:36:09 - PDFExtractor - INFO - 开始处理PDF文件: 202405280147_甘树彬_440622197010241719.pdf
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:11 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:11 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:11 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:12 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - INFO - 添加记录: 甘树彬
2025-07-31 16:36:13 - PDFExtractor - INFO - 添加新记录: 甘树彬
2025-07-31 16:36:13 - PDFExtractor - INFO - 成功处理PDF: 202405280147_甘树彬_440622197010241719.pdf
2025-07-31 16:36:13 - PDFExtractor - INFO - 开始处理PDF文件: 202405280148_张江_44060219640727181X.pdf
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:18 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:18 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:18 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:18 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - INFO - 添加记录: 张江
2025-07-31 16:36:19 - PDFExtractor - INFO - 添加新记录: 张江
2025-07-31 16:36:19 - PDFExtractor - INFO - 成功处理PDF: 202405280148_张江_44060219640727181X.pdf
2025-07-31 16:36:19 - PDFExtractor - INFO - 开始处理PDF文件: 202405280150_陈长东_440121197212030094.pdf
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:21 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:21 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:21 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:23 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:23 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:25 - PDFExtractor - INFO - 添加记录: 陈长东
2025-07-31 16:36:25 - PDFExtractor - INFO - 添加新记录: 陈长东
2025-07-31 16:36:25 - PDFExtractor - INFO - 成功处理PDF: 202405280150_陈长东_440121197212030094.pdf
2025-07-31 16:36:25 - PDFExtractor - INFO - 开始处理PDF文件: 202405280151_李佳华_422202197001287070.pdf
2025-07-31 16:36:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:28 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:28 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:31 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - INFO - 添加记录: 李佳华
2025-07-31 16:36:33 - PDFExtractor - INFO - 添加新记录: 李佳华
2025-07-31 16:36:33 - PDFExtractor - INFO - 成功处理PDF: 202405280151_李佳华_422202197001287070.pdf
2025-07-31 16:36:33 - PDFExtractor - INFO - 开始处理PDF文件: 202405280152_周剑雄_440602196611221510.pdf
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:36 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:37 - PDFExtractor - INFO - 添加记录: 周剑雄
2025-07-31 16:36:37 - PDFExtractor - INFO - 添加新记录: 周剑雄
2025-07-31 16:36:37 - PDFExtractor - INFO - 成功处理PDF: 202405280152_周剑雄_440602196611221510.pdf
2025-07-31 16:36:37 - PDFExtractor - INFO - 开始处理PDF文件: 202405280155_范耀洪_440622196408242899.pdf
2025-07-31 16:36:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:38 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:38 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:39 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:39 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:40 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:43 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:45 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:45 - PDFExtractor - INFO - 添加记录: 范耀洪
2025-07-31 16:36:45 - PDFExtractor - INFO - 添加新记录: 范耀洪
2025-07-31 16:36:45 - PDFExtractor - INFO - 成功处理PDF: 202405280155_范耀洪_440622196408242899.pdf
2025-07-31 16:36:45 - PDFExtractor - INFO - 开始处理PDF文件: 202406070081_杨荣洪_440682196709226012.pdf
2025-07-31 16:36:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:46 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:47 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:47 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:47 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:47 - PDFExtractor - INFO - 添加记录: 杨荣洪
2025-07-31 16:36:47 - PDFExtractor - INFO - 添加新记录: 杨荣洪
2025-07-31 16:36:47 - PDFExtractor - INFO - 成功处理PDF: 202406070081_杨荣洪_440682196709226012.pdf
2025-07-31 16:36:47 - PDFExtractor - INFO - 开始处理PDF文件: 202406170199-唐棣邦-44060119641113183X.pdf
2025-07-31 16:36:47 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:48 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:49 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:50 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:51 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:52 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:53 - PDFExtractor - INFO - 添加记录: 唐棣邦
2025-07-31 16:36:53 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 唐棣邦
2025-07-31 16:36:53 - PDFExtractor - INFO - 成功处理PDF: 202406170199-唐棣邦-44060119641113183X.pdf
2025-07-31 16:36:53 - PDFExtractor - INFO - 开始处理PDF文件: 202406170203-黄剑青-440683197007228030.pdf
2025-07-31 16:36:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:53 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:54 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:56 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:36:58 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:00 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:01 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:04 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - INFO - 添加记录: 黄剑青
2025-07-31 16:37:06 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 黄剑青
2025-07-31 16:37:06 - PDFExtractor - INFO - 成功处理PDF: 202406170203-黄剑青-440683197007228030.pdf
2025-07-31 16:37:06 - PDFExtractor - INFO - 开始处理PDF文件: 202407240048-唐棣邦-44060119641113183X.pdf
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:06 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:07 - PDFExtractor - INFO - 添加记录: 唐棣邦
2025-07-31 16:37:07 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 唐棣邦
2025-07-31 16:37:07 - PDFExtractor - INFO - 成功处理PDF: 202407240048-唐棣邦-44060119641113183X.pdf
2025-07-31 16:37:07 - PDFExtractor - INFO - 开始处理PDF文件: 202412130173_弄庆新_45250119800812256X.pdf
2025-07-31 16:37:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:07 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:08 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:09 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:10 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:11 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:13 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:14 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:15 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:16 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - INFO - 添加记录: 弄庆新
2025-07-31 16:37:17 - PDFExtractor - INFO - 添加新记录: 弄庆新
2025-07-31 16:37:17 - PDFExtractor - INFO - 成功处理PDF: 202412130173_弄庆新_45250119800812256X.pdf
2025-07-31 16:37:17 - PDFExtractor - INFO - 开始处理PDF文件: 202505260063-唐棣邦-44060119641113183X.pdf
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:17 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:18 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:18 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:19 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:20 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:21 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:21 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:21 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:22 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:23 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:23 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:23 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - INFO - 添加记录: 唐棣邦
2025-07-31 16:37:24 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 唐棣邦
2025-07-31 16:37:24 - PDFExtractor - INFO - 成功处理PDF: 202505260063-唐棣邦-44060119641113183X.pdf
2025-07-31 16:37:24 - PDFExtractor - INFO - 开始处理PDF文件: 202506100328-黄剑青-440683197007228030.pdf
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:24 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:25 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:26 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:27 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:28 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:28 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:29 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:30 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:31 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:32 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:33 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - INFO - 添加记录: 黄剑青
2025-07-31 16:37:34 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 黄剑青
2025-07-31 16:37:34 - PDFExtractor - INFO - 成功处理PDF: 202506100328-黄剑青-440683197007228030.pdf
2025-07-31 16:37:34 - PDFExtractor - INFO - 开始处理PDF文件: 202507110198-叶宁-440602197305271549.pdf
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:34 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:35 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:36 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:37 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:38 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:38 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:38 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:39 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:41 - PDFExtractor - ERROR - 处理图片出错: 'OCREngine' object has no attribute 'extract_text_from_image'
2025-07-31 16:37:41 - PDFExtractor - INFO - 添加记录: 叶宁
2025-07-31 16:37:41 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 叶宁
2025-07-31 16:37:41 - PDFExtractor - INFO - 成功处理PDF: 202507110198-叶宁-440602197305271549.pdf
2025-07-31 16:37:41 - PDFExtractor - INFO - PDF文件处理完成
2025-07-31 16:37:41 - PDFExtractor - INFO - 开始保存Excel文件...
2025-07-31 16:37:41 - PDFExtractor - INFO - 开始保存Excel文件: ./output/medical_data_mixed.xlsx
2025-07-31 16:37:41 - PDFExtractor - INFO - Excel文件保存完成: ./output/medical_data_mixed.xlsx
2025-07-31 16:37:41 - PDFExtractor - INFO - 共保存 36 条记录
2025-07-31 16:37:41 - PDFExtractor - INFO - Excel文件保存完成
2025-07-31 16:37:41 - PDFExtractor - INFO - 程序执行完成
2025-07-31 17:03:05 - PDFExtractor - INFO - 日志系统初始化完成
2025-07-31 17:03:05 - PDFExtractor - INFO - ==================================================
2025-07-31 17:03:05 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-07-31 17:03:05 - PDFExtractor - INFO - 初始化OCR引擎...
2025-07-31 17:03:05 - PDFExtractor - INFO - 初始化OCR引擎: paddleocr, 语言: ch
2025-07-31 17:03:06 - PDFExtractor - INFO - 初始化Excel处理器...
2025-07-31 17:03:06 - PDFExtractor - INFO - 初始化PDF处理器...
2025-07-31 17:03:06 - PDFExtractor - INFO - 模块初始化完成
2025-07-31 17:03:06 - PDFExtractor - INFO - 开始处理PDF文件...
2025-07-31 17:03:06 - PDFExtractor - INFO - 找到 45 个待处理PDF文件
2025-07-31 17:03:06 - PDFExtractor - INFO - 开始处理PDF文件: 202403140123_张东_44082219660422041X.pdf
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img1.png (模式: 主)
2025-07-31 17:03:06 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:03:06 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:06 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img1.png
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img2.png (模式: 主)
2025-07-31 17:03:06 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:03:06 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:06 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img2.png
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img3.png (模式: 主)
2025-07-31 17:03:06 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:03:06 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:06 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img3.png
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img4.png (模式: 主)
2025-07-31 17:03:06 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:03:06 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:06 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img4.png
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img5.png (模式: 主)
2025-07-31 17:03:06 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:03:06 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:06 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:07 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:07 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page1_img5.png
2025-07-31 17:03:07 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page3_img1.png (模式: 主)
2025-07-31 17:03:07 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:03:07 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:07 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:07 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:07 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page3_img1.png
2025-07-31 17:03:07 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page3_img2.png (模式: 主)
2025-07-31 17:03:07 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:03:07 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:07 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:07 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:07 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page3_img2.png
2025-07-31 17:03:07 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page4_img1.png (模式: 主)
2025-07-31 17:03:07 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:03:07 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:07 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 识别到文本: 一, 置信度: 0.98, 位置: (589, 774)
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 识别到文本: 米, 置信度: 0.93, 位置: (622, 1038)
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 识别到文本: 张, 置信度: 0.77, 位置: (561, 1210)
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 识别到文本: ：, 置信度: 0.50, 位置: (1116, 1222)
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1140, 1229)
2025-07-31 17:03:09 - PDFExtractor - INFO - 图片识别完成，共找到7个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page4_img1.png
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page5_img1.png (模式: 主)
2025-07-31 17:03:09 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:03:09 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:09 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: Mt, 置信度: 0.54, 位置: (1568, 648)
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: 了, 置信度: 0.60, 位置: (1618, 756)
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.54, 位置: (251, 834)
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (250, 901)
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.99, 位置: (528, 910)
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: 楚沟, 置信度: 0.81, 位置: (484, 1210)
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: ：, 置信度: 0.77, 位置: (1118, 1224)
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1143, 1228)
2025-07-31 17:03:10 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1169, 1229)
2025-07-31 17:03:10 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page5_img1.png
2025-07-31 17:03:11 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page6_img1.png (模式: 主)
2025-07-31 17:03:11 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140123_张东_44082219660422041X.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:03:11 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:11 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:13 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 识别到文本: 科, 置信度: 0.73, 位置: (64, 910)
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 识别到文本:  88 S, 置信度: 0.63, 位置: (135, 1036)
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 识别到文本: 99, 置信度: 0.52, 位置: (99, 1195)
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 识别到文本: A L8°0 *IAS -, 置信度: 0.65, 位置: (132, 1251)
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 识别到文本: q7, 置信度: 0.58, 位置: (104, 1407)
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 识别到文本: s 060, 置信度: 0.77, 位置: (136, 1408)
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 识别到文本: “回O, 置信度: 0.55, 位置: (135, 1511)
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 识别到文本: “树州, 置信度: 0.52, 位置: (102, 1512)
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 识别到文本: 价, 置信度: 0.69, 位置: (1149, 1758)
2025-07-31 17:03:15 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202403140123_张东_44082219660422041X.pdf_page6_img1.png
2025-07-31 17:03:15 - PDFExtractor - INFO - 添加记录: 张东
2025-07-31 17:03:15 - PDFExtractor - INFO - 添加新记录: 张东
2025-07-31 17:03:15 - PDFExtractor - INFO - 成功处理PDF: 202403140123_张东_44082219660422041X.pdf
2025-07-31 17:03:15 - PDFExtractor - INFO - 开始处理PDF文件: 202403140124_黄依依_440102195309300021.pdf
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img1.png (模式: 主)
2025-07-31 17:03:15 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:03:15 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:15 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img1.png
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img2.png (模式: 主)
2025-07-31 17:03:15 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:03:15 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:15 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img2.png
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img3.png (模式: 主)
2025-07-31 17:03:15 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:03:15 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:15 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img3.png
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img4.png (模式: 主)
2025-07-31 17:03:15 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:03:15 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:15 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img4.png
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img5.png (模式: 主)
2025-07-31 17:03:15 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:03:15 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:15 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page1_img5.png
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page3_img1.png (模式: 主)
2025-07-31 17:03:15 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:03:15 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:15 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page3_img1.png
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page3_img2.png (模式: 主)
2025-07-31 17:03:15 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:03:15 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:15 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:15 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page3_img2.png
2025-07-31 17:03:16 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page5_img1.png (模式: 主)
2025-07-31 17:03:16 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:03:16 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:16 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: 10990001：各, 置信度: 0.58, 位置: (204, 373)
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: 里区, 置信度: 0.52, 位置: (528, 728)
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: 州慢, 置信度: 0.60, 位置: (1589, 1024)
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: 右左, 置信度: 1.00, 位置: (484, 1160)
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: 左, 置信度: 1.00, 位置: (529, 1160)
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: °NHZ I, 置信度: 0.74, 位置: (680, 1194)
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: °, 置信度: 0.99, 位置: (621, 1212)
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: 2i, 置信度: 0.83, 位置: (1145, 1228)
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1169, 1229)
2025-07-31 17:03:18 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page5_img1.png
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page6_img1.png (模式: 主)
2025-07-31 17:03:18 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:03:18 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:18 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: 10990001：各, 置信度: 0.58, 位置: (204, 373)
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: M, 置信度: 0.72, 位置: (1568, 648)
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: 了, 置信度: 0.60, 位置: (1618, 756)
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 1.00, 位置: (588, 836)
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (250, 901)
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: 号, 置信度: 0.89, 位置: (498, 1022)
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: 号, 置信度: 1.00, 位置: (470, 1213)
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: 脉, 置信度: 1.00, 位置: (591, 1219)
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1143, 1229)
2025-07-31 17:03:20 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1169, 1229)
2025-07-31 17:03:20 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page6_img1.png
2025-07-31 17:03:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page7_img1.png (模式: 主)
2025-07-31 17:03:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:03:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:22 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本:  80T *AS+SA- SI 0-, 置信度: 0.61, 位置: (132, 680)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.52, 位置: (66, 680)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: 桦, 置信度: 0.62, 位置: (64, 908)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: .08-甲S0, 置信度: 0.53, 位置: (106, 1048)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: AW220, 置信度: 0.64, 位置: (99, 1200)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: AW IC'0IAS -, 置信度: 0.57, 位置: (132, 1248)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: udqo2, 置信度: 0.54, 位置: (104, 1407)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: s 080, 置信度: 0.72, 位置: (136, 1410)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: “回O, 置信度: 0.55, 位置: (135, 1511)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: “树州, 置信度: 0.52, 位置: (102, 1512)
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.92, 位置: (1146, 1478)
2025-07-31 17:03:24 - PDFExtractor - INFO - 图片识别完成，共找到11个文本块: ./temp_images\202403140124_黄依依_440102195309300021.pdf_page7_img1.png
2025-07-31 17:03:24 - PDFExtractor - INFO - 添加记录: 黄依依
2025-07-31 17:03:24 - PDFExtractor - INFO - 添加新记录: 黄依依
2025-07-31 17:03:24 - PDFExtractor - INFO - 成功处理PDF: 202403140124_黄依依_440102195309300021.pdf
2025-07-31 17:03:24 - PDFExtractor - INFO - 开始处理PDF文件: 202403140125_罗端兰_440822196204150248.pdf
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img1.png (模式: 主)
2025-07-31 17:03:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:03:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img1.png
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img2.png (模式: 主)
2025-07-31 17:03:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:03:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img2.png
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img3.png (模式: 主)
2025-07-31 17:03:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:03:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img3.png
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img4.png (模式: 主)
2025-07-31 17:03:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:03:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img4.png
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img5.png (模式: 主)
2025-07-31 17:03:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:03:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page1_img5.png
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page3_img1.png (模式: 主)
2025-07-31 17:03:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:03:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:25 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:25 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page3_img1.png
2025-07-31 17:03:25 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page3_img2.png (模式: 主)
2025-07-31 17:03:25 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:03:25 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:25 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:25 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:25 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page3_img2.png
2025-07-31 17:03:25 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page4_img1.png (模式: 主)
2025-07-31 17:03:25 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:03:25 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:25 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 识别到文本: M, 置信度: 0.72, 位置: (1568, 648)
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 识别到文本: 一, 置信度: 0.98, 位置: (588, 776)
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 识别到文本: 了, 置信度: 0.60, 位置: (1618, 756)
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 识别到文本: 张, 置信度: 0.77, 位置: (561, 1210)
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 识别到文本: 内, 置信度: 0.97, 位置: (682, 1214)
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1144, 1228)
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1169, 1229)
2025-07-31 17:03:27 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page4_img1.png
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page5_img1.png (模式: 主)
2025-07-31 17:03:27 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:03:27 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:27 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 识别到文本: 20990500：, 置信度: 0.55, 位置: (204, 372)
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 识别到文本: 19：, 置信度: 0.53, 位置: (249, 594)
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 识别到文本: ‘中, 置信度: 0.82, 位置: (529, 823)
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.53, 位置: (1616, 857)
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.98, 位置: (249, 901)
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 识别到文本: 州慢, 置信度: 0.60, 位置: (1589, 1024)
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 识别到文本: 整, 置信度: 1.00, 位置: (559, 1210)
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 识别到文本: 一, 置信度: 0.59, 位置: (1116, 1224)
2025-07-31 17:03:28 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1139, 1227)
2025-07-31 17:03:28 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page5_img1.png
2025-07-31 17:03:29 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page6_img1.png (模式: 主)
2025-07-31 17:03:29 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:03:29 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:29 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:30 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: ）, 置信度: 0.54, 位置: (1242, 421)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.72, 位置: (66, 680)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: 科, 置信度: 0.73, 位置: (64, 910)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: 名19 , 置信度: 0.74, 位置: (64, 1152)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: AW SS'O·IAS-, 置信度: 0.63, 位置: (132, 1248)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: udq19, 置信度: 0.92, 位置: (104, 1409)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: s 188, 置信度: 0.68, 位置: (136, 1410)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: “回O, 置信度: 0.51, 位置: (135, 1510)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: “树州, 置信度: 0.52, 位置: (102, 1512)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: udq 19“, 置信度: 0.62, 位置: (102, 1688)
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 识别到文本: 下, 置信度: 0.80, 位置: (1150, 1692)
2025-07-31 17:03:32 - PDFExtractor - INFO - 图片识别完成，共找到11个文本块: ./temp_images\202403140125_罗端兰_440822196204150248.pdf_page6_img1.png
2025-07-31 17:03:32 - PDFExtractor - INFO - 添加记录: 罗端兰
2025-07-31 17:03:32 - PDFExtractor - INFO - 添加新记录: 罗端兰
2025-07-31 17:03:32 - PDFExtractor - INFO - 成功处理PDF: 202403140125_罗端兰_440822196204150248.pdf
2025-07-31 17:03:32 - PDFExtractor - INFO - 开始处理PDF文件: 202403140126_郭雯锦_441423196509153349.pdf
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img1.png (模式: 主)
2025-07-31 17:03:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:03:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:32 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img1.png
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img2.png (模式: 主)
2025-07-31 17:03:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:03:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:32 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img2.png
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img3.png (模式: 主)
2025-07-31 17:03:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:03:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:32 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img3.png
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img4.png (模式: 主)
2025-07-31 17:03:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:03:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:32 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img4.png
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img5.png (模式: 主)
2025-07-31 17:03:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:03:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:32 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page1_img5.png
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page3_img1.png (模式: 主)
2025-07-31 17:03:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:03:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:32 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page3_img1.png
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page3_img2.png (模式: 主)
2025-07-31 17:03:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:03:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:33 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:33 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page3_img2.png
2025-07-31 17:03:33 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page4_img1.png (模式: 主)
2025-07-31 17:03:33 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:03:33 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:33 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:34 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:34 - PDFExtractor - DEBUG - 识别到文本: 80990001：, 置信度: 0.57, 位置: (204, 373)
2025-07-31 17:03:34 - PDFExtractor - DEBUG - 识别到文本: 人）, 置信度: 0.51, 位置: (1125, 790)
2025-07-31 17:03:34 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:03:34 - PDFExtractor - DEBUG - 识别到文本: 州慢, 置信度: 0.60, 位置: (1589, 1024)
2025-07-31 17:03:34 - PDFExtractor - DEBUG - 识别到文本: 奎, 置信度: 0.67, 位置: (252, 1144)
2025-07-31 17:03:34 - PDFExtractor - DEBUG - 识别到文本: 右左, 置信度: 0.76, 位置: (484, 1160)
2025-07-31 17:03:34 - PDFExtractor - DEBUG - 识别到文本: 所, 置信度: 0.63, 位置: (588, 1156)
2025-07-31 17:03:34 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1140, 1229)
2025-07-31 17:03:34 - PDFExtractor - INFO - 图片识别完成，共找到8个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page4_img1.png
2025-07-31 17:03:35 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page5_img1.png (模式: 主)
2025-07-31 17:03:35 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:03:35 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:35 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: 209905001：, 置信度: 0.52, 位置: (204, 372)
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: Mt, 置信度: 0.54, 位置: (1568, 648)
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: 中子, 置信度: 0.52, 位置: (529, 794)
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: 了, 置信度: 0.52, 位置: (1618, 754)
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 1.00, 位置: (251, 902)
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: 奎, 置信度: 0.65, 位置: (252, 1142)
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: 彰, 置信度: 0.58, 位置: (1583, 1068)
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 1.00, 位置: (497, 1198)
2025-07-31 17:03:36 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1140, 1228)
2025-07-31 17:03:36 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page5_img1.png
2025-07-31 17:03:37 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page6_img1.png (模式: 主)
2025-07-31 17:03:37 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:03:37 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:37 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:38 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:03:39 - PDFExtractor - DEBUG - 识别到文本: ）, 置信度: 0.54, 位置: (1242, 421)
2025-07-31 17:03:39 - PDFExtractor - DEBUG - 识别到文本: A 9°0*TAS - , 置信度: 0.67, 位置: (133, 1251)
2025-07-31 17:03:39 - PDFExtractor - DEBUG - 识别到文本: udq 09, 置信度: 0.83, 位置: (104, 1408)
2025-07-31 17:03:39 - PDFExtractor - DEBUG - 识别到文本:   奎, 置信度: 0.71, 位置: (65, 1563)
2025-07-31 17:03:39 - PDFExtractor - DEBUG - 识别到文本: “回O, 置信度: 0.55, 位置: (135, 1511)
2025-07-31 17:03:39 - PDFExtractor - DEBUG - 识别到文本: “树州, 置信度: 0.52, 位置: (102, 1512)
2025-07-31 17:03:39 - PDFExtractor - DEBUG - 识别到文本: q 09 , 置信度: 0.58, 位置: (102, 1688)
2025-07-31 17:03:39 - PDFExtractor - INFO - 图片识别完成，共找到7个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page6_img1.png
2025-07-31 17:03:40 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page7_img1.png (模式: 主)
2025-07-31 17:03:40 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:03:40 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:40 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:42 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本: ）, 置信度: 0.55, 位置: (1242, 421)
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本: A 18 'T  A+9A  , 置信度: 0.52, 位置: (135, 560)
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本:  v90AS- , 置信度: 0.68, 位置: (132, 1252)
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本: udq 09, 置信度: 0.85, 位置: (104, 1408)
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本:   奎, 置信度: 0.66, 位置: (65, 1563)
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本: “回O, 置信度: 0.51, 位置: (135, 1510)
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本: “树州, 置信度: 0.52, 位置: (102, 1512)
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本: 发, 置信度: 0.79, 位置: (1146, 1500)
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本: 频, 置信度: 0.93, 位置: (1144, 1526)
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 识别到文本: udq 09“, 置信度: 0.55, 位置: (102, 1688)
2025-07-31 17:03:44 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202403140126_郭雯锦_441423196509153349.pdf_page7_img1.png
2025-07-31 17:03:44 - PDFExtractor - INFO - 添加记录: 郭雯锦
2025-07-31 17:03:44 - PDFExtractor - INFO - 添加新记录: 郭雯锦
2025-07-31 17:03:44 - PDFExtractor - INFO - 成功处理PDF: 202403140126_郭雯锦_441423196509153349.pdf
2025-07-31 17:03:44 - PDFExtractor - INFO - 开始处理PDF文件: 202403140127_刘伟贤_441423195809200018.pdf
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img1.png (模式: 主)
2025-07-31 17:03:44 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:03:44 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:44 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img1.png
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img2.png (模式: 主)
2025-07-31 17:03:44 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:03:44 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:44 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img2.png
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img3.png (模式: 主)
2025-07-31 17:03:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:03:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img3.png
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img4.png (模式: 主)
2025-07-31 17:03:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:03:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img4.png
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img5.png (模式: 主)
2025-07-31 17:03:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:03:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page1_img5.png
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page3_img1.png (模式: 主)
2025-07-31 17:03:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:03:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page3_img1.png
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page3_img2.png (模式: 主)
2025-07-31 17:03:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:03:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page3_img2.png
2025-07-31 17:03:46 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page4_img1.png (模式: 主)
2025-07-31 17:03:46 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:03:46 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:46 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 000-0-电0, 置信度: 0.53, 位置: (1670, 320)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 009905001：各, 置信度: 0.53, 位置: (204, 373)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: Mt, 置信度: 0.54, 位置: (1568, 648)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 了, 置信度: 0.60, 位置: (1618, 756)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 巡, 置信度: 0.57, 位置: (1580, 981)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 彰, 置信度: 0.58, 位置: (1583, 1068)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 王, 置信度: 0.61, 位置: (470, 1127)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 左, 置信度: 1.00, 位置: (469, 1160)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 远, 置信度: 0.95, 位置: (499, 1164)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 段, 置信度: 0.90, 位置: (500, 1211)
2025-07-31 17:03:47 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1140, 1229)
2025-07-31 17:03:47 - PDFExtractor - INFO - 图片识别完成，共找到12个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page4_img1.png
2025-07-31 17:03:48 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page5_img1.png (模式: 主)
2025-07-31 17:03:48 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:03:48 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:48 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:49 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:49 - PDFExtractor - DEBUG - 识别到文本: 009905001：各, 置信度: 0.53, 位置: (204, 373)
2025-07-31 17:03:49 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.54, 位置: (251, 834)
2025-07-31 17:03:49 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.54, 位置: (1616, 858)
2025-07-31 17:03:49 - PDFExtractor - DEBUG - 识别到文本: “中, 置信度: 0.84, 位置: (560, 851)
2025-07-31 17:03:49 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (249, 902)
2025-07-31 17:03:49 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1144, 1228)
2025-07-31 17:03:49 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 0.99, 位置: (1173, 1229)
2025-07-31 17:03:49 - PDFExtractor - DEBUG - 识别到文本: 4, 置信度: 1.00, 位置: (1199, 1229)
2025-07-31 17:03:49 - PDFExtractor - INFO - 图片识别完成，共找到8个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page5_img1.png
2025-07-31 17:03:50 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page6_img1.png (模式: 主)
2025-07-31 17:03:50 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:03:50 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:50 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:52 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 识别到文本: ）, 置信度: 0.54, 位置: (1242, 421)
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 识别到文本:  00 *TAS+SA - SI 60- , 置信度: 0.57, 位置: (134, 680)
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 识别到文本: 科, 置信度: 0.73, 位置: (64, 910)
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 识别到文本: A LL'OIAS-, 置信度: 0.75, 位置: (132, 1251)
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 识别到文本: udq82, 置信度: 0.81, 位置: (104, 1407)
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 识别到文本: s498, 置信度: 0.50, 位置: (136, 1409)
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 识别到文本: “, 置信度: 0.55, 位置: (100, 1510)
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 识别到文本: “回O, 置信度: 0.55, 位置: (135, 1511)
2025-07-31 17:03:53 - PDFExtractor - INFO - 图片识别完成，共找到8个文本块: ./temp_images\202403140127_刘伟贤_441423195809200018.pdf_page6_img1.png
2025-07-31 17:03:53 - PDFExtractor - INFO - 添加记录: 刘伟贤
2025-07-31 17:03:53 - PDFExtractor - INFO - 添加新记录: 刘伟贤
2025-07-31 17:03:53 - PDFExtractor - INFO - 成功处理PDF: 202403140127_刘伟贤_441423195809200018.pdf
2025-07-31 17:03:53 - PDFExtractor - INFO - 开始处理PDF文件: 202403260124-唐棣邦-44060119641113183X.pdf
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img1.png (模式: 主)
2025-07-31 17:03:53 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:03:53 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:53 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:54 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img1.png
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img2.png (模式: 主)
2025-07-31 17:03:54 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:03:54 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:54 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img2.png
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img3.png (模式: 主)
2025-07-31 17:03:54 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:03:54 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:54 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img3.png
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img4.png (模式: 主)
2025-07-31 17:03:54 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:03:54 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:54 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img4.png
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img5.png (模式: 主)
2025-07-31 17:03:54 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:03:54 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:54 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page1_img5.png
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page3_img1.png (模式: 主)
2025-07-31 17:03:54 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:03:54 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:54 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:54 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page3_img1.png
2025-07-31 17:03:55 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page4_img1.png (模式: 主)
2025-07-31 17:03:55 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:03:55 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:55 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: [d] , 置信度: 0.76, 位置: (1288, 116)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 强, 置信度: 0.87, 位置: (28, 134)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 15, 置信度: 0.91, 位置: (520, 210)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 15, 置信度: 0.96, 位置: (553, 210)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: (sw), 置信度: 0.82, 位置: (994, 274)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 申, 置信度: 0.96, 位置: (262, 324)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 0000000, 置信度: 0.55, 位置: (740, 365)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 中/, 置信度: 0.88, 位置: (557, 440)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 6, 置信度: 0.68, 位置: (1289, 416)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: %92, 置信度: 0.90, 位置: (588, 414)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 导, 置信度: 0.82, 位置: (808, 458)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 20, 置信度: 0.81, 位置: (808, 492)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: A6S磷虫, 置信度: 0.53, 位置: (262, 564)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: [d] , 置信度: 0.69, 位置: (433, 600)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.98, 位置: (484, 605)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 5, 置信度: 0.97, 位置: (553, 609)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 0.55, 位置: (673, 646)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 405060700, 置信度: 0.64, 位置: (742, 646)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: M2, 置信度: 0.63, 位置: (834, 646)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 304050000, 置信度: 0.68, 位置: (914, 646)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: [/]d , 置信度: 0.58, 位置: (430, 738)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.99, 位置: (484, 702)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: [ , 置信度: 0.64, 位置: (1288, 726)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 23, 置信度: 0.74, 位置: (556, 740)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 60, 置信度: 0.77, 位置: (806, 702)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 导, 置信度: 0.64, 位置: (808, 740)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: e, 置信度: 0.54, 位置: (640, 756)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 202, 置信度: 0.78, 位置: (1003, 774)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 0.52, 位置: (637, 778)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.71, 位置: (264, 855)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 1.00, 位置: (264, 926)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 50000, 置信度: 0.68, 位置: (749, 929)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 0.61, 位置: (834, 930)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 3040500000, 置信度: 0.61, 位置: (918, 928)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 0.79, 位置: (1023, 930)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 005000000, 置信度: 0.55, 位置: (1113, 929)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 0.63, 位置: (1000, 1058)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 申, 置信度: 0.90, 位置: (357, 1172)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 23, 置信度: 0.50, 位置: (1016, 1171)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 姓, 置信度: 1.00, 位置: (264, 1200)
2025-07-31 17:03:58 - PDFExtractor - DEBUG - 识别到文本: 30405000700, 置信度: 0.64, 位置: (1102, 1210)
2025-07-31 17:03:58 - PDFExtractor - INFO - 图片识别完成，共找到41个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page4_img1.png
2025-07-31 17:03:59 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page5_img1.png (模式: 主)
2025-07-31 17:03:59 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:03:59 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:03:59 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: W , 置信度: 0.52, 位置: (254, 268)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: %89, 置信度: 0.61, 位置: (397, 211)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: %0%688, 置信度: 0.61, 位置: (466, 242)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: %0~98, 置信度: 0.59, 位置: (498, 242)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: %1118%98~08 % 67, 置信度: 0.57, 位置: (530, 297)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 达, 置信度: 1.00, 位置: (812, 196)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: O, 置信度: 0.80, 位置: (1294, 230)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: ZHXOS, 置信度: 0.56, 位置: (1504, 249)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 重, 置信度: 0.76, 位置: (1392, 298)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: V, 置信度: 0.54, 位置: (370, 300)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: O, 置信度: 0.56, 位置: (406, 350)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 山州山租位角, 置信度: 0.87, 位置: (1612, 428)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 生, 置信度: 0.92, 位置: (1480, 448)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 0, 置信度: 0.62, 位置: (1031, 463)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: eJ-x, 置信度: 0.58, 位置: (327, 482)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: (aBV) 06, 置信度: 0.73, 位置: (1417, 525)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 普, 置信度: 1.00, 位置: (266, 526)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 业擎, 置信度: 0.52, 位置: (788, 547)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 姓班, 置信度: 0.52, 位置: (1242, 580)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 体脂肪率, 置信度: 1.00, 位置: (724, 594)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 08, 置信度: 0.99, 位置: (1416, 584)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 男性, 置信度: 1.00, 位置: (400, 607)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 女性, 置信度: 1.00, 位置: (500, 608)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: O, 置信度: 0.57, 位置: (1320, 658)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 09, 置信度: 1.00, 位置: (1416, 652)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: %9, 置信度: 0.67, 位置: (860, 692)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 8, 置信度: 0.67, 位置: (1415, 692)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 围, 置信度: 0.79, 位置: (211, 758)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: m, 置信度: 0.63, 位置: (836, 838)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 围, 置信度: 0.58, 位置: (239, 760)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 品, 置信度: 0.86, 位置: (1416, 768)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: %59 ~ %SS, 置信度: 0.81, 位置: (456, 857)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 0 ~ 001% 0061, 置信度: 0.61, 位置: (396, 925)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 2010, 置信度: 0.61, 位置: (1244, 826)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 5男, 置信度: 0.78, 位置: (256, 860)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: A, 置信度: 0.71, 位置: (93, 870)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 毕, 置信度: 0.54, 位置: (636, 876)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: % 02'29, 置信度: 0.60, 位置: (453, 994)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 61 000, 置信度: 0.56, 位置: (481, 994)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 0, 置信度: 0.53, 位置: (538, 988)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: % &'7, 置信度: 0.77, 位置: (846, 994)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 9, 置信度: 0.56, 位置: (1219, 1014)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 1, 置信度: 0.99, 位置: (700, 1002)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 1, 置信度: 1.00, 位置: (1032, 1016)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 器, 置信度: 0.52, 位置: (502, 1154)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 重, 置信度: 0.87, 位置: (365, 1164)
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 识别到文本: 口, 置信度: 0.54, 位置: (1156, 1182)
2025-07-31 17:04:03 - PDFExtractor - INFO - 图片识别完成，共找到47个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page5_img1.png
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page5_img2.png (模式: 主)
2025-07-31 17:04:03 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page5_img2.png，尝试替代方法
2025-07-31 17:04:03 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:03 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page5_img2.png
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page5_img3.png (模式: 主)
2025-07-31 17:04:03 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page5_img3.png，尝试替代方法
2025-07-31 17:04:03 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:03 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 识别到文本: 光, 置信度: 0.55, 位置: (31, 83)
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 识别到文本: 建, 置信度: 0.86, 位置: (30, 110)
2025-07-31 17:04:04 - PDFExtractor - INFO - 图片识别完成，共找到2个文本块: ./temp_images\202403260124-唐棣邦-44060119641113183X.pdf_page5_img3.png
2025-07-31 17:04:04 - PDFExtractor - INFO - 添加记录: 唐棣邦
2025-07-31 17:04:04 - PDFExtractor - INFO - 添加新记录: 唐棣邦
2025-07-31 17:04:04 - PDFExtractor - INFO - 成功处理PDF: 202403260124-唐棣邦-44060119641113183X.pdf
2025-07-31 17:04:04 - PDFExtractor - INFO - 开始处理PDF文件: 202403260125-黄剑青-440683197007228030.pdf
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img1.png (模式: 主)
2025-07-31 17:04:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:04:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img1.png
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img2.png (模式: 主)
2025-07-31 17:04:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:04:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img2.png
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img3.png (模式: 主)
2025-07-31 17:04:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:04:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img3.png
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img4.png (模式: 主)
2025-07-31 17:04:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:04:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img4.png
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img5.png (模式: 主)
2025-07-31 17:04:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:04:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page1_img5.png
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page3_img1.png (模式: 主)
2025-07-31 17:04:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:04:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page3_img1.png
2025-07-31 17:04:05 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page4_img1.png (模式: 主)
2025-07-31 17:04:05 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:04:05 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:05 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 80, 置信度: 0.64, 位置: (993, 160)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: [d] , 置信度: 0.76, 位置: (1288, 116)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 09, 置信度: 0.99, 位置: (806, 144)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: e, 置信度: 0.63, 位置: (640, 190)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 1, 置信度: 0.54, 位置: (638, 216)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 回品, 置信度: 0.52, 位置: (31, 256)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: (sw), 置信度: 0.82, 位置: (994, 274)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 申, 置信度: 0.96, 位置: (262, 324)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 05000000, 置信度: 0.52, 位置: (738, 365)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 0.84, 位置: (673, 364)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 中/, 置信度: 0.91, 位置: (557, 439)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 8, 置信度: 0.67, 位置: (994, 388)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 6, 置信度: 0.68, 位置: (1289, 416)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 6, 置信度: 0.83, 位置: (521, 406)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 1, 置信度: 0.66, 位置: (587, 408)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: e, 置信度: 0.52, 位置: (640, 471)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 208., 置信度: 0.67, 位置: (816, 494)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: AS虫, 置信度: 0.52, 位置: (262, 564)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 9, 置信度: 0.58, 位置: (558, 566)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: [d] , 置信度: 0.69, 位置: (433, 600)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.98, 位置: (484, 604)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 5, 置信度: 0.94, 位置: (554, 612)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 0.79, 位置: (674, 646)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 5060000, 置信度: 0.67, 位置: (752, 648)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: #, 置信度: 0.52, 位置: (832, 648)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 0000000000, 置信度: 0.51, 位置: (914, 646)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: [/a]d , 置信度: 0.53, 位置: (430, 740)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.99, 位置: (484, 702)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: [ , 置信度: 0.62, 位置: (1288, 724)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 09, 置信度: 0.89, 位置: (996, 708)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: ., 置信度: 0.51, 位置: (807, 739)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 20 8, 置信度: 0.78, 位置: (816, 775)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.71, 位置: (264, 855)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 1.00, 位置: (264, 926)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 5, 置信度: 0.77, 位置: (521, 921)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 3040500000, 置信度: 0.69, 位置: (918, 928)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 0.79, 位置: (1023, 930)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 0.88, 位置: (1053, 926)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 申, 置信度: 0.90, 位置: (357, 1172)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 0.74, 位置: (1016, 1170)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 姓, 置信度: 1.00, 位置: (262, 1200)
2025-07-31 17:04:10 - PDFExtractor - DEBUG - 识别到文本: 20000, 置信度: 0.51, 位置: (1090, 1211)
2025-07-31 17:04:10 - PDFExtractor - INFO - 图片识别完成，共找到42个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page4_img1.png
2025-07-31 17:04:11 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page5_img1.png (模式: 主)
2025-07-31 17:04:11 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:04:11 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:11 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: W , 置信度: 0.52, 位置: (254, 268)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 6-, 置信度: 0.70, 位置: (1648, 131)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: %89, 置信度: 0.61, 位置: (397, 211)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: %~08%, 置信度: 0.63, 位置: (432, 210)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: %0%6898, 置信度: 0.52, 位置: (467, 244)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: %0~98, 置信度: 0.59, 位置: (498, 242)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: %18%98, 置信度: 0.65, 位置: (530, 258)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 达, 置信度: 1.00, 位置: (812, 196)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: ZHXOS, 置信度: 0.56, 位置: (1504, 249)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: %~, 置信度: 0.71, 位置: (398, 264)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: %67~, 置信度: 0.60, 位置: (432, 266)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 重, 置信度: 0.76, 位置: (1392, 298)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 0.84, 位置: (398, 298)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 5, 置信度: 0.71, 位置: (432, 298)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: V, 置信度: 0.54, 位置: (370, 300)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: O, 置信度: 0.56, 位置: (406, 335)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 山州山租位角, 置信度: 0.87, 位置: (1612, 428)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 生, 置信度: 0.92, 位置: (1480, 448)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 0, 置信度: 0.62, 位置: (1031, 463)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: (86) 06, 置信度: 0.74, 位置: (1416, 525)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 普, 置信度: 1.00, 位置: (266, 527)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 业擎, 置信度: 0.54, 位置: (788, 548)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 姓班, 置信度: 0.52, 位置: (1242, 580)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 体脂肪率, 置信度: 1.00, 位置: (724, 594)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 08, 置信度: 0.99, 位置: (1416, 584)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 男性, 置信度: 1.00, 位置: (400, 607)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 女性, 置信度: 1.00, 位置: (500, 608)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 09, 置信度: 0.99, 位置: (1416, 653)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 8, 置信度: 0.67, 位置: (1415, 692)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 围, 置信度: 0.79, 位置: (211, 758)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 围, 置信度: 0.58, 位置: (239, 760)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 品, 置信度: 0.86, 位置: (1416, 768)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: %59 ~ %9S, 置信度: 0.79, 位置: (456, 858)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 02 ~ 001% 0907, 置信度: 0.65, 位置: (396, 923)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 2010, 置信度: 0.61, 位置: (1244, 826)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: d, 置信度: 0.54, 位置: (94, 868)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 3男, 置信度: 0.96, 位置: (256, 860)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 毕, 置信度: 0.54, 位置: (636, 876)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 科By S0, 置信度: 0.55, 位置: (267, 960)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 0.90, 位置: (952, 916)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: % 00, 置信度: 0.80, 位置: (453, 994)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: % &'1 , 置信度: 0.67, 位置: (846, 996)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 88, 置信度: 0.55, 位置: (633, 1006)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: d, 置信度: 0.54, 位置: (1130, 972)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 器, 置信度: 0.52, 位置: (502, 1154)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 重, 置信度: 0.87, 位置: (365, 1164)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 口, 置信度: 0.54, 位置: (1156, 1182)
2025-07-31 17:04:16 - PDFExtractor - INFO - 图片识别完成，共找到47个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page5_img1.png
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page5_img2.png (模式: 主)
2025-07-31 17:04:16 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page5_img2.png，尝试替代方法
2025-07-31 17:04:16 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:16 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page5_img2.png
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page5_img3.png (模式: 主)
2025-07-31 17:04:16 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page5_img3.png，尝试替代方法
2025-07-31 17:04:16 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 光, 置信度: 0.55, 位置: (31, 83)
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 识别到文本: 建, 置信度: 0.86, 位置: (30, 110)
2025-07-31 17:04:16 - PDFExtractor - INFO - 图片识别完成，共找到2个文本块: ./temp_images\202403260125-黄剑青-440683197007228030.pdf_page5_img3.png
2025-07-31 17:04:16 - PDFExtractor - INFO - 添加记录: 黄剑青
2025-07-31 17:04:16 - PDFExtractor - INFO - 添加新记录: 黄剑青
2025-07-31 17:04:16 - PDFExtractor - INFO - 成功处理PDF: 202403260125-黄剑青-440683197007228030.pdf
2025-07-31 17:04:16 - PDFExtractor - INFO - 开始处理PDF文件: 202403280156-袁圣丹-440924196908123182.pdf
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img1.png (模式: 主)
2025-07-31 17:04:16 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:04:16 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:16 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:17 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img1.png
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img2.png (模式: 主)
2025-07-31 17:04:17 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:04:17 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:17 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img2.png
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img3.png (模式: 主)
2025-07-31 17:04:17 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:04:17 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:17 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img3.png
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img4.png (模式: 主)
2025-07-31 17:04:17 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:04:17 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:17 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img4.png
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img5.png (模式: 主)
2025-07-31 17:04:17 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:04:17 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:17 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page1_img5.png
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page3_img1.png (模式: 主)
2025-07-31 17:04:17 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:04:17 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:17 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page3_img1.png
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page3_img2.png (模式: 主)
2025-07-31 17:04:17 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:04:17 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:17 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:17 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page3_img2.png
2025-07-31 17:04:18 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page4_img1.png (模式: 主)
2025-07-31 17:04:18 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:04:18 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:18 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:19 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:19 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:04:19 - PDFExtractor - DEBUG - 识别到文本: 右, 置信度: 0.97, 位置: (468, 1160)
2025-07-31 17:04:19 - PDFExtractor - DEBUG - 识别到文本: 滑, 置信度: 0.93, 位置: (528, 1213)
2025-07-31 17:04:19 - PDFExtractor - INFO - 图片识别完成，共找到3个文本块: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page4_img1.png
2025-07-31 17:04:19 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page5_img1.png (模式: 主)
2025-07-31 17:04:19 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:04:19 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:19 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 识别到文本: ‘霜, 置信度: 0.70, 位置: (530, 196)
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 识别到文本: 中中, 置信度: 0.63, 位置: (498, 860)
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.53, 位置: (1616, 857)
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.78, 位置: (528, 855)
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (250, 901)
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 识别到文本: 2i, 置信度: 0.83, 位置: (1125, 1229)
2025-07-31 17:04:21 - PDFExtractor - INFO - 图片识别完成，共找到7个文本块: ./temp_images\202403280156-袁圣丹-440924196908123182.pdf_page5_img1.png
2025-07-31 17:04:21 - PDFExtractor - INFO - 添加记录: 袁圣丹
2025-07-31 17:04:21 - PDFExtractor - INFO - 添加新记录: 袁圣丹
2025-07-31 17:04:21 - PDFExtractor - INFO - 成功处理PDF: 202403280156-袁圣丹-440924196908123182.pdf
2025-07-31 17:04:21 - PDFExtractor - INFO - 开始处理PDF文件: 202403280156_袁圣丹_440924196908123182.pdf
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img1.png (模式: 主)
2025-07-31 17:04:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:04:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img1.png
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img2.png (模式: 主)
2025-07-31 17:04:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:04:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img2.png
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img3.png (模式: 主)
2025-07-31 17:04:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:04:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img3.png
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img4.png (模式: 主)
2025-07-31 17:04:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:04:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img4.png
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img5.png (模式: 主)
2025-07-31 17:04:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:04:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page1_img5.png
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page3_img1.png (模式: 主)
2025-07-31 17:04:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:04:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page3_img1.png
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page3_img2.png (模式: 主)
2025-07-31 17:04:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:04:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page3_img2.png
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page4_img1.png (模式: 主)
2025-07-31 17:04:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:04:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:22 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:22 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:04:22 - PDFExtractor - DEBUG - 识别到文本: 右, 置信度: 0.97, 位置: (468, 1160)
2025-07-31 17:04:22 - PDFExtractor - DEBUG - 识别到文本: 滑, 置信度: 0.93, 位置: (528, 1213)
2025-07-31 17:04:22 - PDFExtractor - INFO - 图片识别完成，共找到3个文本块: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page4_img1.png
2025-07-31 17:04:22 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page5_img1.png (模式: 主)
2025-07-31 17:04:22 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:04:23 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:23 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 识别到文本: ‘霜, 置信度: 0.70, 位置: (530, 196)
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 识别到文本: 中中, 置信度: 0.63, 位置: (498, 860)
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.53, 位置: (1616, 857)
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.78, 位置: (528, 855)
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (250, 901)
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 识别到文本: 2i, 置信度: 0.83, 位置: (1125, 1229)
2025-07-31 17:04:24 - PDFExtractor - INFO - 图片识别完成，共找到7个文本块: ./temp_images\202403280156_袁圣丹_440924196908123182.pdf_page5_img1.png
2025-07-31 17:04:24 - PDFExtractor - INFO - 添加记录: 袁圣丹
2025-07-31 17:04:24 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 袁圣丹
2025-07-31 17:04:24 - PDFExtractor - INFO - 成功处理PDF: 202403280156_袁圣丹_440924196908123182.pdf
2025-07-31 17:04:24 - PDFExtractor - INFO - 开始处理PDF文件: 202403280157_袁生_440602196710040010.pdf
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img1.png (模式: 主)
2025-07-31 17:04:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:04:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img1.png
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img2.png (模式: 主)
2025-07-31 17:04:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:04:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img2.png
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img3.png (模式: 主)
2025-07-31 17:04:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:04:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img3.png
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img4.png (模式: 主)
2025-07-31 17:04:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:04:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img4.png
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img5.png (模式: 主)
2025-07-31 17:04:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:04:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280157_袁生_440602196710040010.pdf_page1_img5.png
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page3_img1.png (模式: 主)
2025-07-31 17:04:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:04:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280157_袁生_440602196710040010.pdf_page3_img1.png
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page3_img2.png (模式: 主)
2025-07-31 17:04:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:04:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:24 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403280157_袁生_440602196710040010.pdf_page3_img2.png
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page4_img1.png (模式: 主)
2025-07-31 17:04:24 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:04:24 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:24 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.56, 位置: (1616, 856)
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 识别到文本: “, 置信度: 0.71, 位置: (1109, 1048)
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 识别到文本: 右, 置信度: 0.97, 位置: (468, 1160)
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 识别到文本: 左, 置信度: 1.00, 位置: (495, 1160)
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 识别到文本: ‘, 置信度: 0.90, 位置: (528, 1210)
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 识别到文本: 二, 置信度: 0.55, 位置: (1098, 1222)
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 识别到文本: 2i, 置信度: 0.83, 位置: (1125, 1229)
2025-07-31 17:04:26 - PDFExtractor - INFO - 图片识别完成，共找到8个文本块: ./temp_images\202403280157_袁生_440602196710040010.pdf_page4_img1.png
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page5_img1.png (模式: 主)
2025-07-31 17:04:26 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403280157_袁生_440602196710040010.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:04:26 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:26 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 识别到文本: 99, 置信度: 0.79, 位置: (248, 592)
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.54, 位置: (251, 834)
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.51, 位置: (1616, 856)
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (249, 902)
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 识别到文本: ‘中, 置信度: 0.58, 位置: (529, 910)
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 识别到文本: 2i, 置信度: 0.83, 位置: (1125, 1229)
2025-07-31 17:04:28 - PDFExtractor - INFO - 图片识别完成，共找到7个文本块: ./temp_images\202403280157_袁生_440602196710040010.pdf_page5_img1.png
2025-07-31 17:04:28 - PDFExtractor - INFO - 添加记录: 袁生
2025-07-31 17:04:28 - PDFExtractor - INFO - 添加新记录: 袁生
2025-07-31 17:04:28 - PDFExtractor - INFO - 成功处理PDF: 202403280157_袁生_440602196710040010.pdf
2025-07-31 17:04:28 - PDFExtractor - INFO - 开始处理PDF文件: 202403281988-黄星-440902197007051616.pdf
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403281988-黄星-440902197007051616.pdf_page1_img1.png (模式: 主)
2025-07-31 17:04:28 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403281988-黄星-440902197007051616.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:04:28 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:28 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202403281988-黄星-440902197007051616.pdf_page1_img1.png
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403281988-黄星-440902197007051616.pdf_page2_img1.png (模式: 主)
2025-07-31 17:04:28 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403281988-黄星-440902197007051616.pdf_page2_img1.png，尝试替代方法
2025-07-31 17:04:28 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:28 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:30 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:30 - PDFExtractor - DEBUG - 识别到文本: “, 置信度: 0.77, 位置: (285, 407)
2025-07-31 17:04:30 - PDFExtractor - DEBUG - 识别到文本: 电品班, 置信度: 0.64, 位置: (1614, 841)
2025-07-31 17:04:30 - PDFExtractor - DEBUG - 识别到文本: 滑, 置信度: 0.91, 位置: (530, 1198)
2025-07-31 17:04:30 - PDFExtractor - DEBUG - 识别到文本: 23, 置信度: 0.99, 位置: (1154, 1215)
2025-07-31 17:04:30 - PDFExtractor - INFO - 图片识别完成，共找到4个文本块: ./temp_images\202403281988-黄星-440902197007051616.pdf_page2_img1.png
2025-07-31 17:04:30 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202403281988-黄星-440902197007051616.pdf_page3_img1.png (模式: 主)
2025-07-31 17:04:30 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202403281988-黄星-440902197007051616.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:04:30 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:30 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 识别到文本: ‘霜, 置信度: 0.67, 位置: (560, 188)
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 识别到文本: 电品班, 置信度: 0.56, 位置: (1616, 844)
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 识别到文本: “中, 置信度: 0.86, 位置: (560, 843)
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.60, 位置: (396, 1160)
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 识别到文本: 姓, 置信度: 1.00, 位置: (249, 1208)
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 识别到文本: 沟, 置信度: 0.99, 位置: (501, 1202)
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 识别到文本: 一, 置信度: 0.69, 位置: (1116, 1210)
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 识别到文本: 23, 置信度: 0.91, 位置: (1153, 1214)
2025-07-31 17:04:32 - PDFExtractor - INFO - 图片识别完成，共找到8个文本块: ./temp_images\202403281988-黄星-440902197007051616.pdf_page3_img1.png
2025-07-31 17:04:32 - PDFExtractor - INFO - 添加记录: 黄星
2025-07-31 17:04:32 - PDFExtractor - INFO - 添加新记录: 黄星
2025-07-31 17:04:32 - PDFExtractor - INFO - 成功处理PDF: 202403281988-黄星-440902197007051616.pdf
2025-07-31 17:04:32 - PDFExtractor - INFO - 开始处理PDF文件: 202404080339_劳承周_440924196207162998.pdf
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img1.png (模式: 主)
2025-07-31 17:04:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:04:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:32 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img1.png
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img2.png (模式: 主)
2025-07-31 17:04:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:04:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:32 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img2.png
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img3.png (模式: 主)
2025-07-31 17:04:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:04:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:32 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img3.png
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img4.png (模式: 主)
2025-07-31 17:04:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:04:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:33 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:33 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img4.png
2025-07-31 17:04:33 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img5.png (模式: 主)
2025-07-31 17:04:33 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:04:33 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:33 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:33 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:33 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page1_img5.png
2025-07-31 17:04:33 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page4_img1.png (模式: 主)
2025-07-31 17:04:33 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:04:33 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:33 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:33 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:33 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page4_img1.png
2025-07-31 17:04:33 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page5_img1.png (模式: 主)
2025-07-31 17:04:33 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:04:33 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:33 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 识别到文本: （%）, 置信度: 0.77, 位置: (529, 511)
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 识别到文本: “, 置信度: 0.52, 位置: (618, 712)
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.55, 位置: (1616, 854)
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 识别到文本: (%0-%0, 置信度: 0.85, 位置: (532, 1172)
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 识别到文本: 都左, 置信度: 0.72, 位置: (482, 1160)
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 识别到文本: 所, 置信度: 0.75, 位置: (618, 1154)
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 识别到文本: 肌, 置信度: 0.92, 位置: (558, 1216)
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 识别到文本: 2i, 置信度: 0.83, 位置: (1125, 1229)
2025-07-31 17:04:35 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page5_img1.png
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page6_img1.png (模式: 主)
2025-07-31 17:04:35 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:04:35 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:35 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 识别到文本: 名19:, 置信度: 0.66, 位置: (250, 594)
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 识别到文本: '叫, 置信度: 0.55, 位置: (529, 763)
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.54, 位置: (251, 834)
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.91, 位置: (528, 854)
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (250, 901)
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 识别到文本: 彰, 置信度: 0.58, 位置: (1583, 1068)
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1128, 1227)
2025-07-31 17:04:36 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1155, 1229)
2025-07-31 17:04:36 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page6_img1.png
2025-07-31 17:04:37 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page7_img1.png (模式: 主)
2025-07-31 17:04:37 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:04:37 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:37 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:38 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: Au/wuol, 置信度: 0.63, 位置: (177, 174)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: A 69 7 1AS+948, 置信度: 0.62, 位置: (137, 556)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: 医, 置信度: 1.00, 位置: (1152, 486)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: 告, 置信度: 0.89, 位置: (1154, 508)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.85, 位置: (69, 813)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: S 8 S, 置信度: 0.51, 位置: (134, 1033)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: 站出, 置信度: 0.50, 位置: (67, 1124)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: A 98 '0*IAS, 置信度: 0.68, 位置: (133, 1227)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: 名19·虫, 置信度: 0.53, 位置: (67, 1277)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: udq89, 置信度: 0.80, 位置: (107, 1395)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: 00, 置信度: 0.55, 位置: (139, 1398)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: “？, 置信度: 0.69, 位置: (105, 1494)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: “回, 置信度: 0.63, 位置: (137, 1492)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: udq9, 置信度: 0.78, 位置: (106, 1636)
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 识别到文本: “树密？, 置信度: 0.56, 位置: (105, 1716)
2025-07-31 17:04:39 - PDFExtractor - INFO - 图片识别完成，共找到15个文本块: ./temp_images\202404080339_劳承周_440924196207162998.pdf_page7_img1.png
2025-07-31 17:04:39 - PDFExtractor - INFO - 添加记录: 劳承周
2025-07-31 17:04:39 - PDFExtractor - INFO - 添加新记录: 劳承周
2025-07-31 17:04:39 - PDFExtractor - INFO - 成功处理PDF: 202404080339_劳承周_440924196207162998.pdf
2025-07-31 17:04:39 - PDFExtractor - INFO - 开始处理PDF文件: 202404080340_劳承文_440924196401172970.pdf
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img1.png (模式: 主)
2025-07-31 17:04:39 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:04:39 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:39 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img1.png
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img2.png (模式: 主)
2025-07-31 17:04:39 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:04:39 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:39 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:40 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img2.png
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img3.png (模式: 主)
2025-07-31 17:04:40 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:04:40 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:40 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img3.png
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img4.png (模式: 主)
2025-07-31 17:04:40 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:04:40 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:40 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img4.png
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img5.png (模式: 主)
2025-07-31 17:04:40 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:04:40 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:40 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page1_img5.png
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page3_img1.png (模式: 主)
2025-07-31 17:04:40 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:04:40 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:40 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page3_img1.png
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page5_img1.png (模式: 主)
2025-07-31 17:04:40 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:04:40 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:40 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:42 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:42 - PDFExtractor - DEBUG - 识别到文本: 09：强, 置信度: 0.62, 位置: (250, 594)
2025-07-31 17:04:42 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:04:42 - PDFExtractor - DEBUG - 识别到文本: 2i, 置信度: 0.83, 位置: (1125, 1229)
2025-07-31 17:04:42 - PDFExtractor - INFO - 图片识别完成，共找到3个文本块: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page5_img1.png
2025-07-31 17:04:42 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page6_img1.png (模式: 主)
2025-07-31 17:04:42 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:04:42 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:42 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 识别到文本: 09：强, 置信度: 0.59, 位置: (249, 594)
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.54, 位置: (251, 834)
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.53, 位置: (1616, 857)
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 识别到文本: ‘中, 置信度: 0.60, 位置: (559, 852)
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (249, 902)
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1145, 1227)
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1170, 1229)
2025-07-31 17:04:44 - PDFExtractor - INFO - 图片识别完成，共找到7个文本块: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page6_img1.png
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page7_img1.png (模式: 主)
2025-07-31 17:04:44 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:04:44 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:44 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:46 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: A/os/gg, 置信度: 0.54, 位置: (179, 222)
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.96, 位置: (69, 813)
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: Au 80°0 *TAS -, 置信度: 0.76, 位置: (133, 1236)
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: 19, 置信度: 0.90, 位置: (102, 1193)
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: 名09:强虫, 置信度: 0.51, 位置: (68, 1277)
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: udqc9, 置信度: 0.83, 位置: (107, 1394)
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: sm 86, 置信度: 0.63, 位置: (140, 1400)
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: “？, 置信度: 0.69, 位置: (105, 1496)
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: udq 99, 置信度: 0.74, 位置: (105, 1632)
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 识别到文本: “树密？, 置信度: 0.56, 位置: (105, 1716)
2025-07-31 17:04:47 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202404080340_劳承文_440924196401172970.pdf_page7_img1.png
2025-07-31 17:04:47 - PDFExtractor - INFO - 添加记录: 劳承文
2025-07-31 17:04:47 - PDFExtractor - INFO - 添加新记录: 劳承文
2025-07-31 17:04:47 - PDFExtractor - INFO - 成功处理PDF: 202404080340_劳承文_440924196401172970.pdf
2025-07-31 17:04:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404080341_袁华英_440924196210273189.pdf
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img1.png (模式: 主)
2025-07-31 17:04:47 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:04:47 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:47 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img1.png
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img2.png (模式: 主)
2025-07-31 17:04:47 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:04:47 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:47 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img2.png
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img3.png (模式: 主)
2025-07-31 17:04:47 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:04:47 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:47 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img3.png
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img4.png (模式: 主)
2025-07-31 17:04:47 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:04:47 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:47 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img4.png
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img5.png (模式: 主)
2025-07-31 17:04:47 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:04:47 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:47 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page1_img5.png
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page4_img1.png (模式: 主)
2025-07-31 17:04:47 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:04:47 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:47 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:48 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:48 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page4_img1.png
2025-07-31 17:04:48 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page5_img1.png (模式: 主)
2025-07-31 17:04:48 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:04:48 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:48 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 识别到文本: ：（%8-%）, 置信度: 0.65, 位置: (529, 550)
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 识别到文本: （%9-9, 置信度: 0.53, 位置: (498, 570)
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 识别到文本: 回, 置信度: 1.00, 位置: (529, 1219)
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 识别到文本: 第, 置信度: 1.00, 位置: (558, 1219)
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 识别到文本: 管, 置信度: 1.00, 位置: (1151, 1219)
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1215, 1227)
2025-07-31 17:04:50 - PDFExtractor - INFO - 图片识别完成，共找到7个文本块: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page5_img1.png
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page6_img1.png (模式: 主)
2025-07-31 17:04:50 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:04:50 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:50 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: 名19:, 置信度: 0.66, 位置: (250, 594)
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.97, 位置: (560, 854)
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (250, 901)
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: K, 置信度: 0.52, 位置: (500, 901)
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: 影, 置信度: 1.00, 位置: (498, 1190)
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: 号号, 置信度: 1.00, 位置: (485, 1218)
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1128, 1228)
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1154, 1228)
2025-07-31 17:04:51 - PDFExtractor - DEBUG - 识别到文本: 4, 置信度: 1.00, 位置: (1184, 1229)
2025-07-31 17:04:51 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page6_img1.png
2025-07-31 17:04:52 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page7_img1.png (模式: 主)
2025-07-31 17:04:52 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:04:52 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:52 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:54 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本: Au/wuol, 置信度: 0.60, 位置: (177, 170)
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本: 告, 置信度: 0.89, 位置: (1154, 508)
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.81, 位置: (69, 813)
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本:  9'0 TAS, 置信度: 0.51, 位置: (133, 1226)
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本: 名19·虫, 置信度: 0.53, 位置: (67, 1277)
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本: dq, 置信度: 0.72, 位置: (107, 1396)
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本: “？, 置信度: 0.69, 位置: (105, 1494)
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本: “回, 置信度: 0.63, 位置: (137, 1492)
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本: dq , 置信度: 0.67, 位置: (106, 1636)
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 识别到文本: “树密？, 置信度: 0.56, 位置: (105, 1716)
2025-07-31 17:04:55 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202404080341_袁华英_440924196210273189.pdf_page7_img1.png
2025-07-31 17:04:55 - PDFExtractor - INFO - 添加记录: 袁华英
2025-07-31 17:04:55 - PDFExtractor - INFO - 添加新记录: 袁华英
2025-07-31 17:04:55 - PDFExtractor - INFO - 成功处理PDF: 202404080341_袁华英_440924196210273189.pdf
2025-07-31 17:04:55 - PDFExtractor - INFO - 开始处理PDF文件: 202404190057_罗庆欢_441423196005222718.pdf
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img1.png (模式: 主)
2025-07-31 17:04:55 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:04:55 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:55 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img1.png
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img2.png (模式: 主)
2025-07-31 17:04:55 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:04:55 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:55 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img2.png
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img3.png (模式: 主)
2025-07-31 17:04:55 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:04:55 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:55 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img3.png
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img4.png (模式: 主)
2025-07-31 17:04:55 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:04:55 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:55 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img4.png
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img5.png (模式: 主)
2025-07-31 17:04:55 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:04:55 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:55 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page1_img5.png
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page3_img1.png (模式: 主)
2025-07-31 17:04:55 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:04:55 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:55 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page3_img1.png
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page3_img2.png (模式: 主)
2025-07-31 17:04:55 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:04:55 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:55 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:55 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page3_img2.png
2025-07-31 17:04:56 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page5_img1.png (模式: 主)
2025-07-31 17:04:56 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:04:56 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:56 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:04:57 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:04:57 - PDFExtractor - DEBUG - 识别到文本: “电品班, 置信度: 0.61, 位置: (1614, 853)
2025-07-31 17:04:57 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:04:57 - PDFExtractor - DEBUG - 识别到文本: 左, 置信度: 1.00, 位置: (469, 1160)
2025-07-31 17:04:57 - PDFExtractor - INFO - 图片识别完成，共找到3个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page5_img1.png
2025-07-31 17:04:58 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page6_img1.png (模式: 主)
2025-07-31 17:04:58 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:04:58 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:04:58 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.54, 位置: (251, 834)
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (250, 901)
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 识别到文本: 彰, 置信度: 0.58, 位置: (1583, 1068)
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 识别到文本: °中, 置信度: 0.97, 位置: (498, 1107)
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 识别到文本: 变, 置信度: 1.00, 位置: (470, 1215)
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1144, 1227)
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1170, 1229)
2025-07-31 17:05:00 - PDFExtractor - INFO - 图片识别完成，共找到8个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page6_img1.png
2025-07-31 17:05:00 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page7_img1.png (模式: 主)
2025-07-31 17:05:00 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:05:00 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:01 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:02 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 识别到文本:  1I'7 1S+9 -, 置信度: 0.57, 位置: (137, 567)
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.75, 位置: (69, 813)
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 识别到文本: A LO'I IAS -, 置信度: 0.71, 位置: (135, 1236)
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 识别到文本: udq69, 置信度: 0.98, 位置: (107, 1392)
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.75, 位置: (69, 1469)
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 识别到文本: “？, 置信度: 0.69, 位置: (105, 1496)
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 识别到文本: udq 69, 置信度: 0.90, 位置: (105, 1632)
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 识别到文本: 7, 置信度: 0.55, 位置: (1146, 1630)
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 识别到文本: “树密？, 置信度: 0.56, 位置: (105, 1716)
2025-07-31 17:05:03 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202404190057_罗庆欢_441423196005222718.pdf_page7_img1.png
2025-07-31 17:05:03 - PDFExtractor - INFO - 添加记录: 罗庆欢
2025-07-31 17:05:03 - PDFExtractor - INFO - 添加新记录: 罗庆欢
2025-07-31 17:05:03 - PDFExtractor - INFO - 成功处理PDF: 202404190057_罗庆欢_441423196005222718.pdf
2025-07-31 17:05:03 - PDFExtractor - INFO - 开始处理PDF文件: 202404190061_钟新梅_441425196808253908.pdf
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img1.png (模式: 主)
2025-07-31 17:05:03 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:05:03 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:03 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img1.png
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img2.png (模式: 主)
2025-07-31 17:05:03 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:05:03 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:03 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img2.png
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img3.png (模式: 主)
2025-07-31 17:05:03 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:05:03 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:03 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img3.png
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img4.png (模式: 主)
2025-07-31 17:05:03 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:05:03 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:03 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img4.png
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img5.png (模式: 主)
2025-07-31 17:05:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:05:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page1_img5.png
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page3_img1.png (模式: 主)
2025-07-31 17:05:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:05:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page3_img1.png
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page3_img2.png (模式: 主)
2025-07-31 17:05:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:05:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:04 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page3_img2.png
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page5_img1.png (模式: 主)
2025-07-31 17:05:04 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:05:04 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:04 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:06 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:06 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.51, 位置: (1616, 856)
2025-07-31 17:05:06 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:05:06 - PDFExtractor - DEBUG - 识别到文本: 右左, 置信度: 0.77, 位置: (484, 1160)
2025-07-31 17:05:06 - PDFExtractor - INFO - 图片识别完成，共找到3个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page5_img1.png
2025-07-31 17:05:06 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page6_img1.png (模式: 主)
2025-07-31 17:05:06 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:05:06 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:06 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:07 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:07 - PDFExtractor - DEBUG - 识别到文本: °￥, 置信度: 0.63, 位置: (470, 143)
2025-07-31 17:05:07 - PDFExtractor - DEBUG - 识别到文本: 中中, 置信度: 0.63, 位置: (498, 908)
2025-07-31 17:05:07 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.53, 位置: (1616, 857)
2025-07-31 17:05:07 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.94, 位置: (530, 854)
2025-07-31 17:05:07 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (250, 901)
2025-07-31 17:05:07 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1145, 1227)
2025-07-31 17:05:07 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1170, 1229)
2025-07-31 17:05:07 - PDFExtractor - INFO - 图片识别完成，共找到7个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page6_img1.png
2025-07-31 17:05:08 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page7_img1.png (模式: 主)
2025-07-31 17:05:08 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:05:08 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:08 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:09 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本:  06 '7 TAS+94 -, 置信度: 0.66, 位置: (137, 567)
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.85, 位置: (69, 813)
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本: 站出, 置信度: 0.50, 位置: (67, 1124)
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本: A 00'I *IAS -, 置信度: 0.73, 位置: (133, 1234)
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本: 4W061, 置信度: 0.64, 位置: (102, 1194)
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本: udq89, 置信度: 0.89, 位置: (107, 1391)
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本: sm 86, 置信度: 0.63, 位置: (140, 1400)
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本: “？, 置信度: 0.69, 位置: (105, 1496)
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本: udq 89, 置信度: 0.88, 位置: (105, 1632)
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 识别到文本: “树密？, 置信度: 0.56, 位置: (105, 1716)
2025-07-31 17:05:11 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202404190061_钟新梅_441425196808253908.pdf_page7_img1.png
2025-07-31 17:05:11 - PDFExtractor - INFO - 添加记录: 钟新梅
2025-07-31 17:05:11 - PDFExtractor - INFO - 添加新记录: 钟新梅
2025-07-31 17:05:11 - PDFExtractor - INFO - 成功处理PDF: 202404190061_钟新梅_441425196808253908.pdf
2025-07-31 17:05:11 - PDFExtractor - INFO - 开始处理PDF文件: 202404190067_袁国泉_H60790359.pdf
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img1.png (模式: 主)
2025-07-31 17:05:11 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:05:11 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:11 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img1.png
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img2.png (模式: 主)
2025-07-31 17:05:11 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:05:11 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:11 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img2.png
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img3.png (模式: 主)
2025-07-31 17:05:11 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:05:11 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:11 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img3.png
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img4.png (模式: 主)
2025-07-31 17:05:11 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:05:11 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:11 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img4.png
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img5.png (模式: 主)
2025-07-31 17:05:11 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:05:11 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:11 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190067_袁国泉_H60790359.pdf_page1_img5.png
2025-07-31 17:05:11 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page3_img1.png (模式: 主)
2025-07-31 17:05:12 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:05:12 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:12 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:12 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:12 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190067_袁国泉_H60790359.pdf_page3_img1.png
2025-07-31 17:05:12 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page4_img1.png (模式: 主)
2025-07-31 17:05:12 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:05:12 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:12 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 识别到文本: 69, 置信度: 0.65, 位置: (248, 592)
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.54, 位置: (251, 834)
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 识别到文本: 电品, 置信度: 0.53, 位置: (1616, 857)
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 识别到文本: “中, 置信度: 0.72, 位置: (530, 852)
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 1.00, 位置: (251, 902)
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 识别到文本: 常, 置信度: 1.00, 位置: (470, 1218)
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 识别到文本: 一, 置信度: 0.59, 位置: (1116, 1224)
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1140, 1229)
2025-07-31 17:05:14 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202404190067_袁国泉_H60790359.pdf_page4_img1.png
2025-07-31 17:05:14 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page5_img1.png (模式: 主)
2025-07-31 17:05:14 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190067_袁国泉_H60790359.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:05:15 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:15 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:16 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: AW 007 TAS+9AX -, 置信度: 0.55, 位置: (137, 568)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.66, 位置: (69, 813)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本:  , 置信度: 0.57, 位置: (135, 800)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: 站出, 置信度: 0.50, 位置: (67, 1124)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本:  70AS-, 置信度: 0.63, 位置: (135, 1236)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: 名69虫, 置信度: 0.57, 位置: (67, 1277)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: udq62, 置信度: 0.82, 位置: (107, 1390)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.59, 位置: (68, 1468)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: “？, 置信度: 0.69, 位置: (105, 1496)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: 品, 置信度: 0.80, 位置: (1122, 1485)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: 国, 置信度: 0.64, 位置: (66, 1676)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: udq62, 置信度: 0.58, 位置: (105, 1628)
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 识别到文本: “树世？, 置信度: 0.54, 位置: (105, 1716)
2025-07-31 17:05:17 - PDFExtractor - INFO - 图片识别完成，共找到13个文本块: ./temp_images\202404190067_袁国泉_H60790359.pdf_page5_img1.png
2025-07-31 17:05:17 - PDFExtractor - INFO - 添加记录: 袁国泉
2025-07-31 17:05:17 - PDFExtractor - INFO - 添加新记录: 袁国泉
2025-07-31 17:05:17 - PDFExtractor - INFO - 成功处理PDF: 202404190067_袁国泉_H60790359.pdf
2025-07-31 17:05:17 - PDFExtractor - INFO - 开始处理PDF文件: 202404190091-黄星-440902197007051616.pdf
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190091-黄星-440902197007051616.pdf_page1_img1.png (模式: 主)
2025-07-31 17:05:17 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190091-黄星-440902197007051616.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:05:17 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:17 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:17 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404190091-黄星-440902197007051616.pdf_page1_img1.png
2025-07-31 17:05:18 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404190091-黄星-440902197007051616.pdf_page2_img1.png (模式: 主)
2025-07-31 17:05:18 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404190091-黄星-440902197007051616.pdf_page2_img1.png，尝试替代方法
2025-07-31 17:05:18 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:18 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: ‘s/u I'I ·Ad , 置信度: 0.72, 位置: (753, 387)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 重国, 置信度: 0.64, 位置: (398, 408)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: :s/ug '0, 置信度: 0.73, 位置: (751, 553)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: ·AL, 置信度: 0.74, 位置: (753, 639)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 年, 置信度: 1.00, 位置: (190, 678)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 科, 置信度: 1.00, 位置: (228, 683)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: :s/Ⅲ 9' :AV, 置信度: 0.61, 位置: (752, 792)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.93, 位置: (194, 872)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: MdAT , 置信度: 0.57, 位置: (696, 921)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 长, 置信度: 0.81, 位置: (228, 943)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: :s/m ≤*0/ 80 : (V/), 置信度: 0.78, 位置: (752, 1058)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 1.00, 位置: (193, 948)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 雪, 置信度: 0.55, 位置: (192, 1152)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 左, 置信度: 1.00, 位置: (1180, 1096)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 89  , 置信度: 0.52, 位置: (779, 1204)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 度, 置信度: 1.00, 位置: (1258, 1200)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: N, 置信度: 0.88, 位置: (751, 1225)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 各全房, 置信度: 1.00, 位置: (836, 1228)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 心下, 置信度: 0.99, 位置: (906, 1227)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 组, 置信度: 1.00, 位置: (946, 1229)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 1.00, 位置: (1288, 1228)
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 识别到文本: 4, 置信度: 0.60, 位置: (495, 1240)
2025-07-31 17:05:20 - PDFExtractor - INFO - 图片识别完成，共找到22个文本块: ./temp_images\202404190091-黄星-440902197007051616.pdf_page2_img1.png
2025-07-31 17:05:20 - PDFExtractor - INFO - 添加记录: 黄星
2025-07-31 17:05:20 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 黄星
2025-07-31 17:05:20 - PDFExtractor - INFO - 成功处理PDF: 202404190091-黄星-440902197007051616.pdf
2025-07-31 17:05:20 - PDFExtractor - INFO - 开始处理PDF文件: 202404221692_袁圣浩_440924196807213170.pdf
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img1.png (模式: 主)
2025-07-31 17:05:20 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:05:20 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:20 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img1.png
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img2.png (模式: 主)
2025-07-31 17:05:20 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:05:20 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:20 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img2.png
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img3.png (模式: 主)
2025-07-31 17:05:20 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:05:20 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:20 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img3.png
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img4.png (模式: 主)
2025-07-31 17:05:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:05:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img4.png
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img5.png (模式: 主)
2025-07-31 17:05:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:05:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page1_img5.png
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page4_img1.png (模式: 主)
2025-07-31 17:05:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:05:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page4_img1.png
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page4_img2.png (模式: 主)
2025-07-31 17:05:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page4_img2.png，尝试替代方法
2025-07-31 17:05:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:21 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page4_img2.png
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page5_img1.png (模式: 主)
2025-07-31 17:05:21 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:05:21 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:21 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:23 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:23 - PDFExtractor - DEBUG - 识别到文本: 00:-0-电, 置信度: 0.51, 位置: (1670, 320)
2025-07-31 17:05:23 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:05:23 - PDFExtractor - DEBUG - 识别到文本: 桥, 置信度: 1.00, 位置: (560, 1214)
2025-07-31 17:05:23 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1140, 1227)
2025-07-31 17:05:23 - PDFExtractor - INFO - 图片识别完成，共找到4个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page5_img1.png
2025-07-31 17:05:23 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page6_img1.png (模式: 主)
2025-07-31 17:05:23 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:05:23 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:23 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:25 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:25 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.54, 位置: (251, 834)
2025-07-31 17:05:25 - PDFExtractor - DEBUG - 识别到文本: ‘中, 置信度: 0.85, 位置: (589, 823)
2025-07-31 17:05:25 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.98, 位置: (250, 901)
2025-07-31 17:05:25 - PDFExtractor - DEBUG - 识别到文本: 管, 置信度: 1.00, 位置: (617, 1164)
2025-07-31 17:05:25 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1144, 1228)
2025-07-31 17:05:25 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1169, 1229)
2025-07-31 17:05:25 - PDFExtractor - INFO - 图片识别完成，共找到6个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page6_img1.png
2025-07-31 17:05:26 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page7_img1.png (模式: 主)
2025-07-31 17:05:26 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:05:26 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:26 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:27 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: Au/wuol, 置信度: 0.60, 位置: (177, 170)
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.85, 位置: (69, 813)
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: A '0 AS, 置信度: 0.59, 位置: (135, 1229)
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: udq29, 置信度: 0.95, 位置: (107, 1393)
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: s 949, 置信度: 0.53, 位置: (139, 1400)
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.80, 位置: (69, 1468)
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: “？, 置信度: 0.69, 位置: (105, 1494)
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: “回, 置信度: 0.63, 位置: (137, 1492)
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: udq29, 置信度: 0.79, 位置: (104, 1636)
2025-07-31 17:05:28 - PDFExtractor - DEBUG - 识别到文本: “树密？, 置信度: 0.56, 位置: (105, 1716)
2025-07-31 17:05:28 - PDFExtractor - INFO - 图片识别完成，共找到10个文本块: ./temp_images\202404221692_袁圣浩_440924196807213170.pdf_page7_img1.png
2025-07-31 17:05:28 - PDFExtractor - INFO - 添加记录: 袁圣浩
2025-07-31 17:05:28 - PDFExtractor - INFO - 添加新记录: 袁圣浩
2025-07-31 17:05:28 - PDFExtractor - INFO - 成功处理PDF: 202404221692_袁圣浩_440924196807213170.pdf
2025-07-31 17:05:28 - PDFExtractor - INFO - 开始处理PDF文件: 202404221693_张燕平_440924197102273661.pdf
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img1.png (模式: 主)
2025-07-31 17:05:29 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:05:29 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:29 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img1.png
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img2.png (模式: 主)
2025-07-31 17:05:29 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:05:29 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:29 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img2.png
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img3.png (模式: 主)
2025-07-31 17:05:29 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:05:29 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:29 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img3.png
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img4.png (模式: 主)
2025-07-31 17:05:29 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:05:29 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:29 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img4.png
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img5.png (模式: 主)
2025-07-31 17:05:29 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:05:29 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:29 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page1_img5.png
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page4_img1.png (模式: 主)
2025-07-31 17:05:29 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:05:29 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:29 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page4_img1.png
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page4_img2.png (模式: 主)
2025-07-31 17:05:29 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page4_img2.png，尝试替代方法
2025-07-31 17:05:29 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:29 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:29 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page4_img2.png
2025-07-31 17:05:30 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page5_img1.png (模式: 主)
2025-07-31 17:05:30 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:05:30 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:30 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:32 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:32 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:05:32 - PDFExtractor - DEBUG - 识别到文本: 州慢, 置信度: 0.60, 位置: (1589, 1024)
2025-07-31 17:05:32 - PDFExtractor - DEBUG - 识别到文本: 右左, 置信度: 0.80, 位置: (484, 1160)
2025-07-31 17:05:32 - PDFExtractor - INFO - 图片识别完成，共找到3个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page5_img1.png
2025-07-31 17:05:32 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page6_img1.png (模式: 主)
2025-07-31 17:05:32 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:05:32 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:32 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 识别到文本: 基, 置信度: 1.00, 位置: (556, 142)
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 识别到文本: ，, 置信度: 0.63, 位置: (444, 476)
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.99, 位置: (252, 901)
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 识别到文本: 彰, 置信度: 0.58, 位置: (1583, 1068)
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 识别到文本: 变, 置信度: 1.00, 位置: (470, 1215)
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 识别到文本: ：, 置信度: 0.50, 位置: (1116, 1222)
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1144, 1228)
2025-07-31 17:05:34 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1169, 1229)
2025-07-31 17:05:34 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page6_img1.png
2025-07-31 17:05:35 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page7_img1.png (模式: 主)
2025-07-31 17:05:35 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:05:35 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:35 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:36 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: Au/wuol, 置信度: 0.60, 位置: (177, 170)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: A 60 7 *TAS+98, 置信度: 0.59, 位置: (138, 556)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.84, 位置: (69, 813)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: 站出, 置信度: 0.50, 位置: (67, 1124)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: A O2'O*IAS, 置信度: 0.64, 位置: (133, 1227)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: udq99, 置信度: 0.98, 位置: (107, 1394)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: s 90, 置信度: 0.67, 位置: (139, 1398)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: “？, 置信度: 0.69, 位置: (105, 1494)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: 1, 置信度: 0.60, 位置: (1120, 1460)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: udq99, 置信度: 0.89, 位置: (105, 1634)
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 识别到文本: “树密？, 置信度: 0.56, 位置: (105, 1716)
2025-07-31 17:05:37 - PDFExtractor - INFO - 图片识别完成，共找到11个文本块: ./temp_images\202404221693_张燕平_440924197102273661.pdf_page7_img1.png
2025-07-31 17:05:37 - PDFExtractor - INFO - 添加记录: 张燕平
2025-07-31 17:05:37 - PDFExtractor - INFO - 添加新记录: 张燕平
2025-07-31 17:05:37 - PDFExtractor - INFO - 成功处理PDF: 202404221693_张燕平_440924197102273661.pdf
2025-07-31 17:05:37 - PDFExtractor - INFO - 开始处理PDF文件: 202404221695_袁圣泽_440924197404273173.pdf
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img1.png (模式: 主)
2025-07-31 17:05:37 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:05:37 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:37 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img1.png
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img2.png (模式: 主)
2025-07-31 17:05:37 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:05:37 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:37 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img2.png
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img3.png (模式: 主)
2025-07-31 17:05:37 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:05:37 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:37 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img3.png
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img4.png (模式: 主)
2025-07-31 17:05:37 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:05:37 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:37 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img4.png
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img5.png (模式: 主)
2025-07-31 17:05:37 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:05:37 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:37 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:38 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:38 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page1_img5.png
2025-07-31 17:05:38 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page3_img1.png (模式: 主)
2025-07-31 17:05:38 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page3_img1.png，尝试替代方法
2025-07-31 17:05:38 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:38 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:38 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:38 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page3_img1.png
2025-07-31 17:05:38 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page3_img2.png (模式: 主)
2025-07-31 17:05:38 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page3_img2.png，尝试替代方法
2025-07-31 17:05:38 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:38 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:38 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:38 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page3_img2.png
2025-07-31 17:05:38 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page5_img1.png (模式: 主)
2025-07-31 17:05:38 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:05:38 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:38 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 识别到文本: 第, 置信度: 1.00, 位置: (529, 605)
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 识别到文本: 管, 置信度: 1.00, 位置: (586, 710)
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 识别到文本: °%08-%0, 置信度: 0.78, 位置: (562, 1176)
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 识别到文本: 都左, 置信度: 0.74, 位置: (481, 1174)
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.99, 位置: (530, 1220)
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 识别到文本: ：, 置信度: 0.50, 位置: (1116, 1222)
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1140, 1229)
2025-07-31 17:05:40 - PDFExtractor - INFO - 图片识别完成，共找到8个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page5_img1.png
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page6_img1.png (模式: 主)
2025-07-31 17:05:40 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:05:40 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:40 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 识别到文本: ‘中, 置信度: 0.71, 位置: (529, 326)
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 识别到文本: 邮, 置信度: 0.54, 位置: (251, 834)
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 识别到文本: 电品班, 置信度: 0.58, 位置: (1616, 855)
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (250, 901)
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 识别到文本: 激, 置信度: 0.55, 位置: (396, 1174)
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 识别到文本: 常, 置信度: 1.00, 位置: (470, 1218)
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 识别到文本: ：, 置信度: 0.77, 位置: (1118, 1224)
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1144, 1228)
2025-07-31 17:05:42 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1169, 1229)
2025-07-31 17:05:42 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page6_img1.png
2025-07-31 17:05:43 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page7_img1.png (模式: 主)
2025-07-31 17:05:43 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:05:43 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:43 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:44 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 识别到文本: 身专端）, 置信度: 0.55, 位置: (1241, 243)
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 识别到文本: 其, 置信度: 0.59, 位置: (1242, 360)
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 识别到文本: A SL'T TAS+9, 置信度: 0.58, 位置: (135, 546)
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 识别到文本:  79'01AS , 置信度: 0.65, 位置: (134, 1240)
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 识别到文本: q s, 置信度: 0.53, 位置: (104, 1407)
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 识别到文本: “回O, 置信度: 0.54, 位置: (135, 1510)
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 识别到文本: “树州, 置信度: 0.52, 位置: (102, 1512)
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 识别到文本: 断, 置信度: 0.57, 位置: (1149, 1741)
2025-07-31 17:05:45 - PDFExtractor - INFO - 图片识别完成，共找到8个文本块: ./temp_images\202404221695_袁圣泽_440924197404273173.pdf_page7_img1.png
2025-07-31 17:05:45 - PDFExtractor - INFO - 添加记录: 袁圣泽
2025-07-31 17:05:45 - PDFExtractor - INFO - 添加新记录: 袁圣泽
2025-07-31 17:05:45 - PDFExtractor - INFO - 成功处理PDF: 202404221695_袁圣泽_440924197404273173.pdf
2025-07-31 17:05:45 - PDFExtractor - INFO - 开始处理PDF文件: 202404221696_叶海燕_440924197402183422.pdf
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img1.png (模式: 主)
2025-07-31 17:05:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:05:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img1.png
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img2.png (模式: 主)
2025-07-31 17:05:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:05:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img2.png
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img3.png (模式: 主)
2025-07-31 17:05:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:05:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img3.png
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img4.png (模式: 主)
2025-07-31 17:05:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:05:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:45 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img4.png
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img5.png (模式: 主)
2025-07-31 17:05:45 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:05:45 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:45 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:46 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:46 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page1_img5.png
2025-07-31 17:05:46 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page4_img1.png (模式: 主)
2025-07-31 17:05:46 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page4_img1.png，尝试替代方法
2025-07-31 17:05:46 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:46 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:46 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:46 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page4_img1.png
2025-07-31 17:05:46 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page4_img2.png (模式: 主)
2025-07-31 17:05:46 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page4_img2.png，尝试替代方法
2025-07-31 17:05:46 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:46 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:46 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:46 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page4_img2.png
2025-07-31 17:05:46 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page5_img1.png (模式: 主)
2025-07-31 17:05:46 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:05:46 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:46 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:47 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:47 - PDFExtractor - DEBUG - 识别到文本: 电品班, 置信度: 0.58, 位置: (1616, 855)
2025-07-31 17:05:47 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:05:47 - PDFExtractor - DEBUG - 识别到文本: 古“, 置信度: 0.61, 位置: (252, 1144)
2025-07-31 17:05:47 - PDFExtractor - DEBUG - 识别到文本: 右, 置信度: 0.98, 位置: (469, 1160)
2025-07-31 17:05:47 - PDFExtractor - DEBUG - 识别到文本: 左, 置信度: 1.00, 位置: (496, 1160)
2025-07-31 17:05:47 - PDFExtractor - DEBUG - 识别到文本: 滑, 置信度: 0.99, 位置: (528, 1212)
2025-07-31 17:05:48 - PDFExtractor - DEBUG - 识别到文本: ：, 置信度: 0.50, 位置: (1116, 1222)
2025-07-31 17:05:48 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1140, 1229)
2025-07-31 17:05:48 - PDFExtractor - INFO - 图片识别完成，共找到8个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page5_img1.png
2025-07-31 17:05:48 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page6_img1.png (模式: 主)
2025-07-31 17:05:48 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page6_img1.png，尝试替代方法
2025-07-31 17:05:48 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:48 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 识别到文本: IMZL, 置信度: 0.61, 位置: (468, 144)
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 识别到文本: 11, 置信度: 0.76, 位置: (472, 617)
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.84, 位置: (249, 902)
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 识别到文本: ‘中, 置信度: 0.82, 位置: (588, 984)
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 识别到文本: 古“, 置信度: 0.58, 位置: (252, 1142)
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 识别到文本: 呈, 置信度: 0.92, 位置: (502, 1219)
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 识别到文本: ：, 置信度: 0.77, 位置: (1118, 1224)
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1144, 1228)
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1169, 1229)
2025-07-31 17:05:50 - PDFExtractor - INFO - 图片识别完成，共找到9个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page6_img1.png
2025-07-31 17:05:50 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page7_img1.png (模式: 主)
2025-07-31 17:05:50 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page7_img1.png，尝试替代方法
2025-07-31 17:05:51 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:51 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:52 - PDFExtractor - INFO - 检测到心电图报告，需要额外旋转270度
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: Au/wuol, 置信度: 0.60, 位置: (177, 170)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: Am 007TAS+9, 置信度: 0.63, 位置: (139, 556)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.74, 位置: (69, 813)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: S88 , 置信度: 0.55, 位置: (134, 1033)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: AW 88 0*TAS, 置信度: 0.77, 位置: (135, 1229)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: udqc9, 置信度: 0.82, 位置: (107, 1396)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本:  9, 置信度: 0.56, 位置: (139, 1398)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: “？, 置信度: 0.69, 位置: (105, 1494)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: “回, 置信度: 0.63, 位置: (137, 1492)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: 士“, 置信度: 0.68, 位置: (65, 1678)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: wdq s9, 置信度: 0.63, 位置: (105, 1635)
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 识别到文本: “树密？, 置信度: 0.56, 位置: (105, 1716)
2025-07-31 17:05:53 - PDFExtractor - INFO - 图片识别完成，共找到12个文本块: ./temp_images\202404221696_叶海燕_440924197402183422.pdf_page7_img1.png
2025-07-31 17:05:53 - PDFExtractor - INFO - 添加记录: 叶海燕
2025-07-31 17:05:53 - PDFExtractor - INFO - 添加新记录: 叶海燕
2025-07-31 17:05:53 - PDFExtractor - INFO - 成功处理PDF: 202404221696_叶海燕_440924197402183422.pdf
2025-07-31 17:05:53 - PDFExtractor - INFO - 开始处理PDF文件: 202404290137-袁圣丹-440924196908123182.pdf
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img1.png (模式: 主)
2025-07-31 17:05:53 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:05:53 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:53 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img1.png
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img2.png (模式: 主)
2025-07-31 17:05:53 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:05:53 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:53 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img2.png
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img3.png (模式: 主)
2025-07-31 17:05:53 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:05:53 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:53 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img3.png
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img4.png (模式: 主)
2025-07-31 17:05:53 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img4.png，尝试替代方法
2025-07-31 17:05:53 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:53 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img4.png
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img5.png (模式: 主)
2025-07-31 17:05:53 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img5.png，尝试替代方法
2025-07-31 17:05:53 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:53 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page1_img5.png
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page5_img1.png (模式: 主)
2025-07-31 17:05:53 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page5_img1.png，尝试替代方法
2025-07-31 17:05:53 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:53 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page5_img1.png
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page5_img2.png (模式: 主)
2025-07-31 17:05:53 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page5_img2.png，尝试替代方法
2025-07-31 17:05:53 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:53 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:53 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page5_img2.png
2025-07-31 17:05:54 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page11_img1.png (模式: 主)
2025-07-31 17:05:54 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page11_img1.png，尝试替代方法
2025-07-31 17:05:54 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:54 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: “坐, 置信度: 0.51, 位置: (284, 332)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: “日号, 置信度: 0.54, 位置: (1430, 353)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 中, 置信度: 0.72, 位置: (352, 486)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: :00:91 10-90-1707, 置信度: 0.54, 位置: (1472, 536)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 源, 置信度: 1.00, 位置: (638, 494)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 图电, 置信度: 0.68, 位置: (1432, 596)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 微, 置信度: 0.64, 位置: (666, 611)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 口, 置信度: 0.59, 位置: (705, 609)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 口, 置信度: 0.69, 位置: (774, 609)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: %>, 置信度: 0.83, 位置: (512, 908)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 女, 置信度: 1.00, 位置: (279, 810)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: ）, 置信度: 0.58, 位置: (929, 1000)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 1, 置信度: 0.63, 位置: (130, 891)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: %0<, 置信度: 0.82, 位置: (513, 1138)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 中口, 置信度: 0.90, 位置: (612, 1139)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 重口, 置信度: 0.92, 位置: (647, 1138)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 王口, 置信度: 0.84, 位置: (752, 1138)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 鲁, 置信度: 0.57, 位置: (479, 1148)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 口, 置信度: 0.93, 位置: (823, 1166)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 口, 置信度: 0.92, 位置: (928, 1166)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 口, 置信度: 0.92, 位置: (963, 1166)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 口, 置信度: 0.81, 位置: (1094, 1165)
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 识别到文本: 口, 置信度: 0.73, 位置: (1128, 1166)
2025-07-31 17:05:56 - PDFExtractor - INFO - 图片识别完成，共找到23个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page11_img1.png
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page12_img1.png (模式: 主)
2025-07-31 17:05:56 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page12_img1.png，尝试替代方法
2025-07-31 17:05:56 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:56 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:57 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:57 - PDFExtractor - DEBUG - 识别到文本: 站出, 置信度: 0.51, 位置: (272, 387)
2025-07-31 17:05:57 - PDFExtractor - DEBUG - 识别到文本: 膀, 置信度: 1.00, 位置: (733, 1206)
2025-07-31 17:05:57 - PDFExtractor - DEBUG - 识别到文本: °旱, 置信度: 0.83, 位置: (786, 1244)
2025-07-31 17:05:57 - PDFExtractor - INFO - 图片识别完成，共找到3个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page12_img1.png
2025-07-31 17:05:57 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page13_img1.png (模式: 主)
2025-07-31 17:05:57 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page13_img1.png，尝试替代方法
2025-07-31 17:05:57 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:57 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:05:59 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:05:59 - PDFExtractor - DEBUG - 识别到文本: 899409, 置信度: 0.71, 位置: (170, 283)
2025-07-31 17:05:59 - PDFExtractor - DEBUG - 识别到文本: ‘旭回, 置信度: 0.80, 位置: (757, 432)
2025-07-31 17:05:59 - PDFExtractor - DEBUG - 识别到文本: 旱回轰中吉, 置信度: 0.58, 位置: (758, 596)
2025-07-31 17:05:59 - PDFExtractor - INFO - 图片识别完成，共找到3个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page13_img1.png
2025-07-31 17:05:59 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page14_img1.png (模式: 主)
2025-07-31 17:05:59 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page14_img1.png，尝试替代方法
2025-07-31 17:05:59 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:05:59 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 识别到文本: ‘, 置信度: 0.91, 位置: (468, 642)
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 识别到文本: 电品班, 置信度: 0.59, 位置: (1616, 854)
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 识别到文本: 性, 置信度: 0.96, 位置: (252, 902)
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 识别到文本: “, 置信度: 0.60, 位置: (320, 1110)
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 识别到文本: 明, 置信度: 1.00, 位置: (470, 1220)
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 识别到文本: 2, 置信度: 1.00, 位置: (1144, 1226)
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 识别到文本: 3, 置信度: 1.00, 位置: (1169, 1227)
2025-07-31 17:06:01 - PDFExtractor - INFO - 图片识别完成，共找到7个文本块: ./temp_images\202404290137-袁圣丹-440924196908123182.pdf_page14_img1.png
2025-07-31 17:06:01 - PDFExtractor - INFO - 添加记录: 袁圣丹
2025-07-31 17:06:01 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 袁圣丹
2025-07-31 17:06:01 - PDFExtractor - INFO - 成功处理PDF: 202404290137-袁圣丹-440924196908123182.pdf
2025-07-31 17:06:01 - PDFExtractor - INFO - 开始处理PDF文件: 202405150543_陈娟_321081197012200022.pdf
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202405150543_陈娟_321081197012200022.pdf_page1_img1.png (模式: 主)
2025-07-31 17:06:01 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202405150543_陈娟_321081197012200022.pdf_page1_img1.png，尝试替代方法
2025-07-31 17:06:01 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:06:01 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202405150543_陈娟_321081197012200022.pdf_page1_img1.png
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202405150543_陈娟_321081197012200022.pdf_page1_img2.png (模式: 主)
2025-07-31 17:06:01 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202405150543_陈娟_321081197012200022.pdf_page1_img2.png，尝试替代方法
2025-07-31 17:06:01 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 非心电图报告，保持纠正后的方向
2025-07-31 17:06:01 - PDFExtractor - INFO - 图片识别完成，共找到0个文本块: ./temp_images\202405150543_陈娟_321081197012200022.pdf_page1_img2.png
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 正在处理图片: ./temp_images\202405150543_陈娟_321081197012200022.pdf_page1_img3.png (模式: 主)
2025-07-31 17:06:01 - PDFExtractor - WARNING - 使用标准方法无法读取图片: ./temp_images\202405150543_陈娟_321081197012200022.pdf_page1_img3.png，尝试替代方法
2025-07-31 17:06:01 - PDFExtractor - INFO - 正在使用标准基础预处理流程...
2025-07-31 17:06:01 - PDFExtractor - DEBUG - 应用后置旋转策略，默认旋转270度来纠正图像方向
2025-07-31 17:16:07 - PDFExtractor - INFO - 日志系统初始化完成
2025-07-31 17:16:07 - PDFExtractor - INFO - ==================================================
2025-07-31 17:16:07 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-07-31 17:16:07 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 09:02:03 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 09:02:03 - PDFExtractor - INFO - ==================================================
2025-08-01 09:02:03 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 09:02:03 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 09:02:34 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 09:03:36 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 09:03:41 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 09:03:41 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-01 09:03:52 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-01 09:03:57 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-01 09:04:01 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-01 09:10:52 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 09:10:52 - PDFExtractor - INFO - ==================================================
2025-08-01 09:10:52 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 09:10:52 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 09:13:03 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 09:13:03 - PDFExtractor - INFO - ==================================================
2025-08-01 09:13:03 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 09:13:03 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 09:14:17 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 09:18:08 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 09:18:08 - PDFExtractor - INFO - ==================================================
2025-08-01 09:18:08 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 09:18:08 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 09:21:47 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 09:33:57 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 09:35:03 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 09:35:09 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-01 09:37:22 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-01 10:07:22 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 10:07:22 - PDFExtractor - INFO - ==================================================
2025-08-01 10:07:22 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 10:07:22 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 10:07:25 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 10:07:25 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 10:07:25 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 10:07:25 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-01 10:07:25 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-01 10:07:25 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-01 10:07:25 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-01 13:46:11 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 13:46:11 - PDFExtractor - INFO - ==================================================
2025-08-01 13:46:11 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 13:46:11 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 13:46:14 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 13:46:15 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 13:46:15 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 13:46:15 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-01 13:46:15 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-01 13:46:15 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-01 13:46:15 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-01 13:46:15 - PDFExtractor - ERROR - 程序执行出错: [Errno 13] Permission denied: './output/medical_data_mixed.xlsx'
Traceback (most recent call last):
  File "D:\wyh\Code\daily_debug\Medical_Helper\OCR-PDF混合内容抽取整理\main.py", line 134, in main
    excel_handler.save_output()
  File "D:\wyh\Code\daily_debug\Medical_Helper\OCR-PDF混合内容抽取整理\excel_handler.py", line 165, in save_output
    wb.save(self.output_file)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\openpyxl\writer\excel.py", line 302, in save_workbook
    archive = ZipFile(filename, 'w', ZIP_DEFLATED, allowZip64=True)
  File "C:\ProgramData\Anaconda3\lib\zipfile.py", line 1248, in __init__
    self.fp = io.open(file, filemode)
PermissionError: [Errno 13] Permission denied: './output/medical_data_mixed.xlsx'
2025-08-01 14:12:02 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 14:12:02 - PDFExtractor - INFO - ==================================================
2025-08-01 14:12:02 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 14:12:02 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 14:12:05 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 14:12:06 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 14:12:06 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 14:12:06 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-01 14:12:06 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-01 14:12:06 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-01 14:12:06 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-01 16:05:47 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 16:05:47 - PDFExtractor - INFO - ==================================================
2025-08-01 16:05:47 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 16:05:47 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 16:05:47 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-01 16:05:47 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-01 16:05:47 - PDFExtractor - INFO - 模块初始化完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-01 16:05:47 - PDFExtractor - INFO - 找到 45 个待处理PDF文件
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403140123_张东_44082219660422041X.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1480字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 张东
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 张东
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403140123_张东_44082219660422041X.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403140124_黄依依_440102195309300021.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 2083字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 黄依依
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 黄依依
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403140124_黄依依_440102195309300021.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403140125_罗端兰_440822196204150248.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1543字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 罗端兰
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 罗端兰
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403140125_罗端兰_440822196204150248.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403140126_郭雯锦_441423196509153349.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1714字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 郭雯锦
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 郭雯锦
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403140126_郭雯锦_441423196509153349.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403140127_刘伟贤_441423195809200018.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1527字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 刘伟贤
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 刘伟贤
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403140127_刘伟贤_441423195809200018.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403260124-唐棣邦-44060119641113183X.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1018字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 唐棣邦
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 唐棣邦
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403260124-唐棣邦-44060119641113183X.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403260125-黄剑青-440683197007228030.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1006字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 黄剑青
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 黄剑青
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403260125-黄剑青-440683197007228030.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403280156-袁圣丹-440924196908123182.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 970字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 袁圣丹
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 袁圣丹
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403280156-袁圣丹-440924196908123182.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403280156_袁圣丹_440924196908123182.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 970字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 袁圣丹
2025-08-01 16:05:47 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 袁圣丹
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403280156_袁圣丹_440924196908123182.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403280157_袁生_440602196710040010.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1078字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 袁生
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 袁生
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403280157_袁生_440602196710040010.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202403281988-黄星-440902197007051616.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 287字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 黄星
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 黄星
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202403281988-黄星-440902197007051616.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404080339_劳承周_440924196207162998.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 2116字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 劳承周
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 劳承周
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404080339_劳承周_440924196207162998.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404080340_劳承文_440924196401172970.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1968字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 劳承文
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 劳承文
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404080340_劳承文_440924196401172970.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404080341_袁华英_440924196210273189.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 2650字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 袁华英
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 袁华英
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404080341_袁华英_440924196210273189.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404190057_罗庆欢_441423196005222718.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1810字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 罗庆欢
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 罗庆欢
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404190057_罗庆欢_441423196005222718.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404190061_钟新梅_441425196808253908.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1936字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 钟新梅
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 钟新梅
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404190061_钟新梅_441425196808253908.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404190067_袁国泉_H60790359.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1442字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 袁国泉
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 袁国泉
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404190067_袁国泉_H60790359.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404190091-黄星-440902197007051616.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 278字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 黄星
2025-08-01 16:05:47 - PDFExtractor - INFO - 发现相同患者记录，合并信息: 黄星
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404190091-黄星-440902197007051616.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404221692_袁圣浩_440924196807213170.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 2172字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 袁圣浩
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 袁圣浩
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404221692_袁圣浩_440924196807213170.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404221693_张燕平_440924197102273661.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 2086字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 张燕平
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 张燕平
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404221693_张燕平_440924197102273661.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404221695_袁圣泽_440924197404273173.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 2121字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 袁圣泽
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 袁圣泽
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404221695_袁圣泽_440924197404273173.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404221696_叶海燕_440924197402183422.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 2036字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:47 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:47 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:47 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:47 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加记录: 叶海燕
2025-08-01 16:05:47 - PDFExtractor - INFO - 添加新记录: 叶海燕
2025-08-01 16:05:47 - PDFExtractor - INFO - 成功处理PDF: 202404221696_叶海燕_440924197402183422.pdf
2025-08-01 16:05:47 - PDFExtractor - INFO - 开始处理PDF文件: 202404290137-袁圣丹-440924196908123182.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 9444字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 袁圣丹
2025-08-01 16:05:48 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202404290137-袁圣丹-440924196908123182.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405150543_陈娟_321081197012200022.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 13281字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 陈娟
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 陈娟
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405150543_陈娟_321081197012200022.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405160114_徐海宁_440622196604195428.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 15424字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 徐海宁
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 徐海宁
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405160114_徐海宁_440622196604195428.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405160123_吴长根_321081197002140310.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 14379字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 吴长根
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 吴长根
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405160123_吴长根_321081197002140310.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405240209_何六女_440622195810051324.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1915字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 何六女
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 何六女
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405240209_何六女_440622195810051324.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280142_梁全财_440601196507101512.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 10092字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 梁全财
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 梁全财
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280142_梁全财_440601196507101512.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280143_黄锦颖_440682198509191723.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 8875字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 黄锦颖
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 黄锦颖
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280143_黄锦颖_440682198509191723.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280145-叶宁-440602197305271549.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 9242字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 叶宁
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 叶宁
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280145-叶宁-440602197305271549.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280146_李仁声_450302196511280551.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 9804字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 李仁声
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 李仁声
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280146_李仁声_450302196511280551.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280147_甘树彬_440622197010241719.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 8694字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 甘树彬
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 甘树彬
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280147_甘树彬_440622197010241719.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280148_张江_44060219640727181X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 10099字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 张江
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 张江
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280148_张江_44060219640727181X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280150_陈长东_440121197212030094.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 11273字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 陈长东
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 陈长东
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280150_陈长东_440121197212030094.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280151_李佳华_422202197001287070.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 10238字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 李佳华
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 李佳华
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280151_李佳华_422202197001287070.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280152_周剑雄_440602196611221510.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 9917字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 周剑雄
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 周剑雄
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280152_周剑雄_440602196611221510.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202405280155_范耀洪_440622196408242899.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 10587字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 范耀洪
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 范耀洪
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202405280155_范耀洪_440622196408242899.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202406070081_杨荣洪_440682196709226012.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 5421字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 杨荣洪
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 杨荣洪
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202406070081_杨荣洪_440682196709226012.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202406170199-唐棣邦-44060119641113183X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 13159字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 唐棣邦
2025-08-01 16:05:48 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202406170199-唐棣邦-44060119641113183X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202406170203-黄剑青-440683197007228030.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 11315字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 黄剑青
2025-08-01 16:05:48 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202406170203-黄剑青-440683197007228030.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202407240048-唐棣邦-44060119641113183X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 811字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 唐棣邦
2025-08-01 16:05:48 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202407240048-唐棣邦-44060119641113183X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202412130173_弄庆新_45250119800812256X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 11211字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 弄庆新
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加新记录: 弄庆新
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202412130173_弄庆新_45250119800812256X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202505260063-唐棣邦-44060119641113183X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 11470字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 唐棣邦
2025-08-01 16:05:48 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202505260063-唐棣邦-44060119641113183X.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202506100328-黄剑青-440683197007228030.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 9727字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 黄剑青
2025-08-01 16:05:48 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202506100328-黄剑青-440683197007228030.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始处理PDF文件: 202507110198-叶宁-440602197305271549.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 10190字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:05:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-01 16:05:48 - PDFExtractor - INFO -   原始文本长度: 0字符
2025-08-01 16:05:48 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-01 16:05:48 - PDFExtractor - INFO - 图片字段提取完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 添加记录: 叶宁
2025-08-01 16:05:48 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-01 16:05:48 - PDFExtractor - INFO - 成功处理PDF: 202507110198-叶宁-440602197305271549.pdf
2025-08-01 16:05:48 - PDFExtractor - INFO - PDF文件处理完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始保存Excel文件...
2025-08-01 16:05:48 - PDFExtractor - INFO - 开始保存Excel文件: ./output/medical_data_mixed.xlsx
2025-08-01 16:05:48 - PDFExtractor - INFO - Excel文件保存完成: ./output/medical_data_mixed.xlsx
2025-08-01 16:05:48 - PDFExtractor - INFO - 共保存 43 条记录
2025-08-01 16:05:48 - PDFExtractor - INFO - Excel文件保存完成
2025-08-01 16:05:48 - PDFExtractor - INFO - 程序执行完成
2025-08-01 16:12:48 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 16:12:48 - PDFExtractor - INFO - ==================================================
2025-08-01 16:12:48 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 16:12:48 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 16:12:48 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-01 16:12:48 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-01 16:12:48 - PDFExtractor - INFO - 模块初始化完成
2025-08-01 16:12:48 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-01 16:12:48 - PDFExtractor - INFO - 找到 45 个待处理PDF文件
2025-08-01 16:12:48 - PDFExtractor - INFO - 开始处理PDF文件: 202403140123_张东_44082219660422041X.pdf
2025-08-01 16:12:48 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1480字符
2025-08-01 16:12:48 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 16:12:48 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 16:12:48 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 16:12:48 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-01 16:12:48 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-01 16:12:48 - PDFExtractor - INFO -   原始文本长度: 1480字符
2025-08-01 16:12:48 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-01 16:12:48 - PDFExtractor - DEBUG - 第6页包含1张图片
2025-08-01 16:12:49 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-01 16:15:15 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 16:21:59 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 16:22:32 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 16:22:32 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-01 16:24:08 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-01 16:24:54 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-01 16:25:25 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-01 17:16:52 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 17:16:52 - PDFExtractor - INFO - ==================================================
2025-08-01 17:16:52 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 17:16:52 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 17:16:52 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-01 17:16:52 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-01 17:16:52 - PDFExtractor - INFO - 模块初始化完成
2025-08-01 17:16:52 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-01 17:16:52 - PDFExtractor - INFO - 找到 45 个待处理PDF文件
2025-08-01 17:16:52 - PDFExtractor - INFO - 开始处理PDF文件: 202403140123_张东_44082219660422041X.pdf
2025-08-01 17:16:52 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1480字符
2025-08-01 17:16:52 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 17:16:52 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 17:16:52 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 17:16:52 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-01 17:16:52 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-01 17:16:52 - PDFExtractor - INFO -   原始文本长度: 1480字符
2025-08-01 17:16:52 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-01 17:16:52 - PDFExtractor - DEBUG - 第6页包含1张图片
2025-08-01 17:16:53 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-01 17:19:14 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 17:27:09 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 17:27:48 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 17:27:48 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-01 17:29:18 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-01 17:30:08 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-01 17:30:58 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-01 17:31:03 - PDFExtractor - ERROR - 程序执行出错: [Errno 13] Permission denied: './output/medical_data_mixed.xlsx'
Traceback (most recent call last):
  File "D:\wyh\Code\daily_debug\Medical_Helper\OCR-PDF混合内容抽取整理\main.py", line 134, in main
    excel_handler.save_output()
  File "D:\wyh\Code\daily_debug\Medical_Helper\OCR-PDF混合内容抽取整理\excel_handler.py", line 165, in save_output
    wb.save(self.output_file)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\openpyxl\writer\excel.py", line 302, in save_workbook
    archive = ZipFile(filename, 'w', ZIP_DEFLATED, allowZip64=True)
  File "C:\ProgramData\Anaconda3\lib\zipfile.py", line 1248, in __init__
    self.fp = io.open(file, filemode)
PermissionError: [Errno 13] Permission denied: './output/medical_data_mixed.xlsx'
2025-08-01 17:32:28 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-01 17:32:28 - PDFExtractor - INFO - ==================================================
2025-08-01 17:32:28 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-01 17:32:28 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-01 17:32:28 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-01 17:32:28 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-01 17:32:28 - PDFExtractor - INFO - 模块初始化完成
2025-08-01 17:32:28 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-01 17:32:28 - PDFExtractor - INFO - 找到 45 个待处理PDF文件
2025-08-01 17:32:28 - PDFExtractor - INFO - 开始处理PDF文件: 202403140123_张东_44082219660422041X.pdf
2025-08-01 17:32:28 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1480字符
2025-08-01 17:32:28 - PDFExtractor - INFO - 文字字段提取完成
2025-08-01 17:32:28 - PDFExtractor - INFO - 开始提取图片字段
2025-08-01 17:32:28 - PDFExtractor - INFO - 关键词检查结果:
2025-08-01 17:32:28 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-01 17:32:28 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-01 17:32:28 - PDFExtractor - INFO -   原始文本长度: 1480字符
2025-08-01 17:32:28 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-01 17:32:28 - PDFExtractor - DEBUG - 第6页包含1张图片
2025-08-01 17:32:29 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-01 17:34:51 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-01 17:42:58 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 17:43:36 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-01 17:43:36 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-01 17:45:10 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-01 17:46:03 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-01 17:46:54 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-04 16:41:57 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-04 16:41:57 - PDFExtractor - INFO - ==================================================
2025-08-04 16:41:57 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-04 16:41:57 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-04 16:41:57 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-04 16:41:57 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-04 16:41:57 - PDFExtractor - INFO - 模块初始化完成
2025-08-04 16:41:57 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-04 16:41:57 - PDFExtractor - INFO - 找到 211 个待处理PDF文件
2025-08-04 16:41:57 - PDFExtractor - INFO - 开始处理PDF文件: 202403140123_张东_44082219660422041X.pdf
2025-08-04 16:41:57 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1480字符
2025-08-04 16:41:57 - PDFExtractor - INFO - 文字字段提取完成
2025-08-04 16:41:57 - PDFExtractor - INFO - 开始提取图片字段
2025-08-04 16:41:57 - PDFExtractor - INFO - 关键词检查结果:
2025-08-04 16:41:57 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-04 16:41:57 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-04 16:41:57 - PDFExtractor - INFO -   原始文本长度: 1480字符
2025-08-04 16:41:57 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-04 16:41:57 - PDFExtractor - DEBUG - 第6页包含1张图片
2025-08-04 16:41:58 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-04 16:44:31 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-04 16:54:02 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-04 16:54:49 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-04 16:57:33 - PDFExtractor - WARNING - 发现数据冲突，为冯政祥创建单独记录: ['总检日期: 2024-06-27 vs 2024-07-25']
2025-08-04 17:07:19 - PDFExtractor - WARNING - 发现数据冲突，为李勇创建单独记录: ['总检日期: 2024-08-20 vs 2024-08-07']
2025-08-04 17:10:55 - PDFExtractor - WARNING - 发现数据冲突，为李捷浩创建单独记录: ['总检日期: 2024-07-31 vs 2024-08-30']
2025-08-04 17:10:55 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-04 17:16:41 - PDFExtractor - WARNING - 发现数据冲突，为陈碧文创建单独记录: ['总检日期: 2024-08-20 vs 2024-08-16']
2025-08-04 17:16:41 - PDFExtractor - WARNING - 发现数据冲突，为黄建军创建单独记录: ['总检日期: 2024-08-23 vs 2024-08-19']
2025-08-04 17:16:41 - PDFExtractor - WARNING - 发现数据冲突，为唐婉堃创建单独记录: ['总检日期: 2024-08-28 vs 2024-08-22']
2025-08-04 17:17:18 - PDFExtractor - WARNING - 发现数据冲突，为黎蝶梅创建单独记录: ['总检日期: 2024-08-30 vs 2024-08-28']
2025-08-04 17:17:18 - PDFExtractor - WARNING - 发现数据冲突，为龚肖倩创建单独记录: ['总检日期: 2024-08-30 vs 2024-08-28']
2025-08-04 17:17:21 - PDFExtractor - WARNING - 发现数据冲突，为黄惠仪创建单独记录: ['总检日期: 2024-09-02 vs 2024-08-30']
2025-08-04 17:17:21 - PDFExtractor - WARNING - 发现数据冲突，为唐婉堃创建单独记录: ['总检日期: 2024-08-28 vs 2024-09-02']
2025-08-04 17:38:59 - PDFExtractor - WARNING - 发现数据冲突，为李宏芬创建单独记录: ['总检日期: 2024-08-16 vs 2024-11-07']
2025-08-04 17:40:45 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-04 17:41:05 - PDFExtractor - WARNING - 发现数据冲突，为彭学军创建单独记录: ['总检日期: 2024-07-15 vs 2025-06-19']
2025-08-04 17:42:01 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-04 17:42:57 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-04 17:43:21 - PDFExtractor - WARNING - 发现数据冲突，为张宗伟创建单独记录: ['总检日期: 2024-07-25 vs 2025-07-24', '身高: 178.5 vs 177.5', '体重: 79 vs 73.4']
2025-08-05 08:28:02 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-05 08:28:02 - PDFExtractor - INFO - ==================================================
2025-08-05 08:28:02 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-05 08:28:02 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-05 08:28:02 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-05 08:28:02 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-05 08:28:02 - PDFExtractor - INFO - 模块初始化完成
2025-08-05 08:28:02 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-05 08:28:02 - PDFExtractor - INFO - 找到 211 个待处理PDF文件
2025-08-05 08:28:02 - PDFExtractor - INFO - 开始处理PDF文件: 202403140123_张东_44082219660422041X.pdf
2025-08-05 08:28:02 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1480字符
2025-08-05 08:28:02 - PDFExtractor - INFO - 文字字段提取完成
2025-08-05 08:28:02 - PDFExtractor - INFO - 开始提取图片字段
2025-08-05 08:28:02 - PDFExtractor - INFO - 关键词检查结果:
2025-08-05 08:28:02 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-05 08:28:02 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-05 08:28:02 - PDFExtractor - INFO -   原始文本长度: 1480字符
2025-08-05 08:28:02 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-05 08:28:02 - PDFExtractor - DEBUG - 第6页包含1张图片
2025-08-05 08:28:03 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-05 08:30:17 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-05 08:39:54 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-05 08:40:34 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-05 08:43:42 - PDFExtractor - WARNING - 发现数据冲突，为冯政祥创建单独记录: ['总检日期: 2024-06-27 vs 2024-07-25']
2025-08-05 08:52:58 - PDFExtractor - WARNING - 发现数据冲突，为李勇创建单独记录: ['总检日期: 2024-08-20 vs 2024-08-07']
2025-08-05 08:55:56 - PDFExtractor - WARNING - 发现数据冲突，为李捷浩创建单独记录: ['总检日期: 2024-07-31 vs 2024-08-30']
2025-08-05 08:55:56 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-05 09:00:58 - PDFExtractor - WARNING - 发现数据冲突，为陈碧文创建单独记录: ['总检日期: 2024-08-20 vs 2024-08-16']
2025-08-05 09:00:58 - PDFExtractor - WARNING - 发现数据冲突，为黄建军创建单独记录: ['总检日期: 2024-08-23 vs 2024-08-19']
2025-08-05 09:00:58 - PDFExtractor - WARNING - 发现数据冲突，为唐婉堃创建单独记录: ['总检日期: 2024-08-28 vs 2024-08-22']
2025-08-05 09:01:34 - PDFExtractor - WARNING - 发现数据冲突，为黎蝶梅创建单独记录: ['总检日期: 2024-08-30 vs 2024-08-28']
2025-08-05 09:01:34 - PDFExtractor - WARNING - 发现数据冲突，为龚肖倩创建单独记录: ['总检日期: 2024-08-30 vs 2024-08-28']
2025-08-05 09:01:37 - PDFExtractor - WARNING - 发现数据冲突，为黄惠仪创建单独记录: ['总检日期: 2024-09-02 vs 2024-08-30']
2025-08-05 09:01:37 - PDFExtractor - WARNING - 发现数据冲突，为唐婉堃创建单独记录: ['总检日期: 2024-08-28 vs 2024-09-02']
2025-08-05 09:23:12 - PDFExtractor - WARNING - 发现数据冲突，为李宏芬创建单独记录: ['总检日期: 2024-08-16 vs 2024-11-07']
2025-08-05 09:24:56 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-04']
2025-08-05 09:25:14 - PDFExtractor - WARNING - 发现数据冲突，为彭学军创建单独记录: ['总检日期: 2024-07-15 vs 2025-06-19']
2025-08-05 09:26:06 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2025-06-13']
2025-08-05 09:26:56 - PDFExtractor - WARNING - 发现数据冲突，为叶宁创建单独记录: ['总检日期: 2024-06-21 vs 2025-07-28', '身高: 156.5 vs 158', '体重: 52.3 vs 53.3']
2025-08-05 09:27:16 - PDFExtractor - WARNING - 发现数据冲突，为张宗伟创建单独记录: ['总检日期: 2024-07-25 vs 2025-07-24', '身高: 178.5 vs 177.5', '体重: 79 vs 73.4']
2025-08-06 09:06:20 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-06 09:06:20 - PDFExtractor - INFO - ==================================================
2025-08-06 09:06:20 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-06 09:06:20 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-06 09:06:20 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-06 09:06:20 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-06 09:06:20 - PDFExtractor - INFO - 模块初始化完成
2025-08-06 09:06:20 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-06 09:06:20 - PDFExtractor - INFO - 找到 211 个待处理PDF文件
2025-08-06 09:06:20 - PDFExtractor - INFO - 开始处理PDF文件: 202403140123_张东_44082219660422041X.pdf
2025-08-06 09:06:20 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1480字符
2025-08-06 09:06:20 - PDFExtractor - INFO - 文字字段提取完成
2025-08-06 09:06:20 - PDFExtractor - INFO - 开始提取图片字段
2025-08-06 09:06:20 - PDFExtractor - INFO - 关键词检查结果:
2025-08-06 09:06:20 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-06 09:06:20 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-06 09:06:20 - PDFExtractor - INFO -   原始文本长度: 1480字符
2025-08-06 09:06:20 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-06 09:06:20 - PDFExtractor - DEBUG - 第6页包含1张图片
2025-08-06 09:06:20 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-06 09:09:01 - PDFExtractor - WARNING - 发现数据冲突，为袁圣丹创建单独记录: ['总检日期: 2024-04-02 vs 2024-05-07']
2025-08-06 09:19:22 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-06 09:20:05 - PDFExtractor - WARNING - 发现数据冲突，为黄剑青创建单独记录: ['总检日期: 2024-03-26 vs 2024-06-25']
2025-08-06 09:23:05 - PDFExtractor - WARNING - 发现数据冲突，为冯政祥创建单独记录: ['总检日期: 2024-06-27 vs 2024-07-25']
2025-08-06 09:34:32 - PDFExtractor - WARNING - 发现数据冲突，为李勇创建单独记录: ['总检日期: 2024-08-20 vs 2024-08-07']
2025-08-06 09:38:32 - PDFExtractor - WARNING - 发现数据冲突，为李捷浩创建单独记录: ['总检日期: 2024-07-31 vs 2024-08-30']
2025-08-06 09:38:32 - PDFExtractor - WARNING - 发现数据冲突，为唐棣邦创建单独记录: ['总检日期: 2024-03-26 vs 2024-07-30']
2025-08-06 09:44:32 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-06 09:44:32 - PDFExtractor - INFO - ==================================================
2025-08-06 09:44:32 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-06 09:44:32 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-06 09:44:32 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-06 09:44:32 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-06 09:44:32 - PDFExtractor - INFO - 模块初始化完成
2025-08-06 09:44:32 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-06 09:44:32 - PDFExtractor - INFO - 找到 136 个待处理PDF文件
2025-08-06 09:44:32 - PDFExtractor - INFO - 开始处理PDF文件: 202409130139_黎德新_440622197310262810.pdf
2025-08-06 09:44:32 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 12343字符
2025-08-06 09:44:32 - PDFExtractor - INFO - 文字字段提取完成
2025-08-06 09:44:32 - PDFExtractor - INFO - 开始提取图片字段
2025-08-06 09:44:32 - PDFExtractor - INFO - 关键词检查结果:
2025-08-06 09:44:32 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-06 09:44:32 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-06 09:44:32 - PDFExtractor - INFO -   原始文本长度: 12343字符
2025-08-06 09:44:32 - PDFExtractor - INFO - 文本中发现关键词 - 心超: False, 心电图: True，开始图片处理
2025-08-06 09:44:32 - PDFExtractor - DEBUG - 第22页包含3张图片
2025-08-06 09:44:33 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page22_img1.png特征: 尺寸2722x3818, 宽高比0.71, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:33 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:33 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page22_img2.png特征: 尺寸227x76, 宽高比2.99, 横向:True, 尺寸合理:False, 非正方形:True
2025-08-06 09:44:33 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:33 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page22_img3.png特征: 尺寸227x76, 宽高比2.99, 横向:True, 尺寸合理:False, 非正方形:True
2025-08-06 09:44:33 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:33 - PDFExtractor - DEBUG - 第21页包含3张图片
2025-08-06 09:44:33 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page21_img1.png特征: 尺寸2722x3818, 宽高比0.71, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:33 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:33 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page21_img2.png特征: 尺寸265x76, 宽高比3.49, 横向:True, 尺寸合理:False, 非正方形:True
2025-08-06 09:44:33 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:33 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page21_img3.png特征: 尺寸265x76, 宽高比3.49, 横向:True, 尺寸合理:False, 非正方形:True
2025-08-06 09:44:33 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:33 - PDFExtractor - DEBUG - 第20页包含1张图片
2025-08-06 09:44:34 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page20_img1.png特征: 尺寸2722x3856, 宽高比0.71, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:34 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:34 - PDFExtractor - DEBUG - 第19页包含1张图片
2025-08-06 09:44:34 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page19_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:34 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:34 - PDFExtractor - DEBUG - 第18页包含1张图片
2025-08-06 09:44:35 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page18_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:35 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:35 - PDFExtractor - DEBUG - 第17页包含1张图片
2025-08-06 09:44:35 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page17_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:35 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:35 - PDFExtractor - DEBUG - 第16页包含1张图片
2025-08-06 09:44:36 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page16_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:36 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:36 - PDFExtractor - DEBUG - 第15页包含1张图片
2025-08-06 09:44:36 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page15_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:36 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:36 - PDFExtractor - DEBUG - 第14页包含1张图片
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page14_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:37 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第13页包含1张图片
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page13_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:44:37 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第12页包含0张图片
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第11页包含0张图片
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第10页包含0张图片
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第9页包含0张图片
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第8页包含0张图片
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第7页包含0张图片
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第6页包含2张图片
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img1.png特征: 尺寸416x189, 宽高比2.20, 横向:True, 尺寸合理:True, 非正方形:True
2025-08-06 09:44:37 - PDFExtractor - INFO - 心电图图片视觉特征检测通过: ./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img1.png
2025-08-06 09:44:37 - PDFExtractor - INFO - 发现心电图图片: ./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img1.png
2025-08-06 09:44:37 - PDFExtractor - INFO - 保存心电图图片到Excel: ./temp_images\ecg_images\黎德新_ecg_1754444677.png
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img2.png特征: 尺寸416x189, 宽高比2.20, 横向:True, 尺寸合理:True, 非正方形:True
2025-08-06 09:44:37 - PDFExtractor - INFO - 心电图图片视觉特征检测通过: ./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img2.png
2025-08-06 09:44:37 - PDFExtractor - INFO - 发现心电图图片: ./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img2.png
2025-08-06 09:44:37 - PDFExtractor - INFO - 跳过额外的心电图图片（已有1张）
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第5页包含0张图片
2025-08-06 09:44:37 - PDFExtractor - INFO - 已找到所需图片，跳过剩余页面
2025-08-06 09:44:37 - PDFExtractor - INFO - 图片扫描完成: 总计16张图片，发现0张心超图片，2张心电图图片
2025-08-06 09:44:37 - PDFExtractor - INFO - 图片字段提取完成
2025-08-06 09:44:37 - PDFExtractor - INFO - 添加记录: 黎德新
2025-08-06 09:44:37 - PDFExtractor - INFO - 添加新记录: 黎德新
2025-08-06 09:44:37 - PDFExtractor - INFO - 成功处理PDF: 202409130139_黎德新_440622197310262810.pdf
2025-08-06 09:44:37 - PDFExtractor - INFO - 开始处理PDF文件: 202409130159_林静_440602199008120321.pdf
2025-08-06 09:44:37 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 10113字符
2025-08-06 09:44:37 - PDFExtractor - INFO - 文字字段提取完成
2025-08-06 09:44:37 - PDFExtractor - INFO - 开始提取图片字段
2025-08-06 09:44:37 - PDFExtractor - INFO - 关键词检查结果:
2025-08-06 09:44:37 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-06 09:44:37 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-06 09:44:37 - PDFExtractor - INFO -   原始文本长度: 10113字符
2025-08-06 09:44:37 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-06 09:44:37 - PDFExtractor - DEBUG - 第23页包含3张图片
2025-08-06 09:44:37 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-06 09:45:15 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-06 09:45:15 - PDFExtractor - INFO - ==================================================
2025-08-06 09:45:15 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-06 09:45:15 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-06 09:45:15 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-06 09:45:15 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-06 09:45:15 - PDFExtractor - INFO - 模块初始化完成
2025-08-06 09:45:15 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-06 09:45:15 - PDFExtractor - INFO - 找到 136 个待处理PDF文件
2025-08-06 09:45:15 - PDFExtractor - INFO - 开始处理PDF文件: 202409130139_黎德新_440622197310262810.pdf
2025-08-06 09:45:15 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 12343字符
2025-08-06 09:45:15 - PDFExtractor - INFO - 文字字段提取完成
2025-08-06 09:45:15 - PDFExtractor - INFO - 开始提取图片字段
2025-08-06 09:45:15 - PDFExtractor - INFO - 关键词检查结果:
2025-08-06 09:45:15 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-06 09:45:15 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-06 09:45:15 - PDFExtractor - INFO -   原始文本长度: 12343字符
2025-08-06 09:45:15 - PDFExtractor - INFO - 文本中发现关键词 - 心超: False, 心电图: True，开始图片处理
2025-08-06 09:45:15 - PDFExtractor - DEBUG - 第22页包含3张图片
2025-08-06 09:45:16 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page22_img1.png特征: 尺寸2722x3818, 宽高比0.71, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:16 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:16 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page22_img2.png特征: 尺寸227x76, 宽高比2.99, 横向:True, 尺寸合理:False, 非正方形:True
2025-08-06 09:45:16 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:16 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page22_img3.png特征: 尺寸227x76, 宽高比2.99, 横向:True, 尺寸合理:False, 非正方形:True
2025-08-06 09:45:16 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:16 - PDFExtractor - DEBUG - 第21页包含3张图片
2025-08-06 09:45:16 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page21_img1.png特征: 尺寸2722x3818, 宽高比0.71, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:16 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:16 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page21_img2.png特征: 尺寸265x76, 宽高比3.49, 横向:True, 尺寸合理:False, 非正方形:True
2025-08-06 09:45:16 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:16 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page21_img3.png特征: 尺寸265x76, 宽高比3.49, 横向:True, 尺寸合理:False, 非正方形:True
2025-08-06 09:45:16 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:16 - PDFExtractor - DEBUG - 第20页包含1张图片
2025-08-06 09:45:17 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page20_img1.png特征: 尺寸2722x3856, 宽高比0.71, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:17 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:17 - PDFExtractor - DEBUG - 第19页包含1张图片
2025-08-06 09:45:17 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page19_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:17 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:17 - PDFExtractor - DEBUG - 第18页包含1张图片
2025-08-06 09:45:18 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page18_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:18 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:18 - PDFExtractor - DEBUG - 第17页包含1张图片
2025-08-06 09:45:18 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page17_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:18 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:18 - PDFExtractor - DEBUG - 第16页包含1张图片
2025-08-06 09:45:18 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page16_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:18 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:18 - PDFExtractor - DEBUG - 第15页包含1张图片
2025-08-06 09:45:19 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page15_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:19 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:19 - PDFExtractor - DEBUG - 第14页包含1张图片
2025-08-06 09:45:19 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page14_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:19 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:19 - PDFExtractor - DEBUG - 第13页包含1张图片
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page13_img1.png特征: 尺寸2722x3704, 宽高比0.73, 横向:False, 尺寸合理:True, 非正方形:False
2025-08-06 09:45:20 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 第12页包含0张图片
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 第11页包含0张图片
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 第10页包含0张图片
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 第9页包含0张图片
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 第8页包含0张图片
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 第7页包含0张图片
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 第6页包含2张图片
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img1.png特征: 尺寸416x189, 宽高比2.20, 横向:True, 尺寸合理:True, 非正方形:True
2025-08-06 09:45:20 - PDFExtractor - INFO - 心电图图片视觉特征检测通过: ./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img1.png
2025-08-06 09:45:20 - PDFExtractor - INFO - 发现心电图图片: ./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img1.png
2025-08-06 09:45:20 - PDFExtractor - INFO - 保存心电图图片到Excel: ./temp_images\ecg_images\黎德新_ecg_1754444720.png
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 图片./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img2.png特征: 尺寸416x189, 宽高比2.20, 横向:True, 尺寸合理:True, 非正方形:True
2025-08-06 09:45:20 - PDFExtractor - INFO - 心电图图片视觉特征检测通过: ./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img2.png
2025-08-06 09:45:20 - PDFExtractor - INFO - 发现心电图图片: ./temp_images\202409130139_黎德新_440622197310262810.pdf_page6_img2.png
2025-08-06 09:45:20 - PDFExtractor - INFO - 跳过额外的心电图图片（已有1张）
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 第5页包含0张图片
2025-08-06 09:45:20 - PDFExtractor - INFO - 已找到所需图片，跳过剩余页面
2025-08-06 09:45:20 - PDFExtractor - INFO - 图片扫描完成: 总计16张图片，发现0张心超图片，2张心电图图片
2025-08-06 09:45:20 - PDFExtractor - INFO - 图片字段提取完成
2025-08-06 09:45:20 - PDFExtractor - INFO - 添加记录: 黎德新
2025-08-06 09:45:20 - PDFExtractor - INFO - 添加新记录: 黎德新
2025-08-06 09:45:20 - PDFExtractor - INFO - 成功处理PDF: 202409130139_黎德新_440622197310262810.pdf
2025-08-06 09:45:20 - PDFExtractor - INFO - 开始处理PDF文件: 202409130159_林静_440602199008120321.pdf
2025-08-06 09:45:20 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 10113字符
2025-08-06 09:45:20 - PDFExtractor - INFO - 文字字段提取完成
2025-08-06 09:45:20 - PDFExtractor - INFO - 开始提取图片字段
2025-08-06 09:45:20 - PDFExtractor - INFO - 关键词检查结果:
2025-08-06 09:45:20 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-06 09:45:20 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-06 09:45:20 - PDFExtractor - INFO -   原始文本长度: 10113字符
2025-08-06 09:45:20 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-06 09:45:20 - PDFExtractor - DEBUG - 第23页包含3张图片
2025-08-06 09:45:20 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-06 10:46:30 - PDFExtractor - WARNING - 发现数据冲突，为黄爱清创建单独记录: ['总检日期: 2024-12-26 vs 2024-12-27']
2025-08-06 10:47:58 - PDFExtractor - WARNING - 发现数据冲突，为黄爱清创建单独记录: ['总检日期: 2024-12-26 vs 2025-01-06']
2025-08-07 13:40:45 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-07 13:40:45 - PDFExtractor - INFO - ==================================================
2025-08-07 13:40:45 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-07 13:40:45 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-07 13:40:45 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-07 13:40:45 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-07 13:40:45 - PDFExtractor - INFO - 模块初始化完成
2025-08-07 13:40:45 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-07 13:40:45 - PDFExtractor - INFO - 找到 221 个待处理PDF文件
2025-08-07 13:40:45 - PDFExtractor - INFO - 开始处理PDF文件: 202411080177_梁佩仪_440682198912122162.pdf
2025-08-07 13:40:45 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1887字符
2025-08-07 13:40:45 - PDFExtractor - INFO - 文字字段提取完成
2025-08-07 13:40:45 - PDFExtractor - INFO - 开始提取图片字段
2025-08-07 13:40:45 - PDFExtractor - INFO - 关键词检查结果:
2025-08-07 13:40:45 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-07 13:40:45 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-07 13:40:45 - PDFExtractor - INFO -   原始文本长度: 1887字符
2025-08-07 13:40:45 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-07 13:40:45 - PDFExtractor - INFO - 图片字段提取完成
2025-08-07 13:40:45 - PDFExtractor - INFO - 添加记录: 梁佩仪
2025-08-07 13:40:45 - PDFExtractor - INFO - 添加新记录: 梁佩仪
2025-08-07 13:40:45 - PDFExtractor - INFO - 成功处理PDF: 202411080177_梁佩仪_440682198912122162.pdf
2025-08-07 13:40:45 - PDFExtractor - INFO - 开始处理PDF文件: 202412130028_董博远_460003197605312610.pdf
2025-08-07 13:40:45 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 8638字符
2025-08-07 13:40:45 - PDFExtractor - INFO - 文字字段提取完成
2025-08-07 13:40:45 - PDFExtractor - INFO - 开始提取图片字段
2025-08-07 13:40:45 - PDFExtractor - INFO - 关键词检查结果:
2025-08-07 13:40:45 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-07 13:40:45 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-07 13:40:45 - PDFExtractor - INFO -   原始文本长度: 8638字符
2025-08-07 13:40:45 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-07 13:40:45 - PDFExtractor - DEBUG - 第18页包含3张图片
2025-08-07 13:40:45 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-07 13:40:49 - PDFExtractor - ERROR - PaddleOCR延迟初始化失败: No module named 'shapely'
2025-08-07 13:40:49 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:49 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:49 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:49 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:49 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:49 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:50 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:50 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:50 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:50 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:50 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:50 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:51 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:51 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:51 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:51 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:51 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:51 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:52 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:52 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:53 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:53 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:54 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:54 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:54 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:55 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:55 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:55 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:55 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:55 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:56 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:56 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:56 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:56 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:56 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:56 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:57 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:57 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:57 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:57 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:58 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:58 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:58 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:59 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:59 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:59 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:59 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:59 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:59 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:59 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:59 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:59 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:59 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:59 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:40:59 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:59 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:59 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:40:59 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:00 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:01 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:01 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:01 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:02 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:02 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:03 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:03 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:04 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:05 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:06 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:06 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:06 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:06 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:06 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:07 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:07 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:07 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:07 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:08 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:08 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:09 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:09 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:09 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:09 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:09 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:10 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:10 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:10 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:10 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:11 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:11 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:11 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:11 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:11 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:11 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:11 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:11 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:11 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:11 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:11 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:11 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:11 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:41:11 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:12 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:12 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:41:12 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:00 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-07 13:43:00 - PDFExtractor - INFO - ==================================================
2025-08-07 13:43:00 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-07 13:43:00 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-07 13:43:00 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-07 13:43:00 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-07 13:43:00 - PDFExtractor - INFO - 模块初始化完成
2025-08-07 13:43:00 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-07 13:43:00 - PDFExtractor - INFO - 找到 221 个待处理PDF文件
2025-08-07 13:43:00 - PDFExtractor - INFO - 开始处理PDF文件: 202411080177_梁佩仪_440682198912122162.pdf
2025-08-07 13:43:00 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1887字符
2025-08-07 13:43:00 - PDFExtractor - INFO - 文字字段提取完成
2025-08-07 13:43:00 - PDFExtractor - INFO - 开始提取图片字段
2025-08-07 13:43:00 - PDFExtractor - INFO - 关键词检查结果:
2025-08-07 13:43:00 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-07 13:43:00 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-07 13:43:00 - PDFExtractor - INFO -   原始文本长度: 1887字符
2025-08-07 13:43:00 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-07 13:43:00 - PDFExtractor - INFO - 图片字段提取完成
2025-08-07 13:43:00 - PDFExtractor - INFO - 添加记录: 梁佩仪
2025-08-07 13:43:00 - PDFExtractor - INFO - 添加新记录: 梁佩仪
2025-08-07 13:43:00 - PDFExtractor - INFO - 成功处理PDF: 202411080177_梁佩仪_440682198912122162.pdf
2025-08-07 13:43:00 - PDFExtractor - INFO - 开始处理PDF文件: 202412130028_董博远_460003197605312610.pdf
2025-08-07 13:43:00 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 8638字符
2025-08-07 13:43:00 - PDFExtractor - INFO - 文字字段提取完成
2025-08-07 13:43:00 - PDFExtractor - INFO - 开始提取图片字段
2025-08-07 13:43:00 - PDFExtractor - INFO - 关键词检查结果:
2025-08-07 13:43:00 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-07 13:43:00 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-07 13:43:00 - PDFExtractor - INFO -   原始文本长度: 8638字符
2025-08-07 13:43:00 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-07 13:43:00 - PDFExtractor - DEBUG - 第18页包含3张图片
2025-08-07 13:43:00 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-07 13:43:02 - PDFExtractor - ERROR - PaddleOCR延迟初始化失败: No module named 'shapely'
2025-08-07 13:43:02 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:02 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:02 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:02 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:02 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:02 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:03 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:03 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:03 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:03 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:03 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:04 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:04 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:04 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:04 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:04 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:04 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:04 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:05 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:05 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:05 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:05 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:05 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:05 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:05 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:06 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:06 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:06 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:06 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:06 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:06 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:06 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:07 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:07 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:07 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:07 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:07 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:07 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:07 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:08 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:08 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:08 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:08 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:08 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:08 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:09 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:09 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:09 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:43:09 - PDFExtractor - ERROR - OCR检查心电图图片失败: 'NoneType' object has no attribute 'ocr'
2025-08-07 13:43:10 - PDFExtractor - WARNING - OCR引擎初始化失败
2025-08-07 13:44:03 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-07 13:44:03 - PDFExtractor - INFO - ==================================================
2025-08-07 13:44:03 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-07 13:44:03 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-07 13:44:03 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-07 13:44:03 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-07 13:44:03 - PDFExtractor - INFO - 模块初始化完成
2025-08-07 13:44:03 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-07 13:44:03 - PDFExtractor - INFO - 找到 221 个待处理PDF文件
2025-08-07 13:44:03 - PDFExtractor - INFO - 开始处理PDF文件: 202411080177_梁佩仪_440682198912122162.pdf
2025-08-07 13:44:03 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1887字符
2025-08-07 13:44:03 - PDFExtractor - INFO - 文字字段提取完成
2025-08-07 13:44:03 - PDFExtractor - INFO - 开始提取图片字段
2025-08-07 13:44:03 - PDFExtractor - INFO - 关键词检查结果:
2025-08-07 13:44:03 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-07 13:44:03 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-07 13:44:03 - PDFExtractor - INFO -   原始文本长度: 1887字符
2025-08-07 13:44:03 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-07 13:44:03 - PDFExtractor - INFO - 图片字段提取完成
2025-08-07 13:44:03 - PDFExtractor - INFO - 添加记录: 梁佩仪
2025-08-07 13:44:03 - PDFExtractor - INFO - 添加新记录: 梁佩仪
2025-08-07 13:44:03 - PDFExtractor - INFO - 成功处理PDF: 202411080177_梁佩仪_440682198912122162.pdf
2025-08-07 13:44:03 - PDFExtractor - INFO - 开始处理PDF文件: 202412130028_董博远_460003197605312610.pdf
2025-08-07 13:44:03 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 8638字符
2025-08-07 13:44:03 - PDFExtractor - INFO - 文字字段提取完成
2025-08-07 13:44:03 - PDFExtractor - INFO - 开始提取图片字段
2025-08-07 13:44:03 - PDFExtractor - INFO - 关键词检查结果:
2025-08-07 13:44:03 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-07 13:44:03 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-07 13:44:03 - PDFExtractor - INFO -   原始文本长度: 8638字符
2025-08-07 13:44:03 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-07 13:44:03 - PDFExtractor - DEBUG - 第18页包含3张图片
2025-08-07 13:44:04 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
2025-08-07 13:45:06 - PDFExtractor - INFO - 日志系统初始化完成
2025-08-07 13:45:06 - PDFExtractor - INFO - ==================================================
2025-08-07 13:45:06 - PDFExtractor - INFO - OCR-PDF混合内容抽取整理程序启动
2025-08-07 13:45:06 - PDFExtractor - INFO - 初始化心超OCR引擎...
2025-08-07 13:45:06 - PDFExtractor - INFO - 初始化Excel处理器...
2025-08-07 13:45:06 - PDFExtractor - INFO - 初始化PDF处理器...
2025-08-07 13:45:06 - PDFExtractor - INFO - 模块初始化完成
2025-08-07 13:45:06 - PDFExtractor - INFO - 开始处理PDF文件...
2025-08-07 13:45:06 - PDFExtractor - INFO - 找到 221 个待处理PDF文件
2025-08-07 13:45:06 - PDFExtractor - INFO - 开始处理PDF文件: 202411080177_梁佩仪_440682198912122162.pdf
2025-08-07 13:45:06 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 1887字符
2025-08-07 13:45:06 - PDFExtractor - INFO - 文字字段提取完成
2025-08-07 13:45:06 - PDFExtractor - INFO - 开始提取图片字段
2025-08-07 13:45:06 - PDFExtractor - INFO - 关键词检查结果:
2025-08-07 13:45:06 - PDFExtractor - INFO -   发现的心超关键词: []
2025-08-07 13:45:06 - PDFExtractor - INFO -   发现的心电图关键词: []
2025-08-07 13:45:06 - PDFExtractor - INFO -   原始文本长度: 1887字符
2025-08-07 13:45:06 - PDFExtractor - INFO - 文本中未发现心超或心电图关键词，跳过图片处理
2025-08-07 13:45:06 - PDFExtractor - INFO - 图片字段提取完成
2025-08-07 13:45:06 - PDFExtractor - INFO - 添加记录: 梁佩仪
2025-08-07 13:45:06 - PDFExtractor - INFO - 添加新记录: 梁佩仪
2025-08-07 13:45:06 - PDFExtractor - INFO - 成功处理PDF: 202411080177_梁佩仪_440682198912122162.pdf
2025-08-07 13:45:06 - PDFExtractor - INFO - 开始处理PDF文件: 202412130028_董博远_460003197605312610.pdf
2025-08-07 13:45:06 - PDFExtractor - INFO - 开始提取文字字段，文本长度: 8638字符
2025-08-07 13:45:06 - PDFExtractor - INFO - 文字字段提取完成
2025-08-07 13:45:06 - PDFExtractor - INFO - 开始提取图片字段
2025-08-07 13:45:06 - PDFExtractor - INFO - 关键词检查结果:
2025-08-07 13:45:06 - PDFExtractor - INFO -   发现的心超关键词: ['心脏彩超']
2025-08-07 13:45:06 - PDFExtractor - INFO -   发现的心电图关键词: ['心电图']
2025-08-07 13:45:06 - PDFExtractor - INFO -   原始文本长度: 8638字符
2025-08-07 13:45:06 - PDFExtractor - INFO - 文本中发现关键词 - 心超: True, 心电图: True，开始图片处理
2025-08-07 13:45:06 - PDFExtractor - DEBUG - 第18页包含3张图片
2025-08-07 13:45:07 - PDFExtractor - INFO - 开始延迟初始化PaddleOCR（带超时保护）...
