#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式坐标标定工具 V2
支持大框数字识别模式的标定
"""

import cv2
import numpy as np
import os
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import logging
from typing import Dict, List, Tuple, Optional
import configparser

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InteractiveCalibratorV2:
    """交互式坐标标定工具 V2 - 支持大框数字识别"""
    
    def __init__(self):
        """初始化标定工具"""
        self.root = tk.Tk()
        self.root.title("医疗表单字段标定工具 V2 - 大框数字识别")
        self.root.geometry("1400x900")
        
        # 加载配置
        self.config = configparser.ConfigParser()
        self.config.read('config_new.ini', encoding='utf-8')
        self.digit_mode = self.config.get('OCR_SETTINGS', 'digit_recognition_mode', fallback='single_box')
        print(f"读取到的数字识别模式: {self.digit_mode}")
        
        # 数据存储
        self.current_image = None
        self.current_image_path = None
        self.field_definitions = self._load_field_definitions()
        self.calibrated_regions = {}
        self.current_field = None
        
        # 图像显示相关
        self.canvas_width = 800
        self.canvas_height = 600
        self.image_scale = 1.0
        self.image_offset = (0, 0)
        
        # 标定状态
        self.is_selecting = False
        self.selection_start = None
        self.selection_end = None
        self.current_rect_id = None
        
        self._setup_ui()
        
    def _load_field_definitions(self) -> Dict:
        """加载字段定义 - V2版本支持大框数字识别"""
        print(f"加载字段定义，当前模式: {self.digit_mode}")
        if 'large_box' in self.digit_mode:
            return {
                "基本信息": {
                    "姓名": {"type": "text_input", "color": "red"},
                    "手机号码": {"type": "digit_large_box", "color": "red", "description": "手机号码数字区域"},
                    "出生日期": {"type": "digit_large_box", "color": "red", "description": "整个出生日期区域"},
                    "性别男": {"type": "checkbox", "color": "blue"},
                    "性别女": {"type": "checkbox", "color": "blue"},
                    "血压": {"type": "digit_large_box", "color": "green", "description": "整个血压区域"},
                    "心率": {"type": "digit_large_box", "color": "green", "description": "心率数字区域"},
                    "体重": {"type": "digit_large_box", "color": "green", "description": "整个体重区域"},
                    "身高": {"type": "digit_large_box", "color": "green", "description": "整个身高区域"},
                    "临床症状无": {"type": "checkbox", "color": "blue"},
                    "临床症状有": {"type": "checkbox", "color": "blue"},
                    "临床症状具体": {"type": "text_input", "color": "blue"}
                },
                "危险因素及既往病史": {
                    "吸烟否": {"type": "checkbox", "color": "orange"},
                    "吸烟已戒烟": {"type": "checkbox", "color": "orange"},
                    "吸烟当前吸烟": {"type": "checkbox", "color": "orange"},
                    "饮酒否": {"type": "checkbox", "color": "orange"},
                    "饮酒已戒酒": {"type": "checkbox", "color": "orange"},
                    "饮酒当前饮酒": {"type": "checkbox", "color": "orange"},
                    "心肌梗死病史否": {"type": "checkbox", "color": "orange"},
                    "心肌梗死病史是": {"type": "checkbox", "color": "orange"},
                    "卒中史否": {"type": "checkbox", "color": "orange"},
                    "卒中史是": {"type": "checkbox", "color": "orange"},
                    "卒中史脑梗死": {"type": "checkbox", "color": "orange"},
                    "卒中史脑出血": {"type": "checkbox", "color": "orange"},
                    "卒中史不详": {"type": "checkbox", "color": "orange"},
                    "高血压病史否": {"type": "checkbox", "color": "orange"},
                    "高血压病史是": {"type": "checkbox", "color": "orange"},
                    "血脂异常病史否": {"type": "checkbox", "color": "orange"},
                    "血脂异常病史是": {"type": "checkbox", "color": "orange"},
                    "糖尿病否": {"type": "checkbox", "color": "orange"},
                    "糖尿病是": {"type": "checkbox", "color": "orange"},
                    "外周血管病否": {"type": "checkbox", "color": "orange"},
                    "外周血管病是": {"type": "checkbox", "color": "orange"}
                },
                "用药情况": {
                    "抗血小板药物否": {"type": "checkbox", "color": "purple"},
                    "抗血小板药物是": {"type": "checkbox", "color": "purple"},
                    "阿司匹林": {"type": "checkbox", "color": "purple"},
                    "氯吡格雷": {"type": "checkbox", "color": "purple"},
                    "替格瑞洛": {"type": "checkbox", "color": "purple"},
                    "他汀类否": {"type": "checkbox", "color": "purple"},
                    "他汀类是": {"type": "checkbox", "color": "purple"},
                    "阿托伐他汀": {"type": "checkbox", "color": "purple"},
                    "瑞舒伐他汀": {"type": "checkbox", "color": "purple"},
                    "他汀类其他": {"type": "checkbox", "color": "purple"},
                    "他汀类其他药物名称": {"type": "text_input", "color": "purple"},
                    "降压药物否": {"type": "checkbox", "color": "purple"},
                    "降压药物是": {"type": "checkbox", "color": "purple"},
                    "降糖药物否": {"type": "checkbox", "color": "purple"},
                    "降糖药物是": {"type": "checkbox", "color": "purple"},
                    "胰岛素否": {"type": "checkbox", "color": "purple"},
                    "胰岛素是": {"type": "checkbox", "color": "purple"}
                }
            }
        else:
            # 保持原有的单个数字框模式
            return self._load_original_field_definitions()
    
    def _load_original_field_definitions(self) -> Dict:
        """加载原始字段定义（单个数字框模式）"""
        return {
            "基本信息": {
                "姓名": {"type": "text_input", "color": "red"},
                "手机号码": {"type": "text_input", "color": "red"},
                "出生日期1": {"type": "digit_box", "color": "red"},
                "出生日期2": {"type": "digit_box", "color": "red"},
                "出生日期3": {"type": "digit_box", "color": "red"},
                "出生日期4": {"type": "digit_box", "color": "red"},
                "出生日期5": {"type": "digit_box", "color": "red"},
                "出生日期6": {"type": "digit_box", "color": "red"},
                "出生日期7": {"type": "digit_box", "color": "red"},
                "出生日期8": {"type": "digit_box", "color": "red"},
                "性别男": {"type": "checkbox", "color": "blue"},
                "性别女": {"type": "checkbox", "color": "blue"},
                "血压收缩压": {"type": "digit_box", "color": "green"},
                "血压舒张压": {"type": "digit_box", "color": "green"},
                "心率": {"type": "text_input", "color": "green"},
                "体重1": {"type": "digit_box", "color": "green"},
                "体重2": {"type": "digit_box", "color": "green"},
                "体重3": {"type": "digit_box", "color": "green"},
                "体重4": {"type": "digit_box", "color": "green"},
                "身高1": {"type": "digit_box", "color": "green"},
                "身高2": {"type": "digit_box", "color": "green"},
                "身高3": {"type": "digit_box", "color": "green"},
                "临床症状无": {"type": "checkbox", "color": "blue"},
                "临床症状有": {"type": "checkbox", "color": "blue"},
                "临床症状具体": {"type": "text_input", "color": "blue"}
            },
            "危险因素及既往病史": {
                "吸烟否": {"type": "checkbox", "color": "orange"},
                "吸烟已戒烟": {"type": "checkbox", "color": "orange"},
                "吸烟当前吸烟": {"type": "checkbox", "color": "orange"},
                "饮酒否": {"type": "checkbox", "color": "orange"},
                "饮酒已戒酒": {"type": "checkbox", "color": "orange"},
                "饮酒当前饮酒": {"type": "checkbox", "color": "orange"},
                "心肌梗死病史否": {"type": "checkbox", "color": "orange"},
                "心肌梗死病史是": {"type": "checkbox", "color": "orange"},
                "卒中史否": {"type": "checkbox", "color": "orange"},
                "卒中史是": {"type": "checkbox", "color": "orange"},
                "卒中史脑梗死": {"type": "checkbox", "color": "orange"},
                "卒中史脑出血": {"type": "checkbox", "color": "orange"},
                "卒中史不详": {"type": "checkbox", "color": "orange"},
                "高血压病史否": {"type": "checkbox", "color": "orange"},
                "高血压病史是": {"type": "checkbox", "color": "orange"},
                "血脂异常病史否": {"type": "checkbox", "color": "orange"},
                "血脂异常病史是": {"type": "checkbox", "color": "orange"},
                "糖尿病否": {"type": "checkbox", "color": "orange"},
                "糖尿病是": {"type": "checkbox", "color": "orange"},
                "外周血管病否": {"type": "checkbox", "color": "orange"},
                "外周血管病是": {"type": "checkbox", "color": "orange"}
            },
            "用药情况": {
                "抗血小板药物否": {"type": "checkbox", "color": "purple"},
                "抗血小板药物是": {"type": "checkbox", "color": "purple"},
                "阿司匹林": {"type": "checkbox", "color": "purple"},
                "氯吡格雷": {"type": "checkbox", "color": "purple"},
                "替格瑞洛": {"type": "checkbox", "color": "purple"},
                "他汀类否": {"type": "checkbox", "color": "purple"},
                "他汀类是": {"type": "checkbox", "color": "purple"},
                "阿托伐他汀": {"type": "checkbox", "color": "purple"},
                "瑞舒伐他汀": {"type": "checkbox", "color": "purple"},
                "他汀类其他": {"type": "checkbox", "color": "purple"},
                "他汀类其他药物名称": {"type": "text_input", "color": "purple"},
                "降压药物否": {"type": "checkbox", "color": "purple"},
                "降压药物是": {"type": "checkbox", "color": "purple"},
                "降糖药物否": {"type": "checkbox", "color": "purple"},
                "降糖药物是": {"type": "checkbox", "color": "purple"},
                "胰岛素否": {"type": "checkbox", "color": "purple"},
                "胰岛素是": {"type": "checkbox", "color": "purple"}
            }
        }
    
    def _setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame, width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 显示当前模式
        mode_label = ttk.Label(control_frame, text=f"数字识别模式: {self.digit_mode}", 
                              font=("Arial", 10, "bold"))
        mode_label.pack(pady=(0, 10))
        
        # 图像加载
        load_frame = ttk.LabelFrame(control_frame, text="图像加载", padding=10)
        load_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(load_frame, text="选择图像", command=self._load_image).pack(fill=tk.X)
        
        self.image_info_label = ttk.Label(load_frame, text="未加载图像")
        self.image_info_label.pack(pady=(5, 0))
        
        # 字段选择
        field_frame = ttk.LabelFrame(control_frame, text="字段选择", padding=10)
        field_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 字段树
        self.field_tree = ttk.Treeview(field_frame, height=15)
        self.field_tree.pack(fill=tk.BOTH, expand=True)
        
        # 填充字段树
        self._populate_field_tree()
        
        # 绑定选择事件
        self.field_tree.bind('<<TreeviewSelect>>', self._on_field_select)
        
        # 操作按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="保存标定", command=self._save_calibration).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="加载标定", command=self._load_calibration).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="清除当前", command=self._clear_current).pack(fill=tk.X)
        
        # 右侧图像显示区域
        image_frame = ttk.Frame(main_frame)
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 图像画布
        self.canvas = tk.Canvas(image_frame, width=self.canvas_width, height=self.canvas_height, 
                               bg='white', cursor='crosshair')
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind('<Button-1>', self._on_canvas_click)
        self.canvas.bind('<B1-Motion>', self._on_canvas_drag)
        self.canvas.bind('<ButtonRelease-1>', self._on_canvas_release)
        
        # 状态栏
        self.status_label = ttk.Label(self.root, text="请选择图像和字段开始标定")
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
    def _populate_field_tree(self):
        """填充字段树"""
        for category, fields in self.field_definitions.items():
            category_id = self.field_tree.insert('', 'end', text=category, open=True)
            for field_name, field_info in fields.items():
                field_type = field_info['type']
                description = field_info.get('description', '')
                display_text = f"{field_name} ({field_type})"
                if description:
                    display_text += f" - {description}"
                self.field_tree.insert(category_id, 'end', text=display_text, values=[field_name])

    def _load_image(self):
        """加载图像"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.png *.jpg *.jpeg *.bmp *.tiff")]
        )

        if file_path:
            try:
                self.current_image_path = file_path
                self.current_image = cv2.imread(file_path)

                if self.current_image is None:
                    messagebox.showerror("错误", "无法加载图像文件")
                    return

                self._display_image()
                self.image_info_label.config(text=f"已加载: {os.path.basename(file_path)}")
                self.status_label.config(text="图像已加载，请选择字段进行标定")

            except Exception as e:
                messagebox.showerror("错误", f"加载图像失败: {str(e)}")

    def _display_image(self):
        """显示图像"""
        if self.current_image is None:
            return

        # 计算缩放比例
        h, w = self.current_image.shape[:2]
        scale_w = self.canvas_width / w
        scale_h = self.canvas_height / h
        self.image_scale = min(scale_w, scale_h, 1.0)

        # 缩放图像
        new_w = int(w * self.image_scale)
        new_h = int(h * self.image_scale)
        resized_image = cv2.resize(self.current_image, (new_w, new_h))

        # 转换为PIL格式
        image_rgb = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image_rgb)
        self.photo = ImageTk.PhotoImage(pil_image)

        # 清除画布并显示图像
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)

        # 重新绘制已标定的区域
        self._redraw_calibrated_regions()

    def _on_field_select(self, event):
        """字段选择事件"""
        selection = self.field_tree.selection()
        if selection:
            item = self.field_tree.item(selection[0])
            if item['values']:  # 确保是字段而不是分类
                self.current_field = item['values'][0]
                self.status_label.config(text=f"已选择字段: {self.current_field}，请在图像上框选区域")

    def _on_canvas_click(self, event):
        """画布点击事件"""
        if self.current_field is None:
            messagebox.showwarning("警告", "请先选择要标定的字段")
            return

        self.is_selecting = True
        self.selection_start = (event.x, event.y)
        self.selection_end = (event.x, event.y)

        # 删除之前的选择框
        if self.current_rect_id:
            self.canvas.delete(self.current_rect_id)

    def _on_canvas_drag(self, event):
        """画布拖拽事件"""
        if not self.is_selecting:
            return

        self.selection_end = (event.x, event.y)

        # 删除之前的选择框
        if self.current_rect_id:
            self.canvas.delete(self.current_rect_id)

        # 绘制新的选择框
        self.current_rect_id = self.canvas.create_rectangle(
            self.selection_start[0], self.selection_start[1],
            self.selection_end[0], self.selection_end[1],
            outline='red', width=2
        )

    def _on_canvas_release(self, event):
        """画布释放事件"""
        if not self.is_selecting:
            return

        self.is_selecting = False
        self.selection_end = (event.x, event.y)

        # 计算实际坐标
        x1, y1 = self.selection_start
        x2, y2 = self.selection_end

        # 确保坐标顺序正确
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)

        # 转换为原图坐标
        orig_x1 = int(x1 / self.image_scale)
        orig_y1 = int(y1 / self.image_scale)
        orig_x2 = int(x2 / self.image_scale)
        orig_y2 = int(y2 / self.image_scale)

        # 保存标定结果
        region = [orig_x1, orig_y1, orig_x2 - orig_x1, orig_y2 - orig_y1]
        field_info = self._get_field_info(self.current_field)

        self.calibrated_regions[self.current_field] = {
            "region": region,
            "type": field_info['type'],
            "color": field_info['color']
        }

        # 重新绘制标定区域
        self._redraw_calibrated_regions()

        self.status_label.config(text=f"已标定字段: {self.current_field} - {region}")

    def _get_field_info(self, field_name: str) -> Dict:
        """获取字段信息"""
        for category, fields in self.field_definitions.items():
            if field_name in fields:
                return fields[field_name]
        return {"type": "unknown", "color": "black"}

    def _redraw_calibrated_regions(self):
        """重新绘制已标定的区域"""
        # 删除所有标定区域的绘制
        self.canvas.delete("calibrated")

        for field_name, field_data in self.calibrated_regions.items():
            region = field_data["region"]
            color = field_data["color"]

            # 转换为画布坐标
            x1 = int(region[0] * self.image_scale)
            y1 = int(region[1] * self.image_scale)
            x2 = int((region[0] + region[2]) * self.image_scale)
            y2 = int((region[1] + region[3]) * self.image_scale)

            # 绘制矩形
            self.canvas.create_rectangle(x1, y1, x2, y2, outline=color, width=2, tags="calibrated")

            # 添加标签
            self.canvas.create_text(x1, y1-10, text=field_name, fill=color, anchor=tk.SW, tags="calibrated")

    def _save_calibration(self):
        """保存标定结果"""
        if not self.calibrated_regions:
            messagebox.showwarning("警告", "没有标定数据可保存")
            return

        # 选择保存文件
        # output_file = f"field_calibration2.json" if self.digit_mode == 'large_box' else "field_calibration.json"
        # file_path = filedialog.asksaveasfilename(
        #     title="保存标定文件",
        #     defaultextension=".json",
        #     initialvalue=output_file,
        #     filetypes=[("JSON文件", "*.json")]
        # )


        file_path = filedialog.asksaveasfilename(
            title="保存标定配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                calibration_data = {
                    "version": "2.0" if self.digit_mode == 'large_box' else "1.0",
                    "digit_recognition_mode": self.digit_mode,
                    "calibrated_regions": self.calibrated_regions,
                    "image_info": {
                        "source_image": os.path.basename(self.current_image_path) if self.current_image_path else "",
                        "image_size": self.current_image.shape[:2] if self.current_image is not None else [0, 0]
                    }
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(calibration_data, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("成功", f"标定数据已保存到: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def _load_calibration(self):
        """加载标定结果"""
        file_path = filedialog.askopenfilename(
            title="选择标定文件",
            filetypes=[("JSON文件", "*.json")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.calibrated_regions = data.get("calibrated_regions", {})
                self._redraw_calibrated_regions()

                messagebox.showinfo("成功", f"已加载标定数据: {len(self.calibrated_regions)} 个字段")

            except Exception as e:
                messagebox.showerror("错误", f"加载失败: {str(e)}")

    def _clear_current(self):
        """清除当前选择"""
        if self.current_field and self.current_field in self.calibrated_regions:
            del self.calibrated_regions[self.current_field]
            self._redraw_calibrated_regions()
            self.status_label.config(text=f"已清除字段: {self.current_field}")

    def run(self):
        """运行标定工具"""
        self.root.mainloop()

def main():
    """主函数"""
    calibrator = InteractiveCalibratorV2()
    calibrator.run()

if __name__ == "__main__":
    main()
