# OCR-PDF内容抽取整理 - 第二轮优化说明

根据第二轮优化要求，项目进行了以下改进：

## 1. OCR引擎优化

### 图像处理改进
- 增加图像预处理功能，包括灰度转换、自适应直方图均衡化、降噪、锐化
- 添加可选的二值化处理
- 提高图像处理分辨率，从1600提升到1800像素
- 优化旋转检测策略，更好地识别翻转页面

### OCR参数调优
- 降低文本检测阈值(det_db_thresh)从0.3降至0.2，捕获更多细小文本
- 降低文本框阈值(det_db_box_thresh)从0.5降至0.3，提高检测敏感度
- 增加文本框扩展比例(det_db_unclip_ratio)从1.6提升至2.0，更好地合并相邻文本
- 增加批处理数量(rec_batch_num)从6增至10，提高处理效率
- 增加最大文本长度限制(max_text_length)设为200，支持更长的文本
- 降低置信度阈值(drop_score)设为0.3，保留更多潜在文本

### 损坏JPEG数据处理
- 增加多种读取图像的方法，处理损坏的JPEG数据
- 添加IMREAD_IGNORE_ORIENTATION标志，解决图像方向问题
- 增加更详细的错误日志，便于问题排查

## 2. 文本块拼接逻辑优化

### 按行合并文本块
- 实现按行合并相近的文本块，改进跨行文本处理
- 增加行合并阈值参数，可根据文档特性调整
- 保留原始文本块与行的关系，便于后续精确定位

### 改进字段提取逻辑
- 新增`_find_field_with_number_new`方法，优先在同一行查找关键词和对应值
- 当关键词和值不在同一块时，增加在附近文本块中查找数值的功能
- 实现按曼哈顿距离查找最近的数值块，提高匹配精度
- 添加字段搜索距离参数，灵活控制搜索范围

### 特定字段提取优化
- 改进超声所见和超声提示的提取逻辑，使用行级别的文本匹配
- 优化心电图结论提取，支持通过上下文关键词定位相关页面
- 增强EF值提取能力，添加多种匹配模式

## 3. 日志系统改进

### 日志配置增强
- 使用RotatingFileHandler实现日志文件自动切割，防止日志过大
- 分离文件日志级别和控制台日志级别，便于调试
- 增加详细的日志记录，包括图像处理、OCR识别、文本提取的各个环节
- 设置日志最大大小和备份数量，优化长期运行时的磁盘占用

### 运行信息展示
- 增加更详细的处理过程日志
- 在处理摘要中添加更多关键字段的统计信息
- 显示文件路径的绝对路径，便于定位

## 4. 配置系统优化

### 新增配置选项
- 添加二值化(binarize)选项，可选择是否进行图像二值化
- 增加降噪(denoise)选项，控制是否进行图像噪声处理
- 添加行合并阈值(line_merge_threshold)和字段搜索距离(field_search_distance)参数
- 优化日志配置，增加文件大小和备份数量设置

### 路径配置改进
- 修改所有相对路径为../开头，便于在不同目录结构下运行
- 确保所有输出目录自动创建，避免路径不存在错误

## 5. 新增工具

### 检查工具(check_pdf.py)
- 新增PDF检查脚本，用于测试单个PDF的处理效果
- 详细显示OCR识别结果、文本合并结果和字段提取结果
- 便于针对特定PDF文件进行问题诊断和参数调优

## 使用说明

1. 配置文件更新：检查config.ini中的新增配置项
2. 测试单个PDF：
   ```bash
   python -m OCR-PDF内容抽取整理.check_pdf <pdf文件路径>
   ```
3. 批量处理PDF：
   ```bash
   python -m OCR-PDF内容抽取整理.main
   ```

## 注意事项

1. 对于特别难以识别的PDF，可以尝试调整图像处理参数
2. 如果文本块拼接效果不理想，可以调整line_merge_threshold参数
3. 对于特殊的字段提取需求，可以修改对应的提取方法
4. 日志文件默认存储在../logs目录下，可以查看详细的处理记录 