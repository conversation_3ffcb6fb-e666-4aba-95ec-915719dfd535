import os
import re
import logging
import cv2
import numpy as np
from typing import List, Dict, Optional
from configparser import ConfigParser

logger = logging.getLogger('PDFExtractor')

class EchoOCREngine:
    """专门用于心超图片识别的OCR引擎"""
    
    def __init__(self, config: ConfigParser):
        self.config = config
        self.confidence_threshold = config.getfloat('OCR', 'confidence_threshold', fallback=0.6)
        self.ocr = None
        self._ocr_initialized = False
        # 延迟初始化OCR，只在需要时才初始化
    
    def _ensure_ocr_initialized(self):
        """确保OCR引擎已初始化（延迟初始化，带超时保护）"""
        if self._ocr_initialized:
            return self.ocr is not None

        try:
            logger.info('开始延迟初始化PaddleOCR（带超时保护）...')

            # 使用线程和超时来避免卡住（Windows兼容）
            import threading
            import time

            ocr_result = [None]
            exception_result = [None]

            def init_paddleocr():
                """在子线程中初始化PaddleOCR"""
                try:
                    from paddleocr import PaddleOCR
                    use_gpu = self.config.getboolean('OCR', 'use_gpu', fallback=False)
                    lang = self.config.get('OCR', 'lang', fallback='ch')

                    logger.info(f'PaddleOCR配置: use_gpu={use_gpu}, lang={lang}')

                    ocr = PaddleOCR(
                        use_angle_cls=True,
                        lang=lang,
                        use_gpu=use_gpu,
                        show_log=False
                    )
                    ocr_result[0] = ocr
                except Exception as e:
                    exception_result[0] = e

            # 启动初始化线程
            init_thread = threading.Thread(target=init_paddleocr)
            init_thread.daemon = True
            init_thread.start()

            # 等待最多30秒
            init_thread.join(timeout=30)

            if init_thread.is_alive():
                logger.error('PaddleOCR初始化超时（30秒），跳过心超OCR功能')
                self.ocr = None
                self._ocr_initialized = True
                return False

            if exception_result[0]:
                raise exception_result[0]

            if ocr_result[0]:
                self.ocr = ocr_result[0]
                logger.info('PaddleOCR延迟初始化成功')
                self._ocr_initialized = True
                return True
            else:
                logger.error('PaddleOCR初始化失败，未返回有效对象')
                self.ocr = None
                self._ocr_initialized = True
                return False

        except Exception as e:
            logger.error(f'PaddleOCR延迟初始化失败: {str(e)}')
            import traceback
            traceback.print_exc()
            self.ocr = None
            self._ocr_initialized = True  # 标记为已尝试初始化，避免重复尝试
            return False

    def _read_image_chinese_path(self, image_path: str):
        """读取图片，支持中文路径"""
        try:
            # 使用numpy读取文件，支持中文路径
            with open(image_path, 'rb') as f:
                img_data = f.read()
            img_array = np.frombuffer(img_data, np.uint8)
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            return img
        except Exception as e:
            logger.error(f'读取图片失败 {image_path}: {e}')
            return None
    
    def quick_check_echo_image(self, image_path: str) -> bool:
        """快速检查图片是否包含心超内容"""
        if not self._ensure_ocr_initialized():
            logger.warning('OCR引擎初始化失败')
            return False
            
        try:
            # 读取图片（支持中文路径）
            img = self._read_image_chinese_path(image_path)
            if img is None:
                return False
            
            # 简单预处理
            img = self._simple_preprocess(img)
            
            # 快速OCR识别（低置信度阈值）
            result = self.ocr.ocr(img, cls=True)
            
            if not result or not result[0]:
                return False
            
            # 检查是否包含心超关键词
            all_text = ""
            for line in result[0]:
                if len(line) >= 2:
                    text, confidence = line[1]
                    if confidence >= 0.3:  # 降低置信度要求用于快速检查
                        all_text += text + " "
            
            # 心超专用关键词 - 更精确的识别
            # 必须包含心脏相关的超声关键词，排除其他器官的超声
            heart_echo_keywords = ['超声心动图', '心脏超声', '心超', 'ECHO']
            heart_related_keywords = ['心脏', '心动', '左心室', '右心室', '左心房', '右心房', '主动脉', '二尖瓣', '三尖瓣']
            echo_content_keywords = ['超声所见', '超声提示', '超声诊断']

            # 排除其他器官的超声检查
            exclude_keywords = ['肝脏', '胆囊', '肾脏', '膀胱', '前列腺', '子宫', '卵巢', '甲状腺', '乳腺', '颈动脉', '下肢血管']

            # 检查是否包含排除关键词
            has_exclude = any(keyword in all_text for keyword in exclude_keywords)
            if has_exclude:
                logger.debug(f'图片包含非心脏超声关键词，跳过: {all_text[:100]}')
                return False

            # 必须同时满足：心脏相关关键词 + 超声内容关键词
            has_heart_echo = any(keyword in all_text for keyword in heart_echo_keywords)
            has_heart_related = any(keyword in all_text for keyword in heart_related_keywords)
            has_echo_content = any(keyword in all_text for keyword in echo_content_keywords)

            # 更严格的判断条件
            is_heart_echo = has_heart_echo or (has_heart_related and has_echo_content)

            if is_heart_echo:
                logger.info(f'识别为心脏超声图片，关键词: {all_text[:200]}')
            else:
                logger.debug(f'非心脏超声图片，文本: {all_text[:100]}')

            return is_heart_echo
            
        except Exception as e:
            logger.error(f'快速检查心超图片失败: {str(e)}')
            return False
    
    def extract_echo_info(self, image_path: str) -> Dict[str, str]:
        """从心超图片中提取详细信息"""
        if not self._ensure_ocr_initialized():
            logger.warning('OCR引擎初始化失败')
            return {}
        
        try:
            # 读取图片（支持中文路径）
            img = self._read_image_chinese_path(image_path)
            if img is None:
                logger.error(f'无法读取图片: {image_path}')
                return {}
            
            # 图像预处理
            img = self._enhanced_preprocess(img)
            
            # OCR识别
            result = self.ocr.ocr(img, cls=True)
            
            if not result or not result[0]:
                logger.warning(f'OCR未识别到任何文本: {image_path}')
                return {}
            
            # 提取文本块
            text_blocks = []
            for line in result[0]:
                if len(line) >= 2:
                    position = line[0]
                    text, confidence = line[1]
                    if confidence >= self.confidence_threshold:
                        # 计算中心点
                        center_x = sum([p[0] for p in position]) / 4
                        center_y = sum([p[1] for p in position]) / 4
                        
                        text_blocks.append({
                            'text': text,
                            'confidence': confidence,
                            'center_x': center_x,
                            'center_y': center_y
                        })
            
            # 按位置排序
            text_blocks.sort(key=lambda x: (x['center_y'], x['center_x']))
            
            # 合并文本
            full_text = ' '.join([block['text'] for block in text_blocks])
            
            logger.info(f'心超图片OCR识别完成，文本长度: {len(full_text)}')
            logger.debug(f'识别文本: {full_text[:200]}...')
            
            # 提取心超信息
            return self._extract_echo_fields(full_text)
            
        except Exception as e:
            logger.error(f'心超信息提取失败: {str(e)}')
            return {}
    
    def _simple_preprocess(self, img):
        """简单预处理用于快速检查"""
        try:
            # 转换为灰度图
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img
            
            # 简单的对比度增强
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            # 转回彩色图像
            return cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
            
        except Exception as e:
            logger.error(f'简单预处理失败: {str(e)}')
            return img
    
    def _enhanced_preprocess(self, img):
        """增强预处理用于详细识别"""
        try:
            # 转换为灰度图
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img
            
            # 自适应直方图均衡化
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            # 去噪
            denoised = cv2.fastNlMeansDenoising(enhanced)
            
            # 锐化
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(denoised, -1, kernel)
            
            # 转回彩色图像
            return cv2.cvtColor(sharpened, cv2.COLOR_GRAY2BGR)
            
        except Exception as e:
            logger.error(f'增强预处理失败: {str(e)}')
            return img
    
    def _extract_echo_fields(self, text: str) -> Dict[str, str]:
        """从OCR文本中提取心超字段"""
        result = {}

        logger.info(f'开始提取心超字段，文本长度: {len(text)}')
        logger.debug(f'文本前200字符: {text[:200]}')

        # 提取超声所见 - 增强模式匹配
        seen_patterns = [
            r'超声所见[:\s]*([^超声提示]+?)(?=超声提示|报告医师|$)',
            r'超声所见[：:]\s*([^超声提示]+?)(?=超声提示|$)',
            r'超声所见[:\s]*([^超声提示\n]+)',
            r'超声所见[：:](.*?)(?=超声提示|$)',
            r'超声所见(.*?)(?=超声提示|报告医师|$)'
        ]

        for i, pattern in enumerate(seen_patterns):
            match = re.search(pattern, text, re.DOTALL)
            if match:
                seen_text = match.group(1).strip()
                # 清理文本
                seen_text = re.sub(r'\s+', ' ', seen_text)
                seen_text = seen_text.replace('\n', ' ')
                if len(seen_text) > 10:  # 确保有实际内容
                    result['超声心动图_超声所见'] = seen_text
                    logger.info(f'✅ 使用模式{i+1}提取到超声所见: {seen_text[:100]}...')
                    break
            else:
                logger.debug(f'模式{i+1}未匹配到超声所见')

        # 提取超声提示 - 增强模式匹配
        suggest_patterns = [
            r'超声提示[:\s]*([^报告医师]+?)(?=报告医师|记录员|$)',
            r'超声提示[：:]\s*([^报告医师]+?)(?=报告医师|$)',
            r'超声提示[:\s]*([^\n]+)',
            r'超声提示[：:](.*?)(?=报告医师|记录员|$)',
            r'超声提示(.*?)(?=报告医师|记录员|$)'
        ]

        for i, pattern in enumerate(suggest_patterns):
            match = re.search(pattern, text, re.DOTALL)
            if match:
                suggest_text = match.group(1).strip()
                # 清理文本，去除记录员信息
                suggest_text = re.sub(r'记录员[：:]\s*\S+', '', suggest_text)
                suggest_text = re.sub(r'\s+', ' ', suggest_text)
                suggest_text = suggest_text.replace('\n', ' ')
                if len(suggest_text) > 5:  # 确保有实际内容
                    result['超声心动图_超声提示'] = suggest_text
                    logger.info(f'✅ 使用模式{i+1}提取到超声提示: {suggest_text[:100]}...')
                    break
            else:
                logger.debug(f'模式{i+1}未匹配到超声提示')

        # 调试信息
        if not result:
            logger.warning('未提取到心超信息，提供调试信息:')
            if '超声所见' in text:
                logger.info('✅ 文本中包含"超声所见"')
                seen_pos = text.find('超声所见')
                logger.info(f'"超声所见"后100字符: {text[seen_pos:seen_pos+100]}')
            else:
                logger.warning('❌ 文本中不包含"超声所见"')

            if '超声提示' in text:
                logger.info('✅ 文本中包含"超声提示"')
                suggest_pos = text.find('超声提示')
                logger.info(f'"超声提示"后100字符: {text[suggest_pos:suggest_pos+100]}')
            else:
                logger.warning('❌ 文本中不包含"超声提示"')

        return result
