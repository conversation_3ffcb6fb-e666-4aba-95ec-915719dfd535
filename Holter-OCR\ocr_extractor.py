"""
OCR Extraction Module for Holter PDF Reports
Extracts text from the first page of PDF files using PaddleOCR
"""

import os
import io
import fitz  # PyMuPDF
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from paddleocr import PaddleOCR
import logging
from typing import List, Tuple, Optional, Dict
import tempfile
import cv2


class HolterOCRExtractor:
    """OCR extractor for Holter PDF reports"""
    
    def __init__(self, use_gpu: bool = False, lang: str = 'ch'):
        """
        Initialize the OCR extractor

        Args:
            use_gpu: Whether to use GPU for OCR processing
            lang: Language for OCR ('ch' for Chinese)
        """
        self.logger = logging.getLogger(__name__)

        # Initialize OCR with more conservative settings to avoid errors
        try:
            self.ocr = PaddleOCR(
                use_angle_cls=True,
                lang=lang,
                use_gpu=use_gpu,
                show_log=False,
                enable_mkldnn=False,  # Disable MKLDNN to avoid primitive errors
                cpu_threads=1,        # Use single thread to avoid conflicts
                max_text_length=25    # Limit text length to avoid memory issues
            )
            self.logger.info("OCR initialized successfully with conservative settings")
        except Exception as e:
            self.logger.warning(f"Failed to initialize OCR with conservative settings: {e}")
            # Fallback to minimal settings
            self.ocr = PaddleOCR(
                use_angle_cls=False,
                lang=lang,
                use_gpu=False,
                show_log=False
            )
            self.logger.info("OCR initialized with minimal fallback settings")
        
    def pdf_to_image(self, pdf_path: str, page_num: int = 0, zoom: float = 1.5) -> Optional[np.ndarray]:
        """
        Convert PDF page to image using the most stable method

        Args:
            pdf_path: Path to PDF file
            page_num: Page number to convert (0-based)
            zoom: Zoom factor for image quality (reduced default for stability)

        Returns:
            Image as numpy array or None if failed
        """
        try:
            doc = fitz.open(pdf_path)

            if page_num >= len(doc):
                self.logger.error(f"Page {page_num} not found in {pdf_path}")
                doc.close()
                return None

            page = doc[page_num]

            # Use conservative settings to avoid errors
            mat = fitz.Matrix(zoom, zoom)

            # Get pixmap with minimal settings
            pix = page.get_pixmap(matrix=mat, alpha=False)

            # Convert to bytes and then to PIL Image
            img_data = pix.tobytes("png")
            doc.close()

            # Convert to PIL Image and then numpy array
            img = Image.open(io.BytesIO(img_data))

            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')

            return np.array(img)

        except Exception as e:
            self.logger.error(f"Error converting PDF to image: {e}")
            return None


    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Apply image preprocessing to improve OCR accuracy

        Args:
            image: Input image as numpy array

        Returns:
            Preprocessed image as numpy array
        """
        try:
            # Convert to PIL Image for processing
            if len(image.shape) == 3 and image.shape[2] == 4:
                # Convert RGBA to RGB
                pil_img = Image.fromarray(image).convert('RGB')
            else:
                pil_img = Image.fromarray(image)

            # 1. Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_img)
            pil_img = enhancer.enhance(1.2)  # Slight contrast boost

            # 2. Enhance sharpness
            enhancer = ImageEnhance.Sharpness(pil_img)
            pil_img = enhancer.enhance(1.1)  # Slight sharpness boost

            # 3. Convert to grayscale for better OCR
            pil_img = pil_img.convert('L')

            # 4. Apply slight noise reduction
            pil_img = pil_img.filter(ImageFilter.MedianFilter(size=3))

            # Convert back to numpy array
            processed_image = np.array(pil_img)

            # 5. Apply adaptive thresholding using OpenCV if available
            try:
                # Convert to OpenCV format
                cv_img = cv2.cvtColor(processed_image, cv2.COLOR_GRAY2BGR) if len(processed_image.shape) == 2 else processed_image
                cv_gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY) if len(cv_img.shape) == 3 else cv_img

                # Apply adaptive threshold
                thresh_img = cv2.adaptiveThreshold(
                    cv_gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
                )
                processed_image = thresh_img
            except:
                # If OpenCV processing fails, use the PIL processed image
                pass

            return processed_image

        except Exception as e:
            self.logger.warning(f"Image preprocessing failed, using original: {e}")
            return image

    def extract_text_from_image(self, image: np.ndarray) -> List[Tuple[str, float]]:
        """
        Extract text from image using OCR with simple but stable approach

        Args:
            image: Image as numpy array

        Returns:
            List of (text, confidence) tuples
        """
        try:
            # Save image temporarily for OCR
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                # Convert to PIL Image
                if len(image.shape) == 2:
                    # Grayscale image
                    img_pil = Image.fromarray(image, mode='L')
                elif len(image.shape) == 3:
                    # Color image
                    img_pil = Image.fromarray(image)
                else:
                    self.logger.error(f"Unsupported image shape: {image.shape}")
                    return []

                # Convert to RGB for consistency
                if img_pil.mode != 'RGB':
                    img_pil = img_pil.convert('RGB')

                # Save image
                img_pil.save(tmp_file.name)
                tmp_path = tmp_file.name

            try:
                # Perform OCR with error handling
                result = self.ocr.ocr(tmp_path, cls=True)

                text_results = []
                if result and result[0]:
                    for line in result[0]:
                        if line and len(line) >= 2 and line[1]:
                            text = line[1][0] if line[1][0] else ""
                            confidence = line[1][1] if len(line[1]) > 1 else 0.0
                            if text.strip():  # Only add non-empty text
                                text_results.append((text.strip(), confidence))

                return text_results

            finally:
                try:
                    os.unlink(tmp_path)
                except:
                    pass

        except Exception as e:
            self.logger.error(f"Error during OCR processing: {e}")
            return []


    
    def extract_text_from_pdf(self, pdf_path: str, page_num: int = 0) -> List[Tuple[str, float]]:
        """
        Extract text from PDF page using OCR
        
        Args:
            pdf_path: Path to PDF file
            page_num: Page number to process (0-based, default first page)
            
        Returns:
            List of (text, confidence) tuples
        """
        self.logger.info(f"Processing PDF: {pdf_path}")
        
        # Convert PDF page to image
        image = self.pdf_to_image(pdf_path, page_num)
        if image is None:
            return []
        
        # Extract text using OCR
        text_results = self.extract_text_from_image(image)
        
        self.logger.info(f"Extracted {len(text_results)} text lines from {pdf_path}")
        return text_results
    
    def get_full_text(self, pdf_path: str, page_num: int = 0) -> str:
        """
        Get full text content from PDF page
        
        Args:
            pdf_path: Path to PDF file
            page_num: Page number to process
            
        Returns:
            Full text content as string
        """
        text_results = self.extract_text_from_pdf(pdf_path, page_num)
        return '\n'.join([text for text, _ in text_results])
    
    def get_text_with_confidence(self, pdf_path: str, page_num: int = 0, min_confidence: float = 0.5) -> List[str]:
        """
        Get text lines with minimum confidence threshold
        
        Args:
            pdf_path: Path to PDF file
            page_num: Page number to process
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of text lines meeting confidence threshold
        """
        text_results = self.extract_text_from_pdf(pdf_path, page_num)
        return [text for text, conf in text_results if conf >= min_confidence]


def setup_logging(log_level: str = 'INFO') -> None:
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('holter_ocr.log'),
            logging.StreamHandler()
        ]
    )


if __name__ == "__main__":
    # Test the OCR extractor
    setup_logging()
    
    extractor = HolterOCRExtractor()
    
    # Test with a sample PDF
    pdf_dir = "Holter"
    pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]
    
    if pdf_files:
        test_pdf = os.path.join(pdf_dir, pdf_files[0])
        print(f"Testing with: {test_pdf}")
        
        text_results = extractor.extract_text_from_pdf(test_pdf)
        print(f"\nExtracted {len(text_results)} lines:")
        
        for text, confidence in text_results[:10]:  # Show first 10 lines
            print(f"{text} (confidence: {confidence:.2f})")
