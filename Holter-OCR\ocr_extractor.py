"""
OCR Extraction Module for Holter PDF Reports
Extracts text from the first page of PDF files using PaddleOCR
"""

import os
import fitz  # PyMuPDF
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR
import logging
from typing import List, Tuple, Optional, Dict
import tempfile


class HolterOCRExtractor:
    """OCR extractor for Holter PDF reports"""
    
    def __init__(self, use_gpu: bool = False, lang: str = 'ch'):
        """
        Initialize the OCR extractor
        
        Args:
            use_gpu: Whether to use GPU for OCR processing
            lang: Language for OCR ('ch' for Chinese)
        """
        self.logger = logging.getLogger(__name__)
        self.ocr = PaddleOCR(
            use_angle_cls=True, 
            lang=lang,
            use_gpu=use_gpu,
            show_log=False  # Reduce verbose output
        )
        
    def pdf_to_image(self, pdf_path: str, page_num: int = 0, zoom: float = 2.0) -> Optional[np.ndarray]:
        """
        Convert PDF page to image
        
        Args:
            pdf_path: Path to PDF file
            page_num: Page number to convert (0-based)
            zoom: Zoom factor for image quality
            
        Returns:
            Image as numpy array or None if failed
        """
        try:
            doc = fitz.open(pdf_path)
            
            if page_num >= len(doc):
                self.logger.error(f"Page {page_num} not found in {pdf_path}")
                doc.close()
                return None
                
            page = doc[page_num]
            
            # Convert page to image with specified zoom
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)
            
            # Convert to numpy array
            img_data = pix.tobytes("png")
            doc.close()
            
            # Convert to PIL Image then to numpy array
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                tmp_file.write(img_data)
                tmp_path = tmp_file.name
            
            try:
                img = Image.open(tmp_path)
                img_array = np.array(img)
                return img_array
            finally:
                os.unlink(tmp_path)
                
        except Exception as e:
            self.logger.error(f"Error converting PDF to image: {e}")
            return None
    
    def extract_text_from_image(self, image: np.ndarray) -> List[Tuple[str, float]]:
        """
        Extract text from image using OCR
        
        Args:
            image: Image as numpy array
            
        Returns:
            List of (text, confidence) tuples
        """
        try:
            # Save image temporarily for OCR
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                img_pil = Image.fromarray(image)
                img_pil.save(tmp_file.name)
                tmp_path = tmp_file.name
            
            try:
                # Perform OCR
                result = self.ocr.ocr(tmp_path, cls=True)
                
                text_results = []
                if result and result[0]:
                    for line in result[0]:
                        text = line[1][0]
                        confidence = line[1][1]
                        text_results.append((text, confidence))
                
                return text_results
                
            finally:
                os.unlink(tmp_path)
                
        except Exception as e:
            self.logger.error(f"Error during OCR processing: {e}")
            return []
    
    def extract_text_from_pdf(self, pdf_path: str, page_num: int = 0) -> List[Tuple[str, float]]:
        """
        Extract text from PDF page using OCR
        
        Args:
            pdf_path: Path to PDF file
            page_num: Page number to process (0-based, default first page)
            
        Returns:
            List of (text, confidence) tuples
        """
        self.logger.info(f"Processing PDF: {pdf_path}")
        
        # Convert PDF page to image
        image = self.pdf_to_image(pdf_path, page_num)
        if image is None:
            return []
        
        # Extract text using OCR
        text_results = self.extract_text_from_image(image)
        
        self.logger.info(f"Extracted {len(text_results)} text lines from {pdf_path}")
        return text_results
    
    def get_full_text(self, pdf_path: str, page_num: int = 0) -> str:
        """
        Get full text content from PDF page
        
        Args:
            pdf_path: Path to PDF file
            page_num: Page number to process
            
        Returns:
            Full text content as string
        """
        text_results = self.extract_text_from_pdf(pdf_path, page_num)
        return '\n'.join([text for text, _ in text_results])
    
    def get_text_with_confidence(self, pdf_path: str, page_num: int = 0, min_confidence: float = 0.5) -> List[str]:
        """
        Get text lines with minimum confidence threshold
        
        Args:
            pdf_path: Path to PDF file
            page_num: Page number to process
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of text lines meeting confidence threshold
        """
        text_results = self.extract_text_from_pdf(pdf_path, page_num)
        return [text for text, conf in text_results if conf >= min_confidence]


def setup_logging(log_level: str = 'INFO') -> None:
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('holter_ocr.log'),
            logging.StreamHandler()
        ]
    )


if __name__ == "__main__":
    # Test the OCR extractor
    setup_logging()
    
    extractor = HolterOCRExtractor()
    
    # Test with a sample PDF
    pdf_dir = "Holter"
    pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]
    
    if pdf_files:
        test_pdf = os.path.join(pdf_dir, pdf_files[0])
        print(f"Testing with: {test_pdf}")
        
        text_results = extractor.extract_text_from_pdf(test_pdf)
        print(f"\nExtracted {len(text_results)} lines:")
        
        for text, confidence in text_results[:10]:  # Show first 10 lines
            print(f"{text} (confidence: {confidence:.2f})")
