"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_pdf2word_test
date: 2025/7/29 10:13
desc: 
"""
import fitz  # PyMuPDF
from docx import Document
from docx.shared import Inches
from PIL import Image
import io
import os


def pdf_to_word(pdf_path, output_docx_path):
    # 打开 PDF 文件
    pdf_document = fitz.open(pdf_path)

    # 创建 Word 文档
    doc = Document()

    # 遍历 PDF 的每一页
    for page_num in range(len(pdf_document)):
        page = pdf_document.load_page(page_num)

        # 提取文字内容并添加到 Word
        text = page.get_text()
        if text.strip():  # 如果页面有文字
            doc.add_paragraph(text)

        # 提取图片
        image_list = page.get_images(full=True)

        # 遍历当前页的所有图片
        for img_index, img_info in enumerate(image_list):
            xref = img_info[0]  # 图片引用 ID
            base_image = pdf_document.extract_image(xref)
            image_bytes = base_image["image"]

            # 将图片数据转换为 PIL Image
            image = Image.open(io.BytesIO(image_bytes))

            # 临时保存图片（因为 python-docx 需要文件路径）
            temp_img_path = f"temp_img_{page_num}_{img_index}.png"
            image.save(temp_img_path)

            # 将图片添加到 Word 文档
            doc.add_picture(temp_img_path, width=Inches(4.0))  # 设置图片宽度为 4 英寸

            # 删除临时图片文件
            os.remove(temp_img_path)

    # 保存 Word 文档
    doc.save(output_docx_path)
    print(f"Word 文档已保存到: {output_docx_path}")


# 使用示例
pdf_path = "202412130173_弄庆新_45250119800812256X.pdf"  # 替换为你的 PDF 文件路径
output_docx_path = "202412130173_弄庆新_45250119800812256X.docx"  # 输出的 Word 文件路径
pdf_to_word(pdf_path, output_docx_path)