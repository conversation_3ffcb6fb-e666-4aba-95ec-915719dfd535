#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ROI图片唯一标识符功能
验证每个记录都有自己独立的ROI图片
"""

import os
import cv2
import numpy as np
import time
import shutil
from field_extractor import FieldExtractor
import configparser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_environment():
    """创建测试环境"""
    print("🔧 创建测试环境...")
    
    # 清理并创建必要的目录
    test_dirs = [
        "temp_test_images",
        "roi_images"
    ]

    for dir_name in test_dirs:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
            except Exception as e:
                print(f"⚠️  无法删除目录 {dir_name}: {str(e)}")
        os.makedirs(dir_name, exist_ok=True)
        print(f"✅ 创建目录: {dir_name}")

    # 确保output目录存在但不删除（可能有文件被占用）
    os.makedirs("output", exist_ok=True)
    
    return True

def create_test_images():
    """创建多个不同的测试图像"""
    print("\n📸 创建测试图像...")
    
    test_images = [
        ("person1.png", "张三", "13800138001"),
        ("person2.png", "李四", "13800138002"), 
        ("person3.png", "王五", "13800138003")
    ]
    
    created_files = []
    
    for filename, name, phone in test_images:
        filepath = os.path.join("temp_test_images", filename)
        
        # 创建不同内容的测试图像
        image = np.ones((600, 800, 3), dtype=np.uint8) * 255
        
        # 添加不同的姓名和手机号码
        cv2.putText(image, f"Name: {name}", (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
        cv2.putText(image, f"Phone: {phone}", (50, 250), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
        cv2.putText(image, f"File: {filename}", (50, 300), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # 添加一些模拟的表格线
        cv2.rectangle(image, (40, 180), (400, 320), (0, 0, 0), 2)
        cv2.line(image, (40, 230), (400, 230), (0, 0, 0), 1)
        cv2.line(image, (40, 280), (400, 280), (0, 0, 0), 1)
        
        # 保存图像
        cv2.imwrite(filepath, image)
        created_files.append(filepath)
        print(f"  ✅ 创建测试图像: {filename} ({name}, {phone})")
    
    return created_files

def test_unique_roi_generation():
    """测试唯一ROI图片生成"""
    print("\n🧪 测试唯一ROI图片生成...")
    
    try:
        # 创建测试环境
        create_test_environment()
        
        # 创建测试图像
        test_images = create_test_images()
        
        # 创建字段提取器
        config = configparser.ConfigParser()
        config.read('config_new.ini', encoding='utf-8')
        
        ocr_config = {
            'confidence_threshold': 0.1,
            'use_gpu': False,
            'use_angle_cls': True,
            'lang': 'ch'
        }
        
        extractor = FieldExtractor("field_calibration3.json", ocr_config, config)
        
        print(f"✅ 字段提取器创建成功")
        
        # 处理每个测试图像
        extraction_results = []
        
        for image_path in test_images:
            filename = os.path.basename(image_path)
            print(f"\n🔍 处理图像: {filename}")
            
            # 等待一小段时间确保时间戳不同
            time.sleep(0.1)
            
            # 提取字段（这会生成ROI图片）
            result = extractor.extract_all_fields(image_path, align_image=False)
            
            if "error" not in result:
                extraction_results.append(result)
                print(f"  ✅ 字段提取成功: {len(result.get('extracted_fields', {}))} 个字段")
            else:
                print(f"  ❌ 字段提取失败: {result.get('error')}")
        
        # 检查ROI图片生成情况
        print(f"\n📊 检查ROI图片生成情况...")
        
        roi_dir = "roi_images"
        if os.path.exists(roi_dir):
            roi_files = [f for f in os.listdir(roi_dir) if f.endswith('.png')]
            print(f"总共生成了 {len(roi_files)} 个ROI图片")
            
            # 按字段分组统计
            field_counts = {}
            for filename in roi_files:
                # 提取字段名（假设格式为：图像标识_字段名_字段类型.png）
                parts = filename.split('_')
                if len(parts) >= 3:
                    field_name = parts[-2]  # 倒数第二个部分是字段名
                    if field_name not in field_counts:
                        field_counts[field_name] = []
                    field_counts[field_name].append(filename)
            
            print(f"\n📋 各字段的ROI图片数量:")
            for field_name, files in field_counts.items():
                print(f"  {field_name}: {len(files)} 个")
                if len(files) <= 3:  # 如果数量不多，显示文件名
                    for file in files:
                        print(f"    - {file}")
            
            # 验证是否每个图像都有独立的ROI图片
            expected_images = len(test_images)
            target_fields = ["姓名", "手机号码", "出生日期", "血压", "心率", "体重", "身高"]
            
            success_count = 0
            total_expected = expected_images * len(target_fields)
            
            for field_name in target_fields:
                field_files = field_counts.get(field_name, [])
                if len(field_files) == expected_images:
                    success_count += len(field_files)
                    print(f"  ✅ {field_name}: 每个图像都有独立的ROI图片")
                else:
                    print(f"  ❌ {field_name}: 期望 {expected_images} 个，实际 {len(field_files)} 个")
            
            print(f"\n🎯 ROI图片唯一性验证: {success_count}/{total_expected} ({success_count/total_expected*100:.1f}%)")
            
            return success_count == total_expected
        else:
            print("❌ ROI图片目录不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_roi_image_finding():
    """测试ROI图片查找功能"""
    print("\n🔍 测试ROI图片查找功能...")
    
    try:
        from main_new import SimpleExcelHandler
        
        # 创建Excel处理器
        config = configparser.ConfigParser()
        config.read('config_new.ini', encoding='utf-8')
        excel_handler = SimpleExcelHandler(config)
        
        # 测试图像路径
        test_image_paths = [
            "temp_test_images/person1.png",
            "temp_test_images/person2.png", 
            "temp_test_images/person3.png"
        ]
        
        target_fields = ["姓名", "手机号码", "出生日期", "血压", "心率", "体重", "身高"]
        
        print("🔍 测试ROI图片查找:")
        
        found_count = 0
        total_searches = len(test_image_paths) * len(target_fields)
        
        for image_path in test_image_paths:
            image_name = os.path.basename(image_path)
            print(f"\n  📄 图像: {image_name}")
            
            for field_name in target_fields:
                roi_path = excel_handler._find_roi_image(field_name, image_path)
                
                if roi_path and os.path.exists(roi_path):
                    found_count += 1
                    roi_filename = os.path.basename(roi_path)
                    print(f"    ✅ {field_name}: {roi_filename}")
                else:
                    print(f"    ❌ {field_name}: 未找到ROI图片")
        
        print(f"\n📊 ROI图片查找成功率: {found_count}/{total_searches} ({found_count/total_searches*100:.1f}%)")
        
        return found_count >= total_searches * 0.8  # 80%以上认为成功
        
    except Exception as e:
        print(f"❌ ROI图片查找测试失败: {str(e)}")
        return False

def test_roi_uniqueness_verification():
    """验证ROI图片内容的唯一性"""
    print("\n🔬 验证ROI图片内容唯一性...")
    
    try:
        roi_dir = "roi_images"
        if not os.path.exists(roi_dir):
            print("❌ ROI图片目录不存在")
            return False
        
        # 按字段分组ROI图片
        field_groups = {}
        roi_files = [f for f in os.listdir(roi_dir) if f.endswith('.png')]
        
        for filename in roi_files:
            parts = filename.split('_')
            if len(parts) >= 3:
                field_name = parts[-2]  # 字段名
                if field_name not in field_groups:
                    field_groups[field_name] = []
                field_groups[field_name].append(filename)
        
        # 验证每个字段的ROI图片是否内容不同
        unique_fields = 0
        total_fields = len(field_groups)
        
        for field_name, files in field_groups.items():
            if len(files) <= 1:
                continue  # 只有一个文件，无法比较
            
            print(f"\n  🔍 验证字段: {field_name} ({len(files)} 个图片)")
            
            # 读取所有图片并计算哈希值
            image_hashes = []
            for filename in files:
                filepath = os.path.join(roi_dir, filename)
                try:
                    image = cv2.imread(filepath)
                    if image is not None:
                        # 计算图像哈希值
                        image_hash = hash(image.tobytes())
                        image_hashes.append((filename, image_hash))
                except Exception as e:
                    print(f"    ⚠️  读取图片失败: {filename} - {str(e)}")
            
            # 检查是否有重复的哈希值
            hash_values = [h for _, h in image_hashes]
            unique_hashes = set(hash_values)
            
            if len(unique_hashes) == len(hash_values):
                unique_fields += 1
                print(f"    ✅ 所有ROI图片内容都不同")
            else:
                print(f"    ❌ 发现重复的ROI图片内容")
                # 找出重复的图片
                from collections import Counter
                hash_counts = Counter(hash_values)
                for (filename, img_hash) in image_hashes:
                    if hash_counts[img_hash] > 1:
                        print(f"      🔄 重复图片: {filename}")
        
        print(f"\n📊 ROI图片内容唯一性: {unique_fields}/{total_fields} 个字段通过验证")
        
        return unique_fields >= total_fields * 0.8  # 80%以上认为成功
        
    except Exception as e:
        print(f"❌ ROI图片唯一性验证失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 ROI图片唯一标识符功能测试")
    print("="*80)
    
    # 运行测试
    tests = [
        ("唯一ROI图片生成", test_unique_roi_generation),
        ("ROI图片查找功能", test_roi_image_finding),
        ("ROI图片内容唯一性", test_roi_uniqueness_verification)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 50)
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
                
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results[test_name] = False
    
    # 总结
    print("\n" + "="*80)
    print("📊 测试结果总结")
    print("="*80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！ROI图片唯一标识符功能已正确实现")
        print("\n💡 功能说明:")
        print("1. 每个图像处理时都会生成唯一的标识符")
        print("2. ROI图片文件名包含图像标识符，确保不会被覆盖")
        print("3. Excel中的ROI图片查找支持模糊匹配")
        print("4. 每个记录都有自己独立的ROI图片")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")
        
        if not results.get("唯一ROI图片生成", True):
            print("💡 建议检查字段提取器的图像标识符生成逻辑")
        
        if not results.get("ROI图片查找功能", True):
            print("💡 建议检查Excel处理器的ROI图片查找算法")
    
    print("="*80)

if __name__ == "__main__":
    main()
