#!/usr/bin/env python3
"""
Test script for preprocessing improvements
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import sys
sys.path.append('.')

from ocr_extractor import HolterOCRExtractor
from field_extractor import HolterFieldExtractor

def test_single_file(filename):
    """Test processing of a single file"""
    print(f"\n=== Testing {filename} ===")
    
    try:
        # Initialize extractors
        ocr_extractor = HolterOCRExtractor()
        field_extractor = HolterFieldExtractor()
        
        pdf_path = f"Holter/{filename}"
        
        # Extract text
        full_text = ocr_extractor.get_full_text(pdf_path)
        print(f"Text length: {len(full_text)}")
        
        if full_text.strip():
            # Extract fields
            extracted_data = field_extractor.extract_all_fields(full_text, filename)
            cleaned_data = field_extractor.clean_and_validate_data(extracted_data)
            
            # Count non-null fields
            non_null_count = len([v for v in cleaned_data.values() if v is not None])
            print(f"Extracted fields: {non_null_count}/31")
            
            # Show key fields
            key_fields = ['姓名', '总心搏数', '平均心率（bpm）', '报告结论']
            for field in key_fields:
                if field in cleaned_data and cleaned_data[field] is not None:
                    value = cleaned_data[field]
                    if field == '报告结论':
                        print(f"{field}: {str(value)[:50]}...")
                    else:
                        print(f"{field}: {value}")
            
            return True
        else:
            print("No text extracted")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Main test function"""
    print("Testing preprocessing improvements...")
    
    # Test files that previously had issues
    test_files = [
        "管友田.pdf",
        "于峰修.pdf", 
        "何秀侠.pdf",
        "丁刚.pdf"
    ]
    
    success_count = 0
    for filename in test_files:
        if test_single_file(filename):
            success_count += 1
    
    print(f"\n=== Summary ===")
    print(f"Success rate: {success_count}/{len(test_files)} ({success_count/len(test_files)*100:.1f}%)")

if __name__ == "__main__":
    main()
