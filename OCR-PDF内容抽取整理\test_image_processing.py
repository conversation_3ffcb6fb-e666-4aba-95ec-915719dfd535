import cv2
import numpy as np
import configparser
import os
import argparse
import logging
from configparser import ConfigParser
import sys
# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def visualize_preprocessing(image_path: str, config: configparser.ConfigParser, output_dir: str):
    """
    加载图像，应用在config.ini中定义的预处理步骤，
    并将每个步骤的结果保存到输出目录中。
    """
    if not os.path.exists(image_path):
        logging.error(f"图片未找到: {image_path}")
        return

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    logging.info(f"处理结果将保存在: {os.path.abspath(output_dir)}")

    # 加载原始图像
    # 使用np.fromfile和cv2.imdecode来处理可能包含非ASCII字符的路径
    img = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
    if img is None:
        logging.error(f"无法加载图片: {image_path}")
        return

    # 保存原始图像
    cv2.imwrite(os.path.join(output_dir, '00_original.png'), img)
    logging.info("已保存: 00_original.png")

    # --- 开始预处理流程 (逻辑与 ocr_engine.py 中的 preprocess_image 一致) ---
    
    current_img = img.copy()
    step = 0

    # # 1. 图像尺寸标准化
    # step += 1
    # height, width = current_img.shape[:2]
    # if width > 1800 or height > 1800:
    #     scale = min(1800 / width, 1800 / height)
    #     current_img = cv2.resize(current_img, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
    #     cv2.imwrite(os.path.join(output_dir, f'{step:02d}_resized.png'), current_img)
    #     logging.info(f"已保存: {step:02d}_resized.png")
    
    # 2. 转为灰度图像
    step += 1
    gray = cv2.cvtColor(current_img, cv2.COLOR_BGR2GRAY)
    cv2.imwrite(os.path.join(output_dir, f'{step:02d}_grayscale.png'), gray)
    logging.info(f"已保存: {step:02d}_grayscale.png")
    
    processed_img = gray.copy()

    # 3. 对比度增强 (CLAHE)
    step += 1
    if config.getboolean('IMAGE_PROCESSING', 'enhance_contrast', fallback=True):
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        processed_img = clahe.apply(processed_img)
        cv2.imwrite(os.path.join(output_dir, f'{step:02d}_contrast_enhanced.png'), processed_img)
        logging.info(f"已保存: {step:02d}_contrast_enhanced.png")

    # 5. 锐化处理
    step += 1
    if config.getboolean('IMAGE_PROCESSING', 'sharpen', fallback=True):
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
        processed_img = cv2.filter2D(processed_img, -1, kernel)
        cv2.imwrite(os.path.join(output_dir, f'{step:02d}_sharpened.png'), processed_img)
        logging.info(f"已保存: {step:02d}_sharpened.png")

    # 4. 降噪处理
    step += 1
    if config.getboolean('IMAGE_PROCESSING', 'denoise', fallback=True):
        processed_img = cv2.fastNlMeansDenoising(processed_img, None, 10, 7, 21)
        cv2.imwrite(os.path.join(output_dir, f'{step:02d}_denoised.png'), processed_img)
        logging.info(f"已保存: {step:02d}_denoised.png")


    
    sharpened = processed_img.copy()

    # 6. 二值化
    step += 1
    binarize_method = config.get('IMAGE_PROCESSING', 'binarize_method', fallback='none')
    if binarize_method != 'none':
        if binarize_method == 'otsu':
            _, processed_img = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif binarize_method == 'adaptive':
            processed_img = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                                 cv2.THRESH_BINARY, 11, 2)
        elif binarize_method == 'local':
            processed_img = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_MEAN_C, 
                                                 cv2.THRESH_BINARY, 15, 5)
        filename = f'{step:02d}_binarized_{binarize_method}.png'
        cv2.imwrite(os.path.join(output_dir, filename), processed_img)
        logging.info(f"已保存: {filename}")

    # 7. 形态学操作
    step += 1
    if config.getboolean('IMAGE_PROCESSING', 'apply_morphology', fallback=False):
        morph_type = config.get('IMAGE_PROCESSING', 'morphology_type', fallback='dilate')
        kernel_size = config.getint('IMAGE_PROCESSING', 'morphology_kernel_size', fallback=1)
        morph_kernel = np.ones((kernel_size, kernel_size), np.uint8)
        
        original_for_morph = processed_img.copy()
        if morph_type == 'erode':
            processed_img = cv2.erode(original_for_morph, morph_kernel, iterations=1)
        elif morph_type == 'dilate':
            processed_img = cv2.dilate(original_for_morph, morph_kernel, iterations=1)
        elif morph_type == 'open':
            processed_img = cv2.morphologyEx(original_for_morph, cv2.MORPH_OPEN, morph_kernel)
        elif morph_type == 'close':
            processed_img = cv2.morphologyEx(original_for_morph, cv2.MORPH_CLOSE, morph_kernel)
        
        filename = f'{step:02d}_morphology_{morph_type}.png'
        cv2.imwrite(os.path.join(output_dir, filename), processed_img)
        logging.info(f"已保存: {filename}")

    # 8. 图像去倾斜
    step += 1
    if config.getboolean('IMAGE_PROCESSING', 'deskew', fallback=False):
        try:
            coords = np.column_stack(np.where(processed_img > 0))
            angle = cv2.minAreaRect(coords)[-1]
            if angle < -45: angle = -(90 + angle)
            else: angle = -angle
            
            if abs(angle) > 0.5:
                (h, w) = processed_img.shape[:2]
                center = (w // 2, h // 2)
                M = cv2.getRotationMatrix2D(center, angle, 1.0)
                processed_img = cv2.warpAffine(processed_img, M, (w, h), 
                                            flags=cv2.INTER_CUBIC, 
                                            borderMode=cv2.BORDER_REPLICATE)
                logging.info(f'应用去倾斜，角度: {angle:.2f}度')
                cv2.imwrite(os.path.join(output_dir, f'{step:02d}_deskewed.png'), processed_img)
                logging.info(f"已保存: {step:02d}_deskewed.png")
            else:
                logging.info(f"图像倾斜角度 ({angle:.2f}度) 小于阈值，未执行去倾斜。")
        except Exception as e:
            logging.warning(f"去倾斜失败: {e}")
    
    # 9. 边缘增强
    step += 1
    if config.getboolean('IMAGE_PROCESSING', 'edge_enhance', fallback=False):
        if len(processed_img.shape) == 2: # 必须是单通道图
            edges = cv2.Canny(processed_img, 100, 200)
            processed_img = cv2.addWeighted(processed_img, 0.8, edges, 0.2, 0)
            cv2.imwrite(os.path.join(output_dir, f'{step:02d}_edge_enhanced.png'), processed_img)
            logging.info(f"已保存: {step:02d}_edge_enhanced.png")
        else:
            logging.warning("边缘增强需要单通道灰度图，已跳过。")

    # 10. 最终用于OCR的图像
    step += 1
    final_img = processed_img
    if len(final_img.shape) == 2:
        final_img = cv2.cvtColor(final_img, cv2.COLOR_GRAY2BGR)

    cv2.imwrite(os.path.join(output_dir, f'{step:02d}_final_for_ocr.png'), final_img)
    logging.info(f"已保存: {step:02d}_final_for_ocr.png")

    print("\n" + "="*50)
    logging.info(f"所有处理步骤已完成，请查看 '{os.path.abspath(output_dir)}' 文件夹中的图片。")
    print("="*50 + "\n")


def main():
    """
    主函数，用于解析命令行参数和启动可视化流程。
    """
    parser = argparse.ArgumentParser(
        description="图像预处理步骤可视化工具。该工具会读取一张图片，并根据config.ini的设置，将每一步预处理的结果保存为单独的图片文件。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        "image_filename", 
        type=str, 
        help="要处理的图片文件名。\n"
             "例如: page_1.png\n"
             "脚本会在 ../temp_images/ 目录中查找此文件。"
    )
    parser.add_argument(
        "--config",
        type=str,
        default="config.ini",
        help="配置文件路径 (默认为: config.ini)"
    )
    parser.add_argument(
        "--output",
        type=str,
        default="preprocessing_steps",
        help="保存处理后图片的目录名 (默认为: preprocessing_steps)"
    )
    args = parser.parse_args()
    
    # 构建输入图片文件的完整路径
    # 脚本位于 'OCR-PDF内容抽取整理'，图片位于 'temp_images'
    # 因此，正确的相对路径是 '../temp_images/'
    base_dir = os.path.dirname(os.path.abspath(__file__))
    image_path = os.path.join(base_dir, '..', 'temp_images', args.image_filename)

    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        logging.error(f"配置文件未找到: {args.config}")
        return

    # 读取配置
    config = configparser.ConfigParser()
    config.read(args.config, encoding='utf-8')
    
    # 运行可视化函数
    visualize_preprocessing(image_path, config, args.output)

if __name__ == '__main__':


    image_path = '/OCR-图片整理进文件夹/temp_images\SCAN0021.PDF_p1_img1.png'


    config = ConfigParser()

    config.read('D:\wyh\Code\daily_debug\Medical_Helper\OCR-PDF内容抽取整理\config.ini', encoding='utf-8')

    # 运行可视化函数
    visualize_preprocessing(image_path, config, 'preprocessing_steps')