#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import fitz
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_echo_keywords_in_pdfs():
    """检查所有PDF中的心超关键词"""
    input_path = '../files/7.28体检'
    
    if not os.path.exists(input_path):
        logger.error(f'输入目录不存在: {input_path}')
        return
    
    # 心超关键词
    echo_keywords = ['心超', '心脏超声', '超声心动图', '超声所见', '超声提示', '心脏彩超', '心脏B超', '超声检查']
    
    pdf_files = [f for f in os.listdir(input_path) if f.lower().endswith('.pdf')]
    logger.info(f'找到 {len(pdf_files)} 个PDF文件')
    
    echo_pdfs = []
    
    for pdf_file in pdf_files:
        pdf_path = os.path.join(input_path, pdf_file)
        
        try:
            doc = fitz.open(pdf_path)
            
            # 提取所有页面的文字内容
            all_text = ""
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()
                all_text += page_text + "\n"
            
            # 检查心超关键词
            found_keywords = [kw for kw in echo_keywords if kw in all_text]
            
            if found_keywords:
                echo_pdfs.append({
                    'file': pdf_file,
                    'keywords': found_keywords,
                    'text_length': len(all_text)
                })
                logger.info(f'✅ {pdf_file}: 发现关键词 {found_keywords}')
            
            doc.close()
            
        except Exception as e:
            logger.error(f'❌ 处理 {pdf_file} 时出错: {e}')
    
    print("\n" + "=" * 80)
    print("心超关键词检查结果")
    print("=" * 80)
    print(f"总PDF文件数: {len(pdf_files)}")
    print(f"包含心超关键词的PDF数: {len(echo_pdfs)}")
    
    if echo_pdfs:
        print(f"\n包含心超关键词的PDF文件:")
        for i, pdf_info in enumerate(echo_pdfs, 1):
            print(f"  {i}. {pdf_info['file']}")
            print(f"     关键词: {pdf_info['keywords']}")
            print(f"     文本长度: {pdf_info['text_length']} 字符")
    else:
        print("\n❌ 没有找到包含心超关键词的PDF文件")
    
    print("=" * 80)

if __name__ == "__main__":
    check_echo_keywords_in_pdfs()
