#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗表单字段提取系统 - 主程序
完整的处理流程：PDF解析 -> 图像对齐 -> 字段标定 -> 数据提取 -> 结果输出
"""

import os
import sys
import configparser
import logging
import json
import glob
from pathlib import Path
from typing import List, Dict, Any, Tuple
import subprocess

# 导入核心模块
import fitz  # PyMuPDF
from image_alignment import ImageAligner
from field_extractor import FieldExtractor
from excel_handler import ExcelHandler
from datetime import datetime

class SimplePDFProcessor:
    """简化的PDF处理器"""

    def convert_pdf_to_images(self, pdf_path: str, output_dir: str, dpi: int = 300) -> List[str]:
        """
        将PDF转换为图像

        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录
            dpi: 分辨率

        Returns:
            生成的图像文件路径列表
        """
        image_files = []

        try:
            # 打开PDF文件
            pdf_document = fitz.open(pdf_path)

            # 获取文件名（不含扩展名）
            base_name = os.path.splitext(os.path.basename(pdf_path))[0]

            # 转换每一页
            for page_num in range(len(pdf_document)):
                page = pdf_document.load_page(page_num)

                # 设置缩放比例以达到指定DPI
                zoom = dpi / 72.0  # PDF默认72 DPI
                mat = fitz.Matrix(zoom, zoom)

                # 渲染页面为图像
                pix = page.get_pixmap(matrix=mat)

                # 生成输出文件名
                image_filename = f"{base_name}_p{page_num + 1}_img1.png"
                image_path = os.path.join(output_dir, image_filename)

                # 保存图像
                pix.save(image_path)
                image_files.append(image_path)

                pix = None  # 释放内存

            pdf_document.close()

        except Exception as e:
            raise Exception(f"PDF转换失败: {str(e)}")

        return image_files

class SimpleExcelHandler:
    """简化的Excel处理器"""

    def __init__(self, config=None):
        """初始化Excel处理器"""
        self.config = config

    def create_excel_from_extractions(self, extraction_results: List[Dict], output_file: str):
        """
        从提取结果创建Excel文件，支持对齐失败图像的超链接

        Args:
            extraction_results: 提取结果列表
            output_file: 输出Excel文件路径
        """
        try:
            import openpyxl
            from datetime import datetime

            # 创建工作簿
            workbook = openpyxl.Workbook()
            worksheet = workbook.active

            # 表名添加时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            worksheet.title = f"医疗表单数据_{timestamp}"

            # 定义表头
            headers = [
                "文件名", "图像质量", "对齐状态", "原图链接", "姓名", "手机号码", "出生日期", "性别", "血压", "心率",
                "体重", "身高", "临床症状", "临床症状具体",
                "吸烟", "饮酒", "心肌梗死病史", "卒中史", "高血压病史",
                "血脂异常病史", "糖尿病", "外周血管病",
                "抗血小板药物", "阿司匹林", "氯吡格雷", "替格瑞洛",
                "他汀类", "阿托伐他汀", "瑞舒伐他汀", "他汀类其他",
                "降压药物", "降糖药物", "胰岛素",
                "验证警告", "处理状态"
            ]

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col, value=header)
                cell.font = openpyxl.styles.Font(bold=True)
                cell.fill = openpyxl.styles.PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            # 写入数据
            for row_idx, result in enumerate(extraction_results, 2):
                filename = os.path.basename(result.get("image_path", ""))
                extracted_fields = result.get("extracted_fields", {})
                combined_fields = result.get("combined_fields", {})
                image_quality = result.get("image_quality", {})

                # 检查是否为对齐失败的记录
                is_alignment_failed = result.get("alignment_failed", False)

                # 图像质量状态
                quality_status = "正常"
                if is_alignment_failed:
                    quality_status = "对齐失败"
                elif image_quality.get("should_highlight_row", False):
                    quality_status = "质量警告"

                # 对齐状态
                alignment_status = "失败" if is_alignment_failed else "成功"

                # 基本信息 - 使用组合字段优先
                birth_date = combined_fields.get("出生日期", {}).get("value", "")
                weight = combined_fields.get("体重", {}).get("value", "")
                height = combined_fields.get("身高", {}).get("value", "")
                blood_pressure = combined_fields.get("血压", {}).get("value", "")

                row_data = [
                    filename,  # 这个会被设置为超链接
                    quality_status,
                    alignment_status,
                    "查看文件名",  # 这个会被设置为原图超链接
                    extracted_fields.get("姓名", {}).get("text", ""),
                    extracted_fields.get("手机号码", {}).get("text", ""),
                    birth_date,
                    self._get_gender(extracted_fields),
                    blood_pressure,
                    extracted_fields.get("心率", {}).get("text", ""),
                    weight,
                    height,
                    self._get_clinical_symptoms(extracted_fields),
                    extracted_fields.get("临床症状具体", {}).get("text", ""),
                ]

                # 复选框处理 - 从实际提取结果获取
                risk_factors = [
                    self._get_checkbox_value(extracted_fields, "吸烟"),
                    self._get_checkbox_value(extracted_fields, "饮酒"),
                    self._get_checkbox_value(extracted_fields, "心肌梗死病史"),
                    self._get_checkbox_value(extracted_fields, "卒中史"),
                    self._get_checkbox_value(extracted_fields, "高血压病史"),
                    self._get_checkbox_value(extracted_fields, "血脂异常病史"),
                    self._get_checkbox_value(extracted_fields, "糖尿病"),
                    self._get_checkbox_value(extracted_fields, "外周血管病")
                ]

                medications = [
                    self._get_checkbox_value(extracted_fields, "抗血小板药物"),
                    self._get_checkbox_value(extracted_fields, "阿司匹林"),
                    self._get_checkbox_value(extracted_fields, "氯吡格雷"),
                    self._get_checkbox_value(extracted_fields, "替格瑞洛"),
                    self._get_checkbox_value(extracted_fields, "他汀类"),
                    self._get_checkbox_value(extracted_fields, "阿托伐他汀"),
                    self._get_checkbox_value(extracted_fields, "瑞舒伐他汀"),
                    self._get_checkbox_value(extracted_fields, "他汀类其他"),
                    self._get_checkbox_value(extracted_fields, "降压药物"),
                    self._get_checkbox_value(extracted_fields, "降糖药物"),
                    self._get_checkbox_value(extracted_fields, "胰岛素")
                ]

                # 收集验证警告
                validation_warnings = []
                for field_name, field_info in combined_fields.items():
                    warnings = field_info.get("validation", {}).get("warnings", [])
                    if warnings:
                        validation_warnings.extend([f"{field_name}: {w}" for w in warnings])

                warning_text = "; ".join(validation_warnings) if validation_warnings else ""
                processing_status = "完成"

                # 合并所有数据
                full_row_data = row_data + risk_factors + medications + [warning_text, processing_status]

                # 写入行数据
                for col, value in enumerate(full_row_data, 1):
                    cell = worksheet.cell(row=row_idx, column=col, value=value)

                    # 第一列（文件名）设置为超链接
                    if col == 1 and value:  # 文件名列
                        self._set_image_hyperlink(cell, result, output_file)

                    # 第四列（原图链接）设置为原图超链接
                    elif col == 4 and value and is_alignment_failed:  # 原图链接列，仅对对齐失败的记录
                        self._set_image_hyperlink(cell, result, output_file)

                    # 根据数据质量设置样式
                    if is_alignment_failed:
                        # 对齐失败：橙色背景
                        cell.fill = openpyxl.styles.PatternFill(start_color="FFE4B5", end_color="FFE4B5", fill_type="solid")
                        cell.font = openpyxl.styles.Font(color="FF8C00", bold=True)
                    elif quality_status == "质量警告" or validation_warnings:
                        # 质量警告：浅红色背景
                        cell.fill = openpyxl.styles.PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")

            # 调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 4, 50)  # 增加列宽，最大50
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # 保存文件
            workbook.save(output_file)

            # 如果启用了ROI图片嵌入，则嵌入图片
            if self.config.getboolean('IMAGE_ALIGNMENT', 'embed_roi_in_excel', fallback=False):
                self.embed_roi_images_in_excel(output_file, extraction_results)

        except Exception as e:
            raise Exception(f"创建Excel文件失败: {str(e)}")

    def embed_roi_images_in_excel(self, excel_file: str, extraction_results: List[Dict]):
        """
        在Excel中嵌入ROI图片

        Args:
            excel_file: Excel文件路径
            extraction_results: 提取结果列表
        """
        try:
            import openpyxl
            from openpyxl.drawing import image
            import os

            # 需要嵌入图片的字段
            roi_fields = ["姓名", "手机号码", "出生日期", "血压", "心率", "体重", "身高", "临床症状具体"]

            # 打开已保存的Excel文件
            workbook = openpyxl.load_workbook(excel_file)
            worksheet = workbook.active

            # 添加ROI图片列
            roi_start_col = len(worksheet[1]) + 1  # 在现有列后添加

            # 添加ROI图片列标题
            for i, field_name in enumerate(roi_fields):
                col_letter = openpyxl.utils.get_column_letter(roi_start_col + i)
                worksheet[f"{col_letter}1"] = f"{field_name}_ROI图片"
                worksheet.column_dimensions[col_letter].width = 20  # 设置列宽

            # 为每行数据添加ROI图片
            for row_idx, result in enumerate(extraction_results, start=2):
                extracted_fields = result.get("extracted_fields", {})

                for col_idx, field_name in enumerate(roi_fields):
                    col_letter = openpyxl.utils.get_column_letter(roi_start_col + col_idx)
                    cell = worksheet[f"{col_letter}{row_idx}"]

                    # 查找对应的ROI图片
                    roi_image_path = self._find_roi_image(field_name, result.get("image_path", ""))

                    if roi_image_path and os.path.exists(roi_image_path):
                        try:
                            # 调整图片大小并插入
                            img = image.Image(roi_image_path)

                            # 设置图片大小（像素）
                            img.width = 150
                            img.height = 100

                            # 设置图片位置
                            img.anchor = cell.coordinate

                            # 添加图片到工作表
                            worksheet.add_image(img)

                            # 调整行高以适应图片
                            worksheet.row_dimensions[row_idx].height = 75

                        except Exception as e:
                            print(f"警告: 插入ROI图片失败 {roi_image_path}: {str(e)}")
                            cell.value = "图片加载失败"
                    else:
                        cell.value = "无图片"

            # 保存修改后的Excel文件
            workbook.save(excel_file)
            print(f"信息: ROI图片已嵌入Excel文件: {excel_file}")

        except Exception as e:
            print(f"错误: 嵌入ROI图片失败: {str(e)}")

    def _find_roi_image(self, field_name: str, image_path: str) -> str:
        """
        查找对应的ROI图片文件（支持唯一标识符）

        Args:
            field_name: 字段名称
            image_path: 原始图片路径

        Returns:
            ROI图片路径，如果不存在返回None
        """
        try:
            roi_dir = "roi_images"
            if not os.path.exists(roi_dir):
                return None

            # 获取原始图片的基础名称
            base_image_name = os.path.splitext(os.path.basename(image_path))[0] if image_path else "current_image"

            # 根据字段类型确定文件名模式
            if field_name in ["姓名", "临床症状具体"]:
                field_type = "text"
            elif field_name in ["出生日期", "血压", "心率", "体重", "身高", "手机号码"]:
                field_type = "digit_large_box"
            else:
                field_type = "text"

            # 获取ROI目录中的所有文件
            roi_files = [f for f in os.listdir(roi_dir) if f.endswith('.png')]

            # 查找匹配的ROI图片（支持唯一标识符）
            # 优先级：完全匹配 > 包含基础名称的匹配 > 包含字段名的匹配

            # 1. 查找包含基础图片名称、字段名和字段类型的文件
            for filename in roi_files:
                if (base_image_name in filename and
                    field_name in filename and
                    field_type in filename):
                    return os.path.join(roi_dir, filename)

            # 2. 查找包含字段名和字段类型的文件（向后兼容）
            for filename in roi_files:
                if field_name in filename and field_type in filename:
                    return os.path.join(roi_dir, filename)

            # 3. 最后尝试传统的精确匹配
            possible_names = [
                f"{base_image_name}_{field_name}_{field_type}.png",
                f"current_image_{field_name}_{field_type}.png",
                f"{field_name}_{field_type}.png"
            ]

            for filename in possible_names:
                filepath = os.path.join(roi_dir, filename)
                if os.path.exists(filepath):
                    return filepath

            return None

        except Exception as e:
            print(f"警告: 查找ROI图片失败: {str(e)}")
            return None

    def _set_image_hyperlink(self, cell, result: Dict, output_file: str):
        """为单元格设置图像超链接"""
        try:
            import openpyxl

            image_path = result.get("image_path", "")
            if image_path and os.path.exists(image_path):
                # 计算相对于Excel文件的相对路径
                excel_dir = os.path.dirname(os.path.abspath(output_file))
                image_abs_path = os.path.abspath(image_path)
                try:
                    rel_path = os.path.relpath(image_abs_path, excel_dir)
                    # 使用相对路径创建超链接
                    cell.hyperlink = rel_path.replace(os.sep, '/')
                    cell.style = "Hyperlink"
                except ValueError:
                    # 如果无法创建相对路径，使用绝对路径
                    cell.hyperlink = f"file:///{image_abs_path.replace(os.sep, '/')}"
                    cell.style = "Hyperlink"
        except Exception as e:
            print(f"设置超链接失败: {str(e)}")

    def _get_gender(self, extracted_fields: Dict) -> str:
        """获取性别信息"""
        if extracted_fields.get("性别男", {}).get("checked", False):
            return "男"
        elif extracted_fields.get("性别女", {}).get("checked", False):
            return "女"
        return ""

    def _get_clinical_symptoms(self, extracted_fields: Dict) -> str:
        """获取临床症状信息"""
        if extracted_fields.get("临床症状无", {}).get("checked", False):
            return "无"
        elif extracted_fields.get("临床症状有", {}).get("checked", False):
            return "有"
        return ""

    def _get_checkbox_value(self, extracted_fields: Dict, field_prefix: str) -> str:
        """
        获取复选框组的值

        Args:
            extracted_fields: 提取的字段数据
            field_prefix: 字段前缀（如"吸烟"、"饮酒"等）

        Returns:
            复选框的值（"是"、"否"、具体选项或空字符串）
        """
        # 常见的复选框模式
        patterns = [
            f"{field_prefix}否",
            f"{field_prefix}是",
            f"{field_prefix}当前{field_prefix}",
            f"{field_prefix}已戒{field_prefix}",
            f"{field_prefix}已戒烟",
            f"{field_prefix}已戒酒"
        ]

        # 特殊处理某些字段
        if field_prefix == "卒中史":
            patterns.extend([f"{field_prefix}脑梗死", f"{field_prefix}脑出血", f"{field_prefix}不详"])
        elif field_prefix in ["阿司匹林", "氯吡格雷", "替格瑞洛", "阿托伐他汀", "瑞舒伐他汀"]:
            # 药物名称字段直接检查
            if extracted_fields.get(field_prefix, {}).get("checked", False):
                return "是"
        elif field_prefix == "他汀类其他":
            if extracted_fields.get(field_prefix, {}).get("checked", False):
                return "是"

        # 检查所有可能的字段名
        for pattern in patterns:
            field_data = extracted_fields.get(pattern, {})
            if field_data.get("checked", False):
                if "否" in pattern:
                    return "否"
                elif "是" in pattern:
                    return "是"
                elif "当前" in pattern:
                    return "是"
                elif "已戒" in pattern:
                    return "已戒"
                elif "脑梗死" in pattern:
                    return "脑梗死"
                elif "脑出血" in pattern:
                    return "脑出血"
                elif "不详" in pattern:
                    return "不详"
                else:
                    return "是"

        return ""

class MedicalFormProcessor:
    """医疗表单处理器 - 主控制类"""
    
    def __init__(self, config_file: str = "config_new.ini"):
        """
        初始化处理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.logger = None
        
        # 加载配置
        self._load_config()
        
        # 设置日志
        self._setup_logging()
        
        # 初始化核心组件
        self._initialize_components()
        
        # 处理统计
        self.processing_stats = {
            "total_pdfs": 0,
            "total_images": 0,
            "aligned_images": 0,
            "extracted_records": 0,
            "failed_extractions": 0,
            "processing_time": 0
        }
    
    def _load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        self.config.read(self.config_file, encoding='utf-8')
        
        # 创建必要的目录
        directories = [
            self.config.get('PATHS', 'temp_images_dir'),
            self.config.get('PATHS', 'aligned_images_dir'),
            self.config.get('PATHS', 'output_dir'),
            self.config.get('PATHS', 'logs_dir')
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def _setup_logging(self):
        """设置日志系统"""
        log_level = getattr(logging, self.config.get('LOGGING', 'log_level', fallback='INFO'))
        log_file = self.config.get('LOGGING', 'log_file')
        enable_console = self.config.getboolean('LOGGING', 'enable_console_log', fallback=True)
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 创建logger
        self.logger = logging.getLogger('MedicalFormProcessor')
        self.logger.setLevel(log_level)
        
        # 文件处理器
        if log_file:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        # 控制台处理器
        if enable_console:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
    
    def _initialize_components(self):
        """初始化核心组件"""
        try:
            # 简化的PDF处理器（只用于转换）
            self.pdf_processor = SimplePDFProcessor()

            # 图像对齐器
            target_width = self.config.getint('IMAGE_ALIGNMENT', 'target_width', fallback=2480)
            target_height = self.config.getint('IMAGE_ALIGNMENT', 'target_height', fallback=3507)
            self.image_aligner = ImageAligner(target_width, target_height)

            # 字段提取器
            calibration_file = self.config.get('FIELD_CALIBRATION', 'calibration_config_file')
            if os.path.exists(calibration_file):
                # 准备OCR配置
                ocr_config = {
                    'confidence_threshold': self.config.getfloat('OCR_SETTINGS', 'confidence_threshold', fallback=0.1),
                    'use_gpu': self.config.getboolean('OCR_SETTINGS', 'use_gpu', fallback=False),
                    'use_angle_cls': self.config.getboolean('OCR_SETTINGS', 'use_angle_cls', fallback=True),
                    'lang': self.config.get('OCR_SETTINGS', 'language', fallback='ch'),
                    'enable_handwriting_mode': self.config.getboolean('OCR_SETTINGS', 'enable_handwriting_mode', fallback=True),
                    'enable_digit_optimization': self.config.getboolean('OCR_SETTINGS', 'enable_digit_optimization', fallback=True)
                }
                self.field_extractor = FieldExtractor(calibration_file, ocr_config, self.config)
            else:
                self.field_extractor = None

            # Excel处理器 - 使用简化版本
            self.excel_handler = SimpleExcelHandler(self.config)

            self.logger.info("核心组件初始化完成")

        except Exception as e:
            self.logger.error(f"组件初始化失败: {str(e)}")
            raise
    
    def process_pdfs_to_images(self) -> List[str]:
        """
        第一步：处理PDF文件，生成图像
        
        Returns:
            生成的图像文件路径列表
        """
        self.logger.info("开始PDF到图像的转换...")
        
        pdf_input_dir = self.config.get('PATHS', 'pdf_input_dir')
        temp_images_dir = self.config.get('PATHS', 'temp_images_dir')
        process_all = self.config.getboolean('PDF_PROCESSING', 'process_all_pdfs', fallback=True)
        
        image_files = []
        
        if process_all:
            # 处理目录中的所有PDF文件
            pdf_files = []
            for ext in ["*.pdf", "*.PDF"]:
                pattern = os.path.join(pdf_input_dir, ext)
                pdf_files.extend(glob.glob(pattern, recursive=False))

            # 去重（防止同一文件被重复添加）
            pdf_files = list(set(pdf_files))
            
            self.processing_stats["total_pdfs"] = len(pdf_files)
            self.logger.info(f"找到 {len(pdf_files)} 个PDF文件")
            
            for pdf_file in pdf_files:
                try:
                    self.logger.info(f"处理PDF: {os.path.basename(pdf_file)}")
                    images = self.pdf_processor.convert_pdf_to_images(
                        pdf_file, 
                        temp_images_dir,
                        dpi=self.config.getint('PDF_PROCESSING', 'dpi', fallback=300)
                    )
                    image_files.extend(images)
                    self.logger.info(f"生成 {len(images)} 张图像")
                    
                except Exception as e:
                    self.logger.error(f"PDF处理失败 {pdf_file}: {str(e)}")
        else:
            # 处理单个PDF文件（从配置中读取）
            pdf_file = self.config.get('PATHS', 'pdf_input_file', fallback='')
            if pdf_file and os.path.exists(pdf_file):
                try:
                    images = self.pdf_processor.convert_pdf_to_images(
                        pdf_file, 
                        temp_images_dir,
                        dpi=self.config.getint('PDF_PROCESSING', 'dpi', fallback=300)
                    )
                    image_files.extend(images)
                    self.processing_stats["total_pdfs"] = 1
                    
                except Exception as e:
                    self.logger.error(f"PDF处理失败 {pdf_file}: {str(e)}")
        
        self.processing_stats["total_images"] = len(image_files)
        self.logger.info(f"PDF转换完成，共生成 {len(image_files)} 张图像")
        
        return image_files
    
    def align_images(self, image_files: List[str]) -> Tuple[List[str], List[str]]:
        """
        第二步：对齐图像

        Args:
            image_files: 原始图像文件列表

        Returns:
            (对齐后的图像文件列表, 对齐失败的原始图像文件列表)
        """
        self.logger.info("开始图像对齐处理...")

        aligned_images_dir = self.config.get('PATHS', 'aligned_images_dir')
        aligned_files = []
        failed_alignment_files = []  # 新增：跟踪对齐失败的文件

        for image_file in image_files:
            try:
                filename = os.path.basename(image_file)
                name, ext = os.path.splitext(filename)
                aligned_path = os.path.join(aligned_images_dir, f"{name}_aligned{ext}")
                
                self.logger.info(f"对齐图像: {filename}")

                # 检查是否保存调试图像
                save_debug = self.config.getboolean('IMAGE_ALIGNMENT', 'save_debug_images', fallback=False)

                aligned_image, align_info = self.image_aligner.align_image(
                    image_file,
                    aligned_path,
                    debug=True,
                    save_debug_image=save_debug
                )
                
                if aligned_image is not None:
                    aligned_files.append(aligned_path)
                    self.processing_stats["aligned_images"] += 1

                    if "warning" in align_info:
                        self.logger.warning(f"图像 {filename}: {align_info.get('warning', '')}")
                    else:
                        self.logger.info(f"图像 {filename}: 对齐成功")
                else:
                    # 对齐失败，将原始图像添加到失败列表中
                    failed_alignment_files.append(image_file)
                    self.processing_stats["failed_alignments"] = self.processing_stats.get("failed_alignments", 0) + 1
                    self.logger.error(f"图像 {filename}: 对齐失败，将使用原图进行人工校验")
                    
            except Exception as e:
                self.logger.error(f"图像对齐异常 {image_file}: {str(e)}")
        
        self.logger.info(f"图像对齐完成，成功对齐 {len(aligned_files)} 张图像，对齐失败 {len(failed_alignment_files)} 张图像")
        return aligned_files, failed_alignment_files
    
    def check_calibration_config(self) -> bool:
        """
        第三步：检查标定配置
        
        Returns:
            标定配置是否可用
        """
        calibration_file = self.config.get('FIELD_CALIBRATION', 'calibration_config_file')
        
        if os.path.exists(calibration_file):
            self.logger.info(f"找到标定配置文件: {calibration_file}")
            return True
        else:
            self.logger.warning(f"标定配置文件不存在: {calibration_file}")
            
            auto_launch = self.config.getboolean('FIELD_CALIBRATION', 'auto_launch_calibrator', fallback=True)
            
            if auto_launch:
                self.logger.info("启动交互式标定工具...")
                print("\n" + "="*60)
                print("🔧 标定配置文件不存在，需要进行字段标定")
                print("📋 请使用交互式标定工具完成字段标定：")
                print("   python interactive_calibrator.py")
                print("📄 标定完成后，请将配置文件保存为:")
                print(f"   {calibration_file}")
                print("🔄 然后重新运行主程序")
                print("="*60)
                
                # 尝试启动交互式标定工具
                try:
                    subprocess.run([sys.executable, "interactive_calibrator.py"], check=False)
                except Exception as e:
                    self.logger.error(f"启动交互式标定工具失败: {str(e)}")
            
            return False
    
    def extract_fields(self, aligned_images: List[str], failed_alignment_images: List[str] = None) -> List[Dict[str, Any]]:
        """
        第四步：提取字段数据
        
        Args:
            aligned_images: 对齐后的图像列表
            failed_alignment_images: 对齐失败的原始图像文件列表

        Returns:
            提取结果列表
        """
        if not self.field_extractor:
            self.logger.error("字段提取器未初始化，请检查标定配置")
            return []
        
        self.logger.info("开始字段提取...")
        
        extraction_results = []
        align_before = self.config.getboolean('FIELD_EXTRACTION', 'align_before_extraction', fallback=False)
        
        for image_file in aligned_images:
            try:
                filename = os.path.basename(image_file)
                self.logger.info(f"提取字段: {filename}")
                
                result = self.field_extractor.extract_all_fields(image_file, align_image=align_before)
                
                if "error" not in result:
                    extraction_results.append(result)
                    self.processing_stats["extracted_records"] += 1
                    
                    summary = result.get("summary", {})
                    self.logger.info(f"提取完成: 成功 {summary.get('successful_extractions', 0)}, "
                                   f"失败 {summary.get('failed_extractions', 0)}")
                else:
                    self.logger.error(f"字段提取失败 {filename}: {result['error']}")
                    self.processing_stats["failed_extractions"] += 1
                    
            except Exception as e:
                self.logger.error(f"字段提取异常 {image_file}: {str(e)}")
                self.processing_stats["failed_extractions"] += 1
        
        # 处理对齐失败的图像，为它们创建特殊的记录
        if failed_alignment_images:
            self.logger.info(f"处理 {len(failed_alignment_images)} 个对齐失败的图像...")

            for image_file in failed_alignment_images:
                try:
                    filename = os.path.basename(image_file)
                    self.logger.info(f"创建对齐失败记录: {filename}")

                    # 创建对齐失败的特殊记录
                    failed_record = self._create_failed_alignment_record(image_file)
                    extraction_results.append(failed_record)

                    self.processing_stats["failed_alignment_records"] = self.processing_stats.get("failed_alignment_records", 0) + 1

                except Exception as e:
                    self.logger.error(f"处理对齐失败图像异常 {image_file}: {str(e)}")

        total_records = len(extraction_results)
        successful_ocr = total_records - len(failed_alignment_images) if failed_alignment_images else total_records

        self.logger.info(f"字段提取完成，成功OCR提取 {successful_ocr} 条记录，对齐失败 {len(failed_alignment_images) if failed_alignment_images else 0} 条记录，总计 {total_records} 条记录")
        return extraction_results

    def _create_failed_alignment_record(self, image_file: str) -> Dict[str, Any]:
        """
        为对齐失败的图像创建特殊记录

        Args:
            image_file: 原始图像文件路径

        Returns:
            对齐失败的记录字典
        """
        filename = os.path.basename(image_file)

        # 创建基础记录结构
        failed_record = {
            "image_path": image_file,
            "filename": filename,
            "status": "对齐失败",
            "requires_manual_review": True,
            "alignment_failed": True,
            "extracted_fields": {},
            "summary": {
                "successful_extractions": 0,
                "failed_extractions": 0,
                "total_fields": 0,
                "alignment_status": "失败"
            }
        }

        # 添加所有字段作为空值，但标记为需要人工填写
        if self.field_extractor and hasattr(self.field_extractor, 'calibrated_regions'):
            for field_name in self.field_extractor.calibrated_regions.keys():
                failed_record["extracted_fields"][field_name] = {
                    "text": "",
                    "confidence": 0.0,
                    "is_empty": True,
                    "needs_manual_input": True,
                    "reason": "图像对齐失败，需要人工校验"
                }

        return failed_record

    def generate_outputs(self, extraction_results: List[Dict[str, Any]]):
        """
        第五步：生成输出文件
        
        Args:
            extraction_results: 提取结果列表
        """
        self.logger.info("开始生成输出文件...")
        
        output_dir = self.config.get('PATHS', 'output_dir')
        
        # 生成Excel文件
        if self.config.get('OUTPUT', 'output_format', fallback='xlsx') == 'xlsx':
            # excel_file 添加时间戳
            # excel_file = os.path.join(output_dir, self.config.get('OUTPUT', 'excel_filename'+'_'+datetime.now().strftime("%Y%m%d_%H%M%S")))
            excel_file = os.path.join(output_dir, self.config.get('OUTPUT', 'excel_filename'))

            try:
                self.excel_handler.create_excel_from_extractions(extraction_results, excel_file)
                self.logger.info(f"Excel文件已生成: {excel_file}")
            except Exception as e:
                self.logger.error(f"Excel文件生成失败: {str(e)}")
        
        # 保存JSON结果
        if self.config.getboolean('OUTPUT', 'save_json_results', fallback=True):
            json_file = os.path.join(output_dir, self.config.get('OUTPUT', 'json_filename'))
            try:
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(extraction_results, f, ensure_ascii=False, indent=2)
                self.logger.info(f"JSON结果已保存: {json_file}")
            except Exception as e:
                self.logger.error(f"JSON文件保存失败: {str(e)}")
        
        # 生成文本报告
        if self.config.getboolean('OUTPUT', 'generate_text_report', fallback=True):
            report_file = os.path.join(output_dir, self.config.get('OUTPUT', 'report_filename'))
            try:
                self._generate_text_report(extraction_results, report_file)
                self.logger.info(f"文本报告已生成: {report_file}")
            except Exception as e:
                self.logger.error(f"文本报告生成失败: {str(e)}")
        
        # 保存处理统计
        if self.config.getboolean('LOGGING', 'save_processing_stats', fallback=True):
            stats_file = os.path.join(output_dir, "processing_statistics.json")
            try:
                with open(stats_file, 'w', encoding='utf-8') as f:
                    json.dump(self.processing_stats, f, ensure_ascii=False, indent=2)
                self.logger.info(f"处理统计已保存: {stats_file}")
            except Exception as e:
                self.logger.error(f"处理统计保存失败: {str(e)}")
    
    def _generate_text_report(self, extraction_results: List[Dict[str, Any]], report_file: str):
        """生成文本报告"""
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("医疗表单字段提取处理报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 处理统计
            f.write("处理统计:\n")
            f.write(f"  处理PDF文件: {self.processing_stats['total_pdfs']}\n")
            f.write(f"  生成图像: {self.processing_stats['total_images']}\n")
            f.write(f"  对齐图像: {self.processing_stats['aligned_images']}\n")
            f.write(f"  提取记录: {self.processing_stats['extracted_records']}\n")
            f.write(f"  失败记录: {self.processing_stats['failed_extractions']}\n\n")
            
            # 详细结果
            for i, result in enumerate(extraction_results, 1):
                f.write(f"记录 {i}: {os.path.basename(result.get('image_path', ''))}\n")
                f.write("-" * 30 + "\n")
                
                if "combined_fields" in result:
                    for field_name, field_info in result["combined_fields"].items():
                        value = field_info.get("value", "")
                        warnings = field_info.get("validation", {}).get("warnings", [])
                        status = "⚠️" if warnings else "✅"
                        f.write(f"  {status} {field_name}: {value}\n")
                        if warnings:
                            for warning in warnings:
                                f.write(f"    警告: {warning}\n")
                
                f.write("\n")
    
    def run_complete_process(self):
        """运行完整的处理流程"""
        import time
        start_time = time.time()
        
        try:
            self.logger.info("开始医疗表单处理流程...")
            
            # 第一步：PDF转图像
            image_files = self.process_pdfs_to_images()
            if not image_files:
                self.logger.error("没有生成任何图像文件，处理终止")
                return False
            
            # 第二步：图像对齐
            aligned_images, failed_alignment_images = self.align_images(image_files)
            if not aligned_images and not failed_alignment_images:
                self.logger.error("没有任何图像可以处理，处理终止")
                return False
            
            # 第三步：检查标定配置
            if not self.check_calibration_config():
                self.logger.error("标定配置不可用，处理终止")
                return False
            
            # 重新初始化字段提取器（如果标定配置刚刚创建）
            if not self.field_extractor:
                calibration_file = self.config.get('FIELD_CALIBRATION', 'calibration_config_file')
                if os.path.exists(calibration_file):
                    ocr_config = {
                        'confidence_threshold': 0.1,
                        'use_gpu': False,
                        'use_angle_cls': True,
                        'lang': 'ch'
                    }
                    self.field_extractor = FieldExtractor(calibration_file, ocr_config, self.config)
                else:
                    self.logger.error("标定配置仍然不可用")
                    return False
            
            # 第四步：字段提取
            extraction_results = self.extract_fields(aligned_images, failed_alignment_images)
            if not extraction_results:
                self.logger.error("没有成功提取的数据，处理终止")
                return False
            
            # 第五步：生成输出
            self.generate_outputs(extraction_results)
            
            # 计算处理时间
            end_time = time.time()
            self.processing_stats["processing_time"] = end_time - start_time
            
            self.logger.info(f"处理流程完成！耗时: {self.processing_stats['processing_time']:.2f}秒")
            
            # 显示最终统计
            print("\n" + "="*60)
            print("🎉 医疗表单处理完成！")
            print("="*60)
            print(f"📊 处理统计:")
            print(f"  • PDF文件: {self.processing_stats['total_pdfs']}")
            print(f"  • 生成图像: {self.processing_stats['total_images']}")
            print(f"  • 对齐图像: {self.processing_stats['aligned_images']}")
            print(f"  • 提取记录: {self.processing_stats['extracted_records']}")
            print(f"  • 失败记录: {self.processing_stats['failed_extractions']}")
            print(f"  • 处理时间: {self.processing_stats['processing_time']:.2f}秒")
            print(f"📁 输出目录: {self.config.get('PATHS', 'output_dir')}")
            print("="*60)
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理流程异常: {str(e)}")
            return False


def main():
    """主函数"""
    print("🏥 医疗表单字段提取系统")
    print("=" * 50)
    
    try:
        # 检查配置文件
        config_file = "config_new.ini"
        if not os.path.exists(config_file):
            print(f"❌ 配置文件不存在: {config_file}")
            print("请确保配置文件存在并正确配置")
            return
        
        # 创建处理器并运行
        processor = MedicalFormProcessor(config_file)
        success = processor.run_complete_process()
        
        if success:
            print("\n✅ 处理成功完成！")
        else:
            print("\n❌ 处理过程中出现错误，请查看日志文件")
            
    except Exception as e:
        print(f"\n💥 程序异常: {str(e)}")
        print("请检查配置文件和依赖环境")


if __name__ == "__main__":
    main()
