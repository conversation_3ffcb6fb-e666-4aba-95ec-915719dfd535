#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import fitz
import logging
from configparser import ConfigParser

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from echo_ocr_engine import EchoOCREngine

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_echo_integration():
    """调试心超OCR集成问题"""
    
    # 读取配置
    config = ConfigParser()
    config.read('config.ini', encoding='utf-8')
    
    # 初始化心超OCR引擎
    logger.info('初始化心超OCR引擎...')
    echo_ocr_engine = EchoOCREngine(config)
    
    # 测试弄庆新PDF（已知包含心超内容）
    test_pdf = '../files/7.28体检/202412130173_弄庆新_45250119800812256X.pdf'
    
    if not os.path.exists(test_pdf):
        logger.error(f'测试PDF不存在: {test_pdf}')
        return
    
    logger.info(f'开始调试PDF: {test_pdf}')
    
    try:
        doc = fitz.open(test_pdf)
        
        # 提取所有文本
        all_text = ""
        for page_num in range(len(doc)):
            page = doc[page_num]
            page_text = page.get_text()
            all_text += page_text + "\n"
        
        logger.info(f'PDF总页数: {len(doc)}')
        logger.info(f'提取文本长度: {len(all_text)}字符')
        
        # 检查心超关键词
        echo_keywords = ['心超', '心脏超声', '超声心动图', '超声所见', '超声提示', '心脏彩超', '心脏B超', '超声检查']
        found_echo_keywords = [kw for kw in echo_keywords if kw in all_text]
        
        logger.info(f'发现的心超关键词: {found_echo_keywords}')
        
        if not found_echo_keywords:
            logger.warning('未发现心超关键词，退出测试')
            return
        
        # 创建临时图片目录
        temp_dir = './temp_debug_images'
        os.makedirs(temp_dir, exist_ok=True)
        
        echo_images_found = 0
        
        # 从后往前扫描页面
        for page_num in range(len(doc) - 1, -1, -1):
            page = doc[page_num]
            image_list = page.get_images()
            
            logger.info(f'第{page_num+1}页包含{len(image_list)}张图片')
            
            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)
                    
                    if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图
                        temp_img_path = os.path.join(temp_dir, f'page_{page_num+1}_img_{img_index}.png')
                        
                        # 保存图片
                        if pix.alpha:
                            pix = fitz.Pixmap(fitz.csRGB, pix)
                        
                        img_data = pix.tobytes("png")
                        with open(temp_img_path, "wb") as f:
                            f.write(img_data)
                        
                        logger.info(f'保存图片: {temp_img_path}')
                        
                        # 检查是否是心超图片
                        logger.info(f'检查图片是否为心超图片...')
                        is_echo = echo_ocr_engine.quick_check_echo_image(temp_img_path)
                        logger.info(f'心超图片检查结果: {is_echo}')
                        
                        if is_echo:
                            echo_images_found += 1
                            logger.info(f'发现心超图片: {temp_img_path}')
                            
                            # 提取心超信息
                            logger.info(f'开始提取心超信息...')
                            echo_info = echo_ocr_engine.extract_echo_info(temp_img_path)
                            
                            if echo_info:
                                logger.info(f'成功提取心超信息:')
                                for key, value in echo_info.items():
                                    logger.info(f'  {key}: {value[:100]}...' if len(value) > 100 else f'  {key}: {value}')
                            else:
                                logger.warning(f'心超图片未提取到有效信息')
                            
                            # 只处理第一张心超图片
                            if echo_images_found >= 1:
                                logger.info('已找到心超图片，停止搜索')
                                break
                    
                    pix = None
                    
                except Exception as e:
                    logger.error(f'处理图片出错: {str(e)}')
                    import traceback
                    traceback.print_exc()
            
            # 如果已找到心超图片，退出页面循环
            if echo_images_found > 0:
                break
        
        doc.close()
        
        logger.info(f'调试完成: 发现{echo_images_found}张心超图片')
        
        # 清理临时文件
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            logger.info('清理临时文件完成')
        
    except Exception as e:
        logger.error(f'调试过程出错: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_echo_integration()
