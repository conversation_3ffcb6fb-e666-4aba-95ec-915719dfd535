#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试对齐失败图像处理功能
验证对齐失败的图像能否正确添加到Excel中并设置超链接
"""

import os
import json
import shutil
from main_new import MedicalFormProcessor
import configparser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_environment():
    """创建测试环境"""
    print("🔧 创建测试环境...")
    
    # 确保必要的目录存在
    test_dirs = [
        "temp_images",
        "aligned_images", 
        "output",
        "roi_images"
    ]
    
    for dir_name in test_dirs:
        os.makedirs(dir_name, exist_ok=True)
        print(f"✅ 创建目录: {dir_name}")
    
    return True

def simulate_alignment_failure():
    """模拟对齐失败的情况"""
    print("\n🎭 模拟对齐失败情况...")
    
    # 创建一个模拟的图像对齐器，强制返回失败
    class MockImageAligner:
        def align_image(self, image_path, output_path=None, debug=False, save_debug_image=False):
            """模拟对齐失败"""
            filename = os.path.basename(image_path)
            
            # 模拟某些图像对齐失败
            if "fail" in filename.lower() or "test" in filename.lower():
                print(f"  ❌ 模拟对齐失败: {filename}")
                return None, {"error": "模拟对齐失败"}
            else:
                print(f"  ✅ 模拟对齐成功: {filename}")
                # 复制原图作为对齐后的图像
                if output_path:
                    shutil.copy2(image_path, output_path)
                return True, {"success": True}
    
    return MockImageAligner()

def create_test_images():
    """创建测试图像"""
    print("\n📸 创建测试图像...")
    
    import cv2
    import numpy as np
    
    # 创建测试图像
    test_images = [
        ("test_success.png", "对齐成功的图像"),
        ("test_fail_1.png", "对齐失败的图像1"),
        ("test_fail_2.png", "对齐失败的图像2")
    ]
    
    created_files = []
    
    for filename, description in test_images:
        filepath = os.path.join("temp_images", filename)
        
        # 创建简单的测试图像
        image = np.ones((600, 800, 3), dtype=np.uint8) * 255
        
        # 添加文本标识
        cv2.putText(image, description, (50, 300), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(image, filename, (50, 350), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # 保存图像
        cv2.imwrite(filepath, image)
        created_files.append(filepath)
        print(f"  ✅ 创建测试图像: {filename}")
    
    return created_files

def test_alignment_failure_processing():
    """测试对齐失败处理"""
    print("\n🧪 测试对齐失败处理...")
    
    try:
        # 创建测试环境
        create_test_environment()
        
        # 创建测试图像
        test_images = create_test_images()
        
        # 创建处理器（传递配置文件路径）
        processor = MedicalFormProcessor('config_new.ini')
        
        # 替换图像对齐器为模拟版本
        processor.image_aligner = simulate_alignment_failure()
        
        # 测试对齐过程
        print("\n🔄 测试图像对齐...")
        aligned_images, failed_alignment_images = processor.align_images(test_images)
        
        print(f"✅ 对齐成功: {len(aligned_images)} 个图像")
        print(f"❌ 对齐失败: {len(failed_alignment_images)} 个图像")
        
        # 验证结果
        if len(failed_alignment_images) > 0:
            print("🎯 对齐失败图像列表:")
            for img in failed_alignment_images:
                print(f"  - {os.path.basename(img)}")
        
        # 测试字段提取（模拟）
        print("\n📝 测试字段提取...")
        
        # 创建模拟的提取结果
        mock_extraction_results = []
        
        # 为对齐成功的图像创建正常记录
        for img in aligned_images:
            mock_extraction_results.append({
                "image_path": img,
                "filename": os.path.basename(img),
                "alignment_failed": False,
                "extracted_fields": {
                    "姓名": {"text": "测试姓名", "confidence": 0.9},
                    "手机号码": {"text": "13800138000", "confidence": 0.8}
                },
                "combined_fields": {},
                "image_quality": {}
            })
        
        # 为对齐失败的图像创建失败记录
        for img in failed_alignment_images:
            failed_record = processor._create_failed_alignment_record(img)
            mock_extraction_results.append(failed_record)
        
        print(f"✅ 创建了 {len(mock_extraction_results)} 条提取记录")
        
        # 测试Excel生成
        print("\n📊 测试Excel生成...")
        output_file = os.path.join("output", "test_alignment_failure.xlsx")
        
        try:
            processor.excel_handler.create_excel_from_extractions(mock_extraction_results, output_file)
            print(f"✅ Excel文件已生成: {output_file}")
            
            # 验证Excel文件
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"📄 Excel文件大小: {file_size} 字节")
                
                # 尝试读取Excel验证内容
                try:
                    import openpyxl
                    wb = openpyxl.load_workbook(output_file)
                    ws = wb.active
                    
                    print(f"📋 Excel工作表: {ws.title}")
                    print(f"📏 数据行数: {ws.max_row}")
                    print(f"📏 数据列数: {ws.max_column}")
                    
                    # 检查表头
                    headers = [cell.value for cell in ws[1]]
                    print(f"📑 表头列数: {len(headers)}")
                    
                    # 检查是否有对齐状态和原图链接列
                    if "对齐状态" in headers and "原图链接" in headers:
                        print("✅ 对齐状态和原图链接列已添加")
                    else:
                        print("❌ 缺少对齐状态或原图链接列")
                    
                    # 检查数据行
                    for row_idx in range(2, ws.max_row + 1):
                        row_data = [cell.value for cell in ws[row_idx]]
                        alignment_status = row_data[2] if len(row_data) > 2 else ""  # 对齐状态列
                        
                        if alignment_status == "失败":
                            print(f"🔍 发现对齐失败记录: 行 {row_idx}")
                            
                            # 检查超链接
                            original_link_cell = ws.cell(row=row_idx, column=4)  # 原图链接列
                            if original_link_cell.hyperlink:
                                print(f"  ✅ 原图超链接已设置: {original_link_cell.hyperlink}")
                            else:
                                print(f"  ❌ 原图超链接未设置")
                    
                    wb.close()
                    
                except Exception as e:
                    print(f"⚠️  Excel验证失败: {str(e)}")
            
        except Exception as e:
            print(f"❌ Excel生成失败: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_failed_record_creation():
    """测试对齐失败记录创建"""
    print("\n🔧 测试对齐失败记录创建...")
    
    try:
        processor = MedicalFormProcessor('config_new.ini')
        
        # 创建测试图像路径
        test_image_path = "temp_images/test_fail.png"
        
        # 创建对齐失败记录
        failed_record = processor._create_failed_alignment_record(test_image_path)
        
        print("✅ 对齐失败记录创建成功")
        print(f"📄 记录结构:")
        print(f"  - 图像路径: {failed_record.get('image_path')}")
        print(f"  - 文件名: {failed_record.get('filename')}")
        print(f"  - 状态: {failed_record.get('status')}")
        print(f"  - 需要人工校验: {failed_record.get('requires_manual_review')}")
        print(f"  - 对齐失败: {failed_record.get('alignment_failed')}")
        print(f"  - 提取字段数: {len(failed_record.get('extracted_fields', {}))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 对齐失败图像处理功能测试")
    print("="*80)
    
    # 运行测试
    tests = [
        ("对齐失败记录创建", test_failed_record_creation),
        ("对齐失败处理流程", test_alignment_failure_processing)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 50)
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
                
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results[test_name] = False
    
    # 总结
    print("\n" + "="*80)
    print("📊 测试结果总结")
    print("="*80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！对齐失败处理功能已正确实现")
        print("\n💡 功能说明:")
        print("1. 对齐失败的图像会被添加到Excel中")
        print("2. 对齐失败的行会有特殊的橙色背景样式")
        print("3. 原图链接列提供超链接以便人工校验")
        print("4. 所有字段标记为需要人工输入")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")
    
    print("="*80)

if __name__ == "__main__":
    main()
