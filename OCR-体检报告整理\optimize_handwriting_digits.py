#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手写数字识别优化脚本
分析ROI图片，测试不同的OCR配置，找到最佳参数
"""

import os
import cv2
import numpy as np
from paddleocr import PaddleOCR
import json
import logging
from typing import Dict, List, Tuple, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HandwritingDigitOptimizer:
    """手写数字识别优化器"""
    
    def __init__(self):
        """初始化优化器"""
        self.roi_dir = "roi_images"
        self.test_results = {}
        
        # 禁用PaddleOCR的调试日志
        import logging as paddle_logging
        paddle_logging.getLogger('ppocr').setLevel(paddle_logging.WARNING)
    
    def load_handwriting_roi_images(self) -> List[Tuple[str, np.ndarray]]:
        """加载手写数字ROI图片"""
        roi_images = []
        
        if not os.path.exists(self.roi_dir):
            logger.error(f"ROI目录不存在: {self.roi_dir}")
            return roi_images
        
        # 查找大框数字ROI图片
        for filename in os.listdir(self.roi_dir):
            if filename.endswith("_digit_large_box.png"):
                filepath = os.path.join(self.roi_dir, filename)
                image = cv2.imread(filepath)
                if image is not None:
                    roi_images.append((filename, image))
                    logger.info(f"加载ROI图片: {filename}")
        
        logger.info(f"总共加载了 {len(roi_images)} 个手写数字ROI图片")
        return roi_images
    
    def test_ocr_configuration(self, config_name: str, ocr_params: Dict) -> Dict[str, Any]:
        """测试OCR配置"""
        logger.info(f"测试OCR配置: {config_name}")
        
        try:
            # 创建OCR引擎
            ocr = PaddleOCR(
                use_angle_cls=True,
                lang=ocr_params.get('lang', 'ch'),
                use_gpu=False,
                **{k: v for k, v in ocr_params.items() if k != 'lang'}
            )
            
            # 加载测试图片
            roi_images = self.load_handwriting_roi_images()
            if not roi_images:
                return {"error": "没有找到测试图片"}
            
            results = []
            
            for filename, image in roi_images:
                try:
                    # OCR识别
                    result = ocr.ocr(image, cls=True)
                    
                    # 提取文本和置信度
                    extracted_text = ""
                    avg_confidence = 0.0
                    
                    if result and result[0]:
                        texts = []
                        confidences = []
                        
                        for line in result[0]:
                            if len(line) >= 2 and len(line[1]) >= 2:
                                text = line[1][0]
                                confidence = line[1][1]
                                
                                # 过滤数字和常见符号
                                filtered_text = ''.join(c for c in text if c.isdigit() or c in '.-/')
                                if filtered_text:
                                    texts.append(filtered_text)
                                    confidences.append(confidence)
                        
                        if texts:
                            extracted_text = ''.join(texts)
                            avg_confidence = sum(confidences) / len(confidences)
                    
                    results.append({
                        "filename": filename,
                        "text": extracted_text,
                        "confidence": avg_confidence,
                        "success": len(extracted_text) > 0
                    })
                    
                except Exception as e:
                    results.append({
                        "filename": filename,
                        "error": str(e),
                        "success": False
                    })
            
            # 计算统计信息
            total_images = len(results)
            successful_extractions = sum(1 for r in results if r.get("success", False))
            avg_confidence = np.mean([r.get("confidence", 0) for r in results if r.get("success", False)])
            
            return {
                "config_name": config_name,
                "total_images": total_images,
                "successful_extractions": successful_extractions,
                "success_rate": successful_extractions / total_images if total_images > 0 else 0,
                "avg_confidence": float(avg_confidence) if not np.isnan(avg_confidence) else 0.0,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"测试配置 {config_name} 失败: {str(e)}")
            return {"error": str(e)}
    
    def run_optimization_tests(self) -> Dict[str, Any]:
        """运行优化测试"""
        logger.info("开始手写数字识别优化测试")
        
        # 定义测试配置
        test_configs = {
            "当前配置": {
                'lang': 'ch',
                'det_db_thresh': 0.0,
                'det_db_box_thresh': 0.0,
                'det_db_unclip_ratio': 2.0,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 30,
                'drop_score': 0.0
            },
            
            "数字专用模式": {
                'lang': 'en',  # 英文模式对数字更友好
                'det_db_thresh': 0.1,
                'det_db_box_thresh': 0.2,
                'det_db_unclip_ratio': 1.5,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 20,
                'drop_score': 0.0
            },
            
            "高精度模式": {
                'lang': 'ch',
                'det_db_thresh': 0.05,
                'det_db_box_thresh': 0.1,
                'det_db_unclip_ratio': 2.5,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 15,
                'drop_score': 0.0
            },
            
            "手写优化模式": {
                'lang': 'ch',
                'det_db_thresh': 0.2,
                'det_db_box_thresh': 0.3,
                'det_db_unclip_ratio': 3.0,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 25,
                'drop_score': 0.1
            },
            
            "混合模式": {
                'lang': 'ch',
                'det_db_thresh': 0.15,
                'det_db_box_thresh': 0.25,
                'det_db_unclip_ratio': 2.2,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 20,
                'drop_score': 0.05
            }
        }
        
        # 运行所有测试
        all_results = {}
        
        for config_name, config_params in test_configs.items():
            result = self.test_ocr_configuration(config_name, config_params)
            all_results[config_name] = result
            
            if "error" not in result:
                logger.info(f"{config_name}: 成功率 {result['success_rate']:.1%}, "
                           f"平均置信度 {result['avg_confidence']:.3f}")
        
        return all_results
    
    def analyze_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析测试结果"""
        logger.info("分析测试结果...")
        
        # 找到最佳配置
        best_config = None
        best_score = 0
        
        valid_results = {k: v for k, v in results.items() if "error" not in v}
        
        for config_name, result in valid_results.items():
            # 综合评分：成功率 * 0.7 + 平均置信度 * 0.3
            score = result['success_rate'] * 0.7 + result['avg_confidence'] * 0.3
            
            if score > best_score:
                best_score = score
                best_config = config_name
        
        # 生成分析报告
        analysis = {
            "best_config": best_config,
            "best_score": best_score,
            "comparison": []
        }
        
        for config_name, result in valid_results.items():
            if "error" not in result:
                analysis["comparison"].append({
                    "config": config_name,
                    "success_rate": result['success_rate'],
                    "avg_confidence": result['avg_confidence'],
                    "score": result['success_rate'] * 0.7 + result['avg_confidence'] * 0.3,
                    "is_best": config_name == best_config
                })
        
        # 按评分排序
        analysis["comparison"].sort(key=lambda x: x['score'], reverse=True)
        
        return analysis
    
    def generate_optimized_config(self, best_config_name: str, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成优化后的配置"""
        if best_config_name not in results:
            return {}
        
        # 基于最佳配置生成优化配置
        test_configs = {
            "当前配置": {
                'language': 'ch',
                'det_db_thresh': 0.0,
                'det_db_box_thresh': 0.0,
                'det_db_unclip_ratio': 2.0,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 30,
                'drop_score': 0.0
            },
            
            "数字专用模式": {
                'language': 'en',
                'det_db_thresh': 0.1,
                'det_db_box_thresh': 0.2,
                'det_db_unclip_ratio': 1.5,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 20,
                'drop_score': 0.0
            },
            
            "高精度模式": {
                'language': 'ch',
                'det_db_thresh': 0.05,
                'det_db_box_thresh': 0.1,
                'det_db_unclip_ratio': 2.5,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 15,
                'drop_score': 0.0
            },
            
            "手写优化模式": {
                'language': 'ch',
                'det_db_thresh': 0.2,
                'det_db_box_thresh': 0.3,
                'det_db_unclip_ratio': 3.0,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 25,
                'drop_score': 0.1
            },
            
            "混合模式": {
                'language': 'ch',
                'det_db_thresh': 0.15,
                'det_db_box_thresh': 0.25,
                'det_db_unclip_ratio': 2.2,
                'rec_batch_num': 1,
                'use_space_char': False,
                'max_text_length': 20,
                'drop_score': 0.05
            }
        }
        
        return test_configs.get(best_config_name, {})
    
    def save_results(self, results: Dict[str, Any], analysis: Dict[str, Any]):
        """保存测试结果"""
        output_file = "handwriting_optimization_results.json"
        
        output_data = {
            "test_results": results,
            "analysis": analysis,
            "timestamp": str(np.datetime64('now'))
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试结果已保存到: {output_file}")

def main():
    """主函数"""
    print("🚀 手写数字识别优化")
    print("="*80)
    
    optimizer = HandwritingDigitOptimizer()
    
    # 运行优化测试
    results = optimizer.run_optimization_tests()
    
    # 分析结果
    analysis = optimizer.analyze_results(results)
    
    # 显示结果
    print("\n📊 测试结果:")
    print("="*50)
    
    for item in analysis["comparison"]:
        status = "🏆" if item["is_best"] else "  "
        print(f"{status} {item['config']:<15}: "
              f"成功率 {item['success_rate']:.1%}, "
              f"置信度 {item['avg_confidence']:.3f}, "
              f"评分 {item['score']:.3f}")
    
    # 显示最佳配置
    if analysis["best_config"]:
        print(f"\n🎯 最佳配置: {analysis['best_config']}")
        
        # 生成优化配置
        optimized_config = optimizer.generate_optimized_config(analysis["best_config"], results)
        
        if optimized_config:
            print("\n💡 建议的OCR_DIGIT配置:")
            print("-" * 30)
            for key, value in optimized_config.items():
                print(f"{key} = {value}")
    
    # 保存结果
    optimizer.save_results(results, analysis)
    
    print("\n" + "="*80)
    print("✅ 优化测试完成！")
    print("💡 请根据测试结果更新 config_new.ini 中的 [OCR_DIGIT] 配置")
    print("="*80)

if __name__ == "__main__":
    main()
