#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def analyze_excel_results():
    """分析Excel处理结果"""
    excel_file = './output/medical_data_mixed.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        print("=" * 60)
        print("OCR-PDF混合内容抽取整理 - 结果分析")
        print("=" * 60)
        
        # 基本统计
        total_records = len(df)
        print(f"📊 总记录数: {total_records}")
        print(f"📊 总字段数: {len(df.columns)}")
        
        # 字段统计
        print(f"\n📋 字段列表:")
        for i, col in enumerate(df.columns, 1):
            non_null_count = df[col].notna().sum()
            success_rate = (non_null_count / total_records) * 100
            print(f"  {i:2d}. {col}: {non_null_count}/{total_records} ({success_rate:.1f}%)")
        
        # 重点字段分析
        key_fields = ['姓名', '性别', '年龄', '总检日期', '身高', '体重', '血压_收缩压', '血压_舒张压']
        print(f"\n🎯 重点字段提取情况:")
        for field in key_fields:
            if field in df.columns:
                non_null_count = df[field].notna().sum()
                success_rate = (non_null_count / total_records) * 100
                status = "✅" if success_rate > 80 else "⚠️" if success_rate > 50 else "❌"
                print(f"  {status} {field}: {non_null_count}/{total_records} ({success_rate:.1f}%)")
        
        # 血液指标分析
        blood_fields = ['血色素', '总胆固醇', '甘油三酯', '高密度脂蛋白胆固醇', '低密度脂蛋白胆固醇']
        print(f"\n🩸 血液指标提取情况:")
        for field in blood_fields:
            if field in df.columns:
                non_null_count = df[field].notna().sum()
                success_rate = (non_null_count / total_records) * 100
                status = "✅" if success_rate > 30 else "⚠️" if success_rate > 10 else "❌"
                print(f"  {status} {field}: {non_null_count}/{total_records} ({success_rate:.1f}%)")
        
        # 心超字段分析
        echo_fields = ['超声心动图_超声所见', '超声心动图_超声提示']
        print(f"\n💓 心超OCR提取情况:")
        for field in echo_fields:
            if field in df.columns:
                non_null_count = df[field].notna().sum()
                success_rate = (non_null_count / total_records) * 100
                status = "✅" if success_rate > 0 else "❌"
                print(f"  {status} {field}: {non_null_count}/{total_records} ({success_rate:.1f}%)")
                
                # 显示成功提取的样例
                if non_null_count > 0:
                    sample = df[df[field].notna()][field].iloc[0]
                    print(f"      样例: {sample[:100]}...")
        
        # 检查数据冲突情况
        print(f"\n⚠️ 数据冲突分析:")
        duplicate_names = df[df.duplicated(['姓名'], keep=False)]['姓名'].unique()
        if len(duplicate_names) > 0:
            print(f"  发现 {len(duplicate_names)} 个重名人员:")
            for name in duplicate_names:
                count = len(df[df['姓名'] == name])
                print(f"    - {name}: {count} 条记录")
        else:
            print("  ✅ 无重名冲突")
        
        # 处理状态分析
        if '处理状态' in df.columns:
            status_counts = df['处理状态'].value_counts()
            print(f"\n📈 处理状态统计:")
            for status, count in status_counts.items():
                percentage = (count / total_records) * 100
                print(f"  {status}: {count} ({percentage:.1f}%)")
        
        print("\n" + "=" * 60)
        
    except Exception as e:
        print(f"❌ 分析Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_excel_results()
