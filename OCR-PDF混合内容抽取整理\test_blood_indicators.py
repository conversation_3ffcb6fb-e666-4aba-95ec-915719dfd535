#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import fitz
import re
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_blood_indicators():
    """测试血液指标提取"""
    
    # 测试几个PDF文件
    test_pdfs = [
        '../files/7.28体检/202412130173_弄庆新_45250119800812256X.pdf',
        '../files/7.28体检/202403140123_张东_44082219660422041X.pdf',
        '../files/7.28体检/202403140124_黄依依_440102195309300021.pdf'
    ]
    
    for pdf_path in test_pdfs:
        if not os.path.exists(pdf_path):
            logger.warning(f'PDF文件不存在: {pdf_path}')
            continue
        
        logger.info(f'测试PDF: {os.path.basename(pdf_path)}')
        
        try:
            doc = fitz.open(pdf_path)
            
            # 提取所有文本
            all_text = ""
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()
                all_text += page_text + "\n"
            
            doc.close()
            
            logger.info(f'提取文本长度: {len(all_text)}字符')
            
            # 测试甘油三酯提取
            tg_patterns = [
                r'甘油三酯\(TG\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'甘油三酯[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'三酰甘油\(TG\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'(?<!HDL)TG[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'甘油三酯[:\s]*(\d+(?:\.\d+)?)',
                r'(?<!HDL)TG[:\s]*(\d+(?:\.\d+)?)',
                r'TRIG[:\s]*(\d+(?:\.\d+)?)',
                # 新增模式
                r'甘油三酯\s+(\d+(?:\.\d+)?)\s+mmol/L',
                r'TG\s+(\d+(?:\.\d+)?)\s+mmol/L',
                r'甘油三酯.*?(\d+(?:\.\d+)?)\s*mmol/L',
                r'TG.*?(\d+(?:\.\d+)?)\s*mmol/L'
            ]
            
            tg_found = False
            for i, pattern in enumerate(tg_patterns):
                match = re.search(pattern, all_text, re.IGNORECASE)
                if match:
                    value = float(match.group(1))
                    if 0.1 <= value <= 10.0:
                        logger.info(f'  甘油三酯(TG): {match.group(1)} (模式{i+1})')
                        tg_found = True
                        break
            
            if not tg_found:
                # 搜索包含甘油三酯的文本片段
                tg_lines = [line for line in all_text.split('\n') if '甘油三酯' in line or 'TG' in line]
                if tg_lines:
                    logger.info(f'  甘油三酯相关文本: {tg_lines[:3]}')
                else:
                    logger.info('  未找到甘油三酯相关文本')
            
            # 测试高密度脂蛋白胆固醇提取
            hdl_patterns = [
                r'高密度脂蛋白胆固醇\(HDL-C\)[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'高密度脂蛋白胆固醇[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'高密度脂蛋白[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'HDL-C[:\s]*(\d+(?:\.\d+)?)\s*mmol/L[↑↓]?',
                r'高密度脂蛋白胆固醇[:\s]*(\d+(?:\.\d+)?)',
                r'HDL-C[:\s]*(\d+(?:\.\d+)?)',
                r'(?<!L)HDL(?!C)[:\s]*(\d+(?:\.\d+)?)',
                # 新增模式
                r'高密度脂蛋白胆固醇\s+(\d+(?:\.\d+)?)\s+mmol/L',
                r'HDL-C\s+(\d+(?:\.\d+)?)\s+mmol/L',
                r'高密度脂蛋白.*?(\d+(?:\.\d+)?)\s*mmol/L',
                r'HDL.*?(\d+(?:\.\d+)?)\s*mmol/L'
            ]
            
            hdl_found = False
            for i, pattern in enumerate(hdl_patterns):
                match = re.search(pattern, all_text, re.IGNORECASE)
                if match:
                    value = float(match.group(1))
                    if 0.5 <= value <= 5.0:
                        logger.info(f'  高密度脂蛋白胆固醇(HDL-C): {match.group(1)} (模式{i+1})')
                        hdl_found = True
                        break
            
            if not hdl_found:
                # 搜索包含HDL的文本片段
                hdl_lines = [line for line in all_text.split('\n') if '高密度脂蛋白' in line or 'HDL' in line]
                if hdl_lines:
                    logger.info(f'  HDL相关文本: {hdl_lines[:3]}')
                else:
                    logger.info('  未找到HDL相关文本')
            
            logger.info('-' * 50)
            
        except Exception as e:
            logger.error(f'处理PDF出错: {str(e)}')
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_blood_indicators()
