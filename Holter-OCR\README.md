# Holter OCR System

A comprehensive OCR-based information extraction system for Holter PDF reports. This system extracts medical data from the first page of PDF files and exports the results to Excel format.

## Features

- **OCR Text Extraction**: Uses PaddleOCR for high-accuracy Chinese text recognition
- **Field Extraction**: Intelligent pattern matching to extract specific medical data fields
- **Batch Processing**: Processes multiple PDF files simultaneously with multi-threading support
- **Data Validation**: Validates and cleans extracted data with proper type conversion
- **Excel Export**: Exports data to formatted Excel files matching the template structure
- **Error Handling**: Comprehensive error logging and progress tracking
- **Configurable**: Flexible configuration system with command-line interface

## System Requirements

- Python 3.7+
- PaddleOCR (pre-installed in conda base environment)
- Required Python packages:
  - pandas
  - openpyxl
  - PyMuPDF (fitz)
  - Pillow
  - tqdm

## Installation

The system is designed to work with the existing conda base environment where PaddleOCR is already installed.

```bash
# Navigate to the Holter-OCR directory
cd Holter-OCR

# Install additional required packages if needed
pip install pandas openpyxl PyMuPDF Pillow tqdm
```

## Directory Structure

```
Holter-OCR/
├── main.py                    # Main execution script
├── ocr_extractor.py          # OCR text extraction module
├── field_extractor.py        # Medical field extraction logic
├── batch_processor.py        # Batch processing system
├── data_exporter.py          # Excel export functionality
├── error_handler.py          # Error handling and logging
├── holter_config.json        # Configuration file
├── README.md                 # This file
├── Holter/                   # Directory containing PDF files
│   ├── 丁刚.pdf
│   ├── 于峰修.pdf
│   └── ...
├── 安医附院Holter（2024）.xlsx  # Excel template file
└── logs/                     # Log files (created automatically)
```

## Usage

### Basic Usage

Run the system with default settings:

```bash
python main.py
```

### Advanced Usage

```bash
# Use custom configuration file
python main.py --config my_config.json

# Override specific settings
python main.py --pdf-dir "path/to/pdfs" --output "results.xlsx" --workers 8

# Test extraction on a single file
python main.py --test "Holter/丁刚.pdf"

# Create a sample configuration file
python main.py --create-config

# Enable verbose logging
python main.py --verbose
```

### Configuration

The system uses a JSON configuration file (`holter_config.json`) with the following options:

```json
{
  "pdf_directory": "Holter",           // Directory containing PDF files
  "output_file": "holter_extracted_data.xlsx",  // Output Excel file
  "template_file": "安医附院Holter（2024）.xlsx", // Excel template
  "log_dir": "logs",                   // Log directory
  "log_level": "INFO",                 // Logging level (DEBUG, INFO, WARNING, ERROR)
  "use_gpu": false,                    // Use GPU for OCR (if available)
  "ocr_language": "ch",                // OCR language (ch for Chinese)
  "max_workers": 4,                    // Number of worker threads
  "use_threading": true,               // Enable multi-threading
  "generate_summary": true             // Generate summary report
}
```

## Extracted Fields

The system extracts the following medical data fields from each PDF:

### Basic Information
- 姓名 (Name)
- 总心搏数 (Total heartbeats)
- RR间期＞2.0s次数 (RR intervals > 2.0s count)
- 最长RR间期(s) (Longest RR interval)

### Heart Rate Data
- 平均心率（bpm）(Average heart rate)
- 最快心率（bpm）(Maximum heart rate)
- 最慢心率（bpm）(Minimum heart rate)

### Sinus Rhythm Data
- 窦性心搏总数 (Sinus beats total)
- 窦性平均心率（bpm）(Sinus average heart rate)
- 窦性最快心率（bpm）(Sinus maximum heart rate)
- 窦性最慢心率（bpm）(Sinus minimum heart rate)

### Atrial Arrhythmia
- 房早总数 (Atrial premature beats total)
- 单发 (Single)
- 成对 (Paired)
- 二联律 (Bigeminy)
- 三联律 (Trigeminy)
- 房早未下传 (Non-conducted atrial premature beats)

### Ventricular Arrhythmia
- 室早总数 (Ventricular premature beats total)
- 单发.1 (Single ventricular)
- 成对.1 (Paired ventricular)
- 二联律.1 (Ventricular bigeminy)
- 三联律.1 (Ventricular trigeminy)
- 室早未下传 (Non-conducted ventricular premature beats)

### Heart Rate Variability
- 心率变异性 (Heart rate variability)
- SDNN
- SDANN
- SDNNIndex
- rMSSD
- pNN50
- 三角指数 (Triangular index)

## Output

The system generates the following output files:

1. **Main Data File**: `holter_extracted_data.xlsx`
   - Contains all extracted data in Excel format
   - Formatted with headers, borders, and proper alignment
   - Matches the structure of the template file

2. **Summary Report**: `holter_extracted_data_summary.xlsx` (if enabled)
   - Contains statistical summary of extracted data
   - Includes mean, standard deviation, min/max values
   - Shows data completeness statistics

3. **Log Files**: `logs/holter_ocr_YYYYMMDD_HHMMSS.log`
   - Detailed processing logs
   - Error logs with stack traces
   - Progress tracking information

4. **Error Report**: `logs/error_report_YYYYMMDD_HHMMSS.json`
   - Detailed error analysis in JSON format
   - Error statistics and categorization
   - Failed file information

## Error Handling

The system includes comprehensive error handling:

- **PDF Processing Errors**: Handles corrupted or unreadable PDF files
- **OCR Errors**: Manages OCR failures and low-confidence text
- **Field Extraction Errors**: Handles missing or malformed data fields
- **Data Validation Errors**: Manages type conversion and validation issues
- **File I/O Errors**: Handles file access and permission issues

All errors are logged with detailed context information and stack traces.

## Performance

- **Multi-threading**: Processes multiple PDFs simultaneously
- **Memory Efficient**: Processes files one at a time to manage memory usage
- **Progress Tracking**: Real-time progress updates with estimated completion time
- **Configurable Workers**: Adjust thread count based on system capabilities

Typical processing speed: 2-5 seconds per PDF file (depending on system performance)

## Troubleshooting

### Common Issues

1. **No text extracted from PDF**
   - Check if PDF contains scanned images rather than text
   - Verify PDF is not corrupted
   - Try increasing OCR zoom factor

2. **Low extraction accuracy**
   - Check image quality in PDF
   - Verify OCR language setting (should be 'ch' for Chinese)
   - Consider using GPU acceleration if available

3. **Memory issues**
   - Reduce number of worker threads
   - Process files in smaller batches
   - Check available system memory

4. **Permission errors**
   - Ensure write permissions for output directory
   - Check if output files are open in other applications

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
python main.py --verbose
```

This will provide detailed information about:
- OCR processing steps
- Field extraction attempts
- Data validation results
- File processing status

## Support

For issues or questions:
1. Check the log files in the `logs/` directory
2. Review the error report JSON file
3. Test with a single file using `--test` option
4. Enable verbose logging for detailed diagnostics

## License

This system is designed for medical data processing and should be used in compliance with relevant data protection and privacy regulations.
