#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的数字识别优化测试
专门针对手写数字ROI图片进行测试
"""

import os
import cv2
import numpy as np
from paddleocr import PaddleOCR
import json
import logging
from typing import Dict, List, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_roi_images_safely():
    """安全加载ROI图片（处理中文文件名）"""
    roi_dir = "roi_images"
    roi_images = []
    
    if not os.path.exists(roi_dir):
        print("❌ ROI目录不存在")
        return roi_images
    
    # 查找大框数字ROI图片
    target_files = [
        "current_image_出生日期_digit_large_box.png",
        "current_image_血压_digit_large_box.png", 
        "current_image_心率_digit_large_box.png",
        "current_image_体重_digit_large_box.png",
        "current_image_身高_digit_large_box.png",
        "current_image_手机号码_digit_large_box.png"
    ]
    
    for filename in target_files:
        filepath = os.path.join(roi_dir, filename)
        
        try:
            # 使用cv2.imdecode处理中文路径
            with open(filepath, 'rb') as f:
                image_data = f.read()
            
            image_array = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
            
            if image is not None:
                roi_images.append((filename, image))
                print(f"✅ 加载ROI图片: {filename}")
            else:
                print(f"❌ 无法解码图片: {filename}")
                
        except Exception as e:
            print(f"❌ 加载图片失败 {filename}: {str(e)}")
    
    print(f"📊 总共加载了 {len(roi_images)} 个ROI图片")
    return roi_images

def test_current_config():
    """测试当前配置"""
    print("\n🔍 测试当前配置...")
    
    # 禁用PaddleOCR的调试日志
    import logging as paddle_logging
    paddle_logging.getLogger('ppocr').setLevel(paddle_logging.WARNING)
    
    try:
        # 使用当前配置创建OCR引擎
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            use_gpu=False,
            det_db_thresh=0.0,
            det_db_box_thresh=0.0,
            det_db_unclip_ratio=2.0,
            rec_batch_num=1,
            use_space_char=False,
            max_text_length=30,
            drop_score=0.0
        )
        
        # 加载测试图片
        roi_images = load_roi_images_safely()
        
        if not roi_images:
            print("❌ 没有找到测试图片")
            return {}
        
        results = {}
        
        for filename, image in roi_images:
            field_name = filename.split('_')[2]  # 提取字段名
            
            try:
                # OCR识别
                result = ocr.ocr(image, cls=True)
                
                # 提取文本
                extracted_text = ""
                avg_confidence = 0.0
                
                if result and result[0]:
                    texts = []
                    confidences = []
                    
                    for line in result[0]:
                        if len(line) >= 2 and len(line[1]) >= 2:
                            text = line[1][0]
                            confidence = line[1][1]
                            
                            # 过滤数字和常见符号
                            filtered_text = ''.join(c for c in text if c.isdigit() or c in '.-/')
                            if filtered_text:
                                texts.append(filtered_text)
                                confidences.append(confidence)
                    
                    if texts:
                        extracted_text = ''.join(texts)
                        avg_confidence = sum(confidences) / len(confidences)
                
                results[field_name] = {
                    "text": extracted_text,
                    "confidence": avg_confidence,
                    "success": len(extracted_text) > 0
                }
                
                status = "✅" if len(extracted_text) > 0 else "❌"
                print(f"  {status} {field_name}: '{extracted_text}' (置信度: {avg_confidence:.3f})")
                
            except Exception as e:
                results[field_name] = {
                    "error": str(e),
                    "success": False
                }
                print(f"  ❌ {field_name}: 识别失败 - {str(e)}")
        
        return results
        
    except Exception as e:
        print(f"❌ OCR引擎创建失败: {str(e)}")
        return {}

def test_optimized_configs():
    """测试优化配置"""
    print("\n🔍 测试优化配置...")
    
    # 禁用PaddleOCR的调试日志
    import logging as paddle_logging
    paddle_logging.getLogger('ppocr').setLevel(paddle_logging.WARNING)
    
    # 定义测试配置（只使用中文模式，避免下载问题）
    test_configs = {
        "高精度模式": {
            'det_db_thresh': 0.05,
            'det_db_box_thresh': 0.1,
            'det_db_unclip_ratio': 2.5,
            'max_text_length': 15,
            'drop_score': 0.0
        },
        
        "手写优化模式": {
            'det_db_thresh': 0.2,
            'det_db_box_thresh': 0.3,
            'det_db_unclip_ratio': 3.0,
            'max_text_length': 25,
            'drop_score': 0.1
        },
        
        "混合模式": {
            'det_db_thresh': 0.15,
            'det_db_box_thresh': 0.25,
            'det_db_unclip_ratio': 2.2,
            'max_text_length': 20,
            'drop_score': 0.05
        }
    }
    
    # 加载测试图片
    roi_images = load_roi_images_safely()
    
    if not roi_images:
        print("❌ 没有找到测试图片")
        return {}
    
    all_results = {}
    
    for config_name, config_params in test_configs.items():
        print(f"\n📋 测试配置: {config_name}")
        
        try:
            # 创建OCR引擎
            ocr = PaddleOCR(
                use_angle_cls=True,
                lang='ch',
                use_gpu=False,
                rec_batch_num=1,
                use_space_char=False,
                **config_params
            )
            
            config_results = {}
            successful_count = 0
            total_confidence = 0
            
            for filename, image in roi_images:
                field_name = filename.split('_')[2]  # 提取字段名
                
                try:
                    # OCR识别
                    result = ocr.ocr(image, cls=True)
                    
                    # 提取文本
                    extracted_text = ""
                    avg_confidence = 0.0
                    
                    if result and result[0]:
                        texts = []
                        confidences = []
                        
                        for line in result[0]:
                            if len(line) >= 2 and len(line[1]) >= 2:
                                text = line[1][0]
                                confidence = line[1][1]
                                
                                # 过滤数字和常见符号
                                filtered_text = ''.join(c for c in text if c.isdigit() or c in '.-/')
                                if filtered_text:
                                    texts.append(filtered_text)
                                    confidences.append(confidence)
                        
                        if texts:
                            extracted_text = ''.join(texts)
                            avg_confidence = sum(confidences) / len(confidences)
                    
                    config_results[field_name] = {
                        "text": extracted_text,
                        "confidence": avg_confidence,
                        "success": len(extracted_text) > 0
                    }
                    
                    if len(extracted_text) > 0:
                        successful_count += 1
                        total_confidence += avg_confidence
                    
                    status = "✅" if len(extracted_text) > 0 else "❌"
                    print(f"    {status} {field_name}: '{extracted_text}' (置信度: {avg_confidence:.3f})")
                    
                except Exception as e:
                    config_results[field_name] = {
                        "error": str(e),
                        "success": False
                    }
                    print(f"    ❌ {field_name}: 识别失败 - {str(e)}")
            
            # 计算统计信息
            success_rate = successful_count / len(roi_images) if len(roi_images) > 0 else 0
            avg_confidence = total_confidence / successful_count if successful_count > 0 else 0
            
            all_results[config_name] = {
                "results": config_results,
                "success_rate": success_rate,
                "avg_confidence": avg_confidence,
                "config": config_params
            }
            
            print(f"    📊 成功率: {success_rate:.1%}, 平均置信度: {avg_confidence:.3f}")
            
        except Exception as e:
            print(f"    ❌ 配置测试失败: {str(e)}")
            all_results[config_name] = {"error": str(e)}
    
    return all_results

def analyze_and_recommend(current_results, optimized_results):
    """分析结果并给出建议"""
    print("\n📊 结果分析和建议")
    print("="*50)
    
    # 计算当前配置的成功率
    current_success_count = sum(1 for r in current_results.values() if r.get("success", False))
    current_success_rate = current_success_count / len(current_results) if current_results else 0
    current_avg_confidence = np.mean([r.get("confidence", 0) for r in current_results.values() if r.get("success", False)])
    
    print(f"当前配置: 成功率 {current_success_rate:.1%}, 平均置信度 {current_avg_confidence:.3f}")
    
    # 找到最佳配置
    best_config = None
    best_score = current_success_rate * 0.7 + current_avg_confidence * 0.3
    
    for config_name, result in optimized_results.items():
        if "error" not in result:
            score = result['success_rate'] * 0.7 + result['avg_confidence'] * 0.3
            print(f"{config_name}: 成功率 {result['success_rate']:.1%}, "
                  f"平均置信度 {result['avg_confidence']:.3f}, 评分 {score:.3f}")
            
            if score > best_score:
                best_score = score
                best_config = config_name
    
    # 给出建议
    if best_config:
        print(f"\n🎯 推荐配置: {best_config}")
        print(f"📈 性能提升: {(best_score - (current_success_rate * 0.7 + current_avg_confidence * 0.3)):.3f}")
        
        recommended_config = optimized_results[best_config]["config"]
        print("\n💡 建议的config_new.ini [OCR_DIGIT]配置:")
        print("-" * 40)
        for key, value in recommended_config.items():
            print(f"{key} = {value}")
        
        return recommended_config
    else:
        print("\n✅ 当前配置已经是最佳的")
        return None

def main():
    """主函数"""
    print("🚀 手写数字识别优化测试")
    print("="*80)
    
    # 测试当前配置
    current_results = test_current_config()
    
    # 测试优化配置
    optimized_results = test_optimized_configs()
    
    # 分析并给出建议
    recommended_config = analyze_and_recommend(current_results, optimized_results)
    
    # 保存结果
    output_data = {
        "current_results": current_results,
        "optimized_results": optimized_results,
        "recommended_config": recommended_config
    }
    
    with open("digit_optimization_results.json", 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print("\n" + "="*80)
    print("✅ 优化测试完成！结果已保存到 digit_optimization_results.json")
    print("="*80)

if __name__ == "__main__":
    main()
