#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import fitz
import re
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ck_extraction():
    """测试肌酸激酶提取"""
    
    input_path = '../files/7.28体检'
    
    if not os.path.exists(input_path):
        logger.error(f'输入目录不存在: {input_path}')
        return
    
    pdf_files = [f for f in os.listdir(input_path) if f.lower().endswith('.pdf')]
    logger.info(f'找到 {len(pdf_files)} 个PDF文件')
    
    ck_count = 0
    ckmb_count = 0
    total_count = 0
    
    ck_results = []
    ckmb_results = []
    
    for pdf_file in pdf_files[:10]:  # 只测试前10个文件
        pdf_path = os.path.join(input_path, pdf_file)
        total_count += 1
        
        try:
            doc = fitz.open(pdf_path)
            
            # 提取所有文本
            all_text = ""
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()
                all_text += page_text + "\n"
            
            doc.close()
            
            logger.info(f'测试 {pdf_file}')
            
            # 测试肌酸激酶提取
            ck_patterns = [
                r'肌酸激酶[^同工酶]*[:\s]*(\d+(?:\.\d+)?)',  # 排除同工酶
                r'CK[^-M]*[:\s]*(\d+(?:\.\d+)?)',
                r'肌酸激酶\s*(\d+(?:\.\d+)?)',
                # 新增模式
                r'肌酸激酶\s+(\d+(?:\.\d+)?)\s+U/L',
                r'CK\s+(\d+(?:\.\d+)?)\s+U/L',
                r'肌酸激酶.*?(\d+(?:\.\d+)?)\s*U/L'
            ]
            
            ck_found = False
            for i, pattern in enumerate(ck_patterns):
                match = re.search(pattern, all_text, re.IGNORECASE)
                if match:
                    try:
                        value = float(match.group(1))
                        if 10 <= value <= 1000:  # CK合理范围
                            logger.info(f'  肌酸激酶(CK): {match.group(1)} (模式{i+1})')
                            ck_count += 1
                            ck_results.append((pdf_file, match.group(1)))
                            ck_found = True
                            break
                    except ValueError:
                        continue
            
            if not ck_found:
                # 搜索包含肌酸激酶的文本片段
                ck_lines = [line.strip() for line in all_text.split('\n') if '肌酸激酶' in line and '同工酶' not in line]
                if ck_lines:
                    logger.info(f'  肌酸激酶相关文本: {ck_lines[:2]}')
                else:
                    # 搜索CK相关文本
                    ck_lines = [line.strip() for line in all_text.split('\n') if re.search(r'\bCK\b', line) and 'CK-MB' not in line]
                    if ck_lines:
                        logger.info(f'  CK相关文本: {ck_lines[:2]}')
            
            # 测试肌酸激酶同工酶提取
            ckmb_patterns = [
                r'肌酸激酶同工酶\(CK-MB\)\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s+U/L',
                r'肌酸激酶同工酶[:\s]*(\d+(?:\.\d+)?)',
                r'肌酸激酶同功酶[:\s]*(\d+(?:\.\d+)?)',
                r'CK-MB[:\s]*(\d+(?:\.\d+)?)',
                r'CKMB[:\s]*(\d+(?:\.\d+)?)',
                # 新增模式
                r'CK-MB\s+(\d+(?:\.\d+)?)\s+U/L',
                r'肌酸激酶同工酶.*?(\d+(?:\.\d+)?)\s*U/L'
            ]
            
            ckmb_found = False
            for i, pattern in enumerate(ckmb_patterns):
                match = re.search(pattern, all_text, re.IGNORECASE)
                if match:
                    try:
                        value = float(match.group(1))
                        if 0 <= value <= 100:  # CK-MB合理范围
                            logger.info(f'  肌酸激酶同工酶(CK-MB): {match.group(1)} (模式{i+1})')
                            ckmb_count += 1
                            ckmb_results.append((pdf_file, match.group(1)))
                            ckmb_found = True
                            break
                    except ValueError:
                        continue
            
            if not ckmb_found:
                # 搜索包含CK-MB的文本片段
                ckmb_lines = [line.strip() for line in all_text.split('\n') if 'CK-MB' in line or '肌酸激酶同工酶' in line]
                if ckmb_lines:
                    logger.info(f'  CK-MB相关文本: {ckmb_lines[:2]}')
            
            logger.info('-' * 50)
            
        except Exception as e:
            logger.error(f'处理 {pdf_file} 时出错: {str(e)}')
    
    print("\n" + "=" * 80)
    print("肌酸激酶提取分析结果")
    print("=" * 80)
    print(f"测试PDF文件数: {total_count}")
    print(f"包含肌酸激酶(CK)的PDF数: {ck_count} ({ck_count/total_count*100:.1f}%)")
    print(f"包含肌酸激酶同工酶(CK-MB)的PDF数: {ckmb_count} ({ckmb_count/total_count*100:.1f}%)")
    
    if ck_results:
        print(f"\n肌酸激酶(CK)提取结果:")
        for file, value in ck_results:
            print(f"  {file}: {value}")
    
    if ckmb_results:
        print(f"\n肌酸激酶同工酶(CK-MB)提取结果:")
        for file, value in ckmb_results:
            print(f"  {file}: {value}")
    
    print("=" * 80)

if __name__ == "__main__":
    test_ck_extraction()
