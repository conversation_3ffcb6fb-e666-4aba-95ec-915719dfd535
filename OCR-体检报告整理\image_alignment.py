#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像几何校正与对齐模块
实现基于表格框架的图像几何校正，确保所有图像的绝对对齐
"""

import cv2
import numpy as np
import os
import json
from typing import Tuple, List, Optional, Dict
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageAligner:
    """图像对齐器 - 基于表格框架进行几何校正"""
    
    def __init__(self, target_width: int = 2480, target_height: int = 3507):
        """
        初始化图像对齐器
        
        Args:
            target_width: 目标图像宽度
            target_height: 目标图像高度
        """
        self.target_width = target_width
        self.target_height = target_height
        
        # 表格检测参数
        self.table_detection_params = {
            'gaussian_blur_kernel': (5, 5),
            'canny_low': 50,
            'canny_high': 150,
            'hough_threshold': 100,
            'hough_min_line_length': 200,
            'hough_max_line_gap': 10,
            'contour_min_area': 50000,  # 最小轮廓面积
            'approx_epsilon_factor': 0.02  # 轮廓近似精度因子
        }
    
    def detect_table_frame(self, image: np.ndarray) -> Optional[np.ndarray]:
        """
        检测表格框架的四个角点
        
        Args:
            image: 输入图像
            
        Returns:
            四个角点坐标 [[左上], [右上], [右下], [左下]] 或 None
        """
        try:
            # 1. 预处理：灰度化 + 高斯模糊降噪
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, self.table_detection_params['gaussian_blur_kernel'], 0)
            
            # 2. 边缘检测
            edges = cv2.Canny(blurred, 
                            self.table_detection_params['canny_low'], 
                            self.table_detection_params['canny_high'])
            
            # 3. 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 4. 筛选轮廓：找到面积最大的近似四边形
            table_contour = None
            max_area = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > self.table_detection_params['contour_min_area']:
                    # 轮廓近似
                    epsilon = self.table_detection_params['approx_epsilon_factor'] * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    
                    # 检查是否为四边形且面积最大
                    if len(approx) == 4 and area > max_area:
                        max_area = area
                        table_contour = approx
            
            if table_contour is not None:
                # 5. 排序角点：左上、右上、右下、左下
                corners = self._sort_corners(table_contour.reshape(4, 2))
                logger.info(f"检测到表格框架，面积: {max_area:.0f}")
                return corners
            else:
                logger.warning("未检测到合适的表格框架")
                return None
                
        except Exception as e:
            logger.error(f"表格框架检测失败: {str(e)}")
            return None
    
    def _sort_corners(self, corners: np.ndarray) -> np.ndarray:
        """
        对角点进行排序：左上、右上、右下、左下
        
        Args:
            corners: 四个角点坐标
            
        Returns:
            排序后的角点坐标
        """
        # 计算中心点
        center = np.mean(corners, axis=0)
        
        # 根据相对于中心点的位置进行排序
        def angle_from_center(point):
            return np.arctan2(point[1] - center[1], point[0] - center[0])
        
        # 按角度排序
        sorted_corners = sorted(corners, key=angle_from_center)
        
        # 重新排列为：左上、右上、右下、左下
        # 找到最左上的点作为起始点
        top_left_idx = 0
        min_sum = float('inf')
        for i, corner in enumerate(sorted_corners):
            sum_coords = corner[0] + corner[1]  # x + y 最小的是左上角
            if sum_coords < min_sum:
                min_sum = sum_coords
                top_left_idx = i
        
        # 重新排列
        reordered = []
        for i in range(4):
            reordered.append(sorted_corners[(top_left_idx + i) % 4])
        
        return np.array(reordered, dtype=np.float32)
    
    def correct_perspective(self, image: np.ndarray, corners: np.ndarray) -> np.ndarray:
        """
        透视校正
        
        Args:
            image: 输入图像
            corners: 表格四个角点
            
        Returns:
            校正后的图像
        """
        try:
            # 目标角点（标准矩形）
            target_corners = np.array([
                [0, 0],                                    # 左上
                [self.target_width - 1, 0],               # 右上
                [self.target_width - 1, self.target_height - 1],  # 右下
                [0, self.target_height - 1]               # 左下
            ], dtype=np.float32)
            
            # 计算透视变换矩阵
            transform_matrix = cv2.getPerspectiveTransform(corners, target_corners)
            
            # 应用透视变换
            corrected = cv2.warpPerspective(image, transform_matrix, 
                                          (self.target_width, self.target_height))
            
            logger.info("透视校正完成")
            return corrected
            
        except Exception as e:
            logger.error(f"透视校正失败: {str(e)}")
            return image
    
    def align_image(self, image_path: str, output_path: Optional[str] = None,
                   debug: bool = False, save_debug_image: bool = False) -> Tuple[Optional[np.ndarray], Dict]:
        """
        对齐单张图像
        
        Args:
            image_path: 输入图像路径
            output_path: 输出图像路径（可选）
            debug: 是否保存调试信息
            
        Returns:
            (对齐后的图像, 处理信息)
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"无法读取图像: {image_path}")
                return None, {"error": "无法读取图像"}
            
            original_shape = image.shape[:2]
            logger.info(f"处理图像: {image_path}, 原始尺寸: {original_shape}")
            
            # 检测表格框架
            corners = self.detect_table_frame(image)
            if corners is None:
                logger.warning("未检测到表格框架，返回原图像")
                return image, {"warning": "未检测到表格框架", "corners": None}
            
            # 透视校正
            aligned_image = self.correct_perspective(image, corners)
            
            # 保存结果
            if output_path:
                cv2.imwrite(output_path, aligned_image)
                logger.info(f"对齐图像已保存: {output_path}")
            
            # 调试信息
            processing_info = {
                "original_shape": original_shape,
                "target_shape": (self.target_height, self.target_width),
                "corners": corners.tolist(),
                "success": True
            }
            
            # 保存调试图像（可选）
            if save_debug_image and output_path:
                debug_path = output_path.replace('.png', '_debug.png')
                self._save_debug_image(image, corners, debug_path)
                processing_info["debug_path"] = debug_path
                logger.info(f"调试图像已保存: {debug_path}")
            elif debug:
                logger.debug("跳过调试图像保存（save_debug_image=False）")
            
            return aligned_image, processing_info
            
        except Exception as e:
            logger.error(f"图像对齐失败: {str(e)}")
            return None, {"error": str(e)}
    
    def _save_debug_image(self, original_image: np.ndarray, corners: np.ndarray, 
                         debug_path: str):
        """保存调试图像，显示检测到的角点"""
        try:
            debug_img = original_image.copy()
            
            # 绘制检测到的角点
            for i, corner in enumerate(corners):
                cv2.circle(debug_img, tuple(corner.astype(int)), 10, (0, 255, 0), -1)
                cv2.putText(debug_img, str(i), tuple(corner.astype(int) + 15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # 绘制表格框架
            cv2.polylines(debug_img, [corners.astype(int)], True, (255, 0, 0), 3)
            
            cv2.imwrite(debug_path, debug_img)
            logger.info(f"调试图像已保存: {debug_path}")
            
        except Exception as e:
            logger.error(f"保存调试图像失败: {str(e)}")
    
    def batch_align_images(self, input_dir: str, output_dir: str, 
                          debug: bool = False) -> Dict:
        """
        批量对齐图像
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            debug: 是否保存调试信息
            
        Returns:
            处理结果统计
        """
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 获取所有图像文件
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
            image_files = []
            for ext in image_extensions:
                image_files.extend(Path(input_dir).glob(f'*{ext}'))
                image_files.extend(Path(input_dir).glob(f'*{ext.upper()}'))
            
            results = {
                "total": len(image_files),
                "success": 0,
                "failed": 0,
                "no_table": 0,
                "processing_info": []
            }
            
            for image_file in image_files:
                input_path = str(image_file)
                output_path = os.path.join(output_dir, f"aligned_{image_file.name}")
                
                aligned_image, info = self.align_image(input_path, output_path, debug)
                
                if aligned_image is not None:
                    if "warning" in info:
                        results["no_table"] += 1
                    else:
                        results["success"] += 1
                else:
                    results["failed"] += 1
                
                info["input_file"] = input_path
                info["output_file"] = output_path
                results["processing_info"].append(info)
            
            # 保存处理报告
            report_path = os.path.join(output_dir, "alignment_report.json")
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"批量处理完成: 成功 {results['success']}, 失败 {results['failed']}, 无表格 {results['no_table']}")
            return results
            
        except Exception as e:
            logger.error(f"批量对齐失败: {str(e)}")
            return {"error": str(e)}


def main():
    """主函数 - 用于测试"""
    aligner = ImageAligner()
    
    # 测试单张图像对齐
    input_path = "temp_images/SCAN0018_p1_img1.png"
    output_path = "debug_output/aligned_SCAN0018_p1_img1.png"
    
    if os.path.exists(input_path):
        aligned_image, info = aligner.align_image(input_path, output_path, debug=True)
        if aligned_image is not None:
            print("图像对齐成功！")
            print(f"处理信息: {info}")
        else:
            print("图像对齐失败！")
    else:
        print(f"测试图像不存在: {input_path}")


if __name__ == "__main__":
    main()
