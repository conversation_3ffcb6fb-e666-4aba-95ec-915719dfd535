import os
import re
import logging
from datetime import datetime
from typing import List, Dict, Optional

import cv2
import numpy as np
from configparser import ConfigParser

from ocr_engine import OCREngine
from excel_handler import ExcelHandler

logger = logging.getLogger('MedicalHelper')


class ImageProcessor:
    def __init__(self, config: ConfigParser, ocr_engine, excel_handler):
        self.config = config
        self.ocr_engine = ocr_engine
        self.excel_handler = excel_handler
        self.input_path = config.get('DEFAULT', 'input_images_path')
        self.output_path = config.get('DEFAULT', 'output_images_path')
        self.preprocessed_path = config.get('DEFAULT', 'preprocessed_images_path')

        # 加载表头定义
        self.header_data = self.excel_handler.load_header()

        # 创建预处理目录
        os.makedirs(self.preprocessed_path, exist_ok=True)

    def process_images(self):
        """处理所有图片"""
        # 获取所有图片文件
        image_files = self._get_image_files()
        logger.info(f'找到 {len(image_files)} 张待处理图片')

        # 预处理图片
        process_limit = int(self.config.get('DEFAULT', 'process_batch_size', fallback='15'))
        preprocessed_files = []
        for img_path in image_files[:process_limit]:
            preprocessed_img = self._preprocess_image(img_path)
            if preprocessed_img is not None:
                preprocessed_files.append(preprocessed_img)

        # 批量OCR识别
        logger.info('开始OCR识别...')
        ocr_results = self.ocr_engine.batch_recognize(preprocessed_files[:process_limit])
        logger.info('OCR识别完成')

        # 提取并匹配信息
        for img_path, text_blocks in ocr_results.items():
            logger.debug(f'处理图片: {os.path.basename(img_path)}')
            extracted_data = self._extract_info(text_blocks)
            if extracted_data:
                logger.info(f'提取到数据: {extracted_data}')
                self.excel_handler.add_record(extracted_data)
                self._archive_image(img_path, extracted_data)
        logger.info('所有图片处理完成')

    def _get_image_files(self) -> List[str]:
        """获取所有待处理的图片文件"""
        if not os.path.isdir(self.input_path):
            raise ValueError(f'Input path does not exist: {self.input_path}')

        image_files = []
        for file in os.listdir(self.input_path):
            if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                image_files.append(os.path.join(self.input_path, file))

        # 按文件名排序(时间顺序)
        image_files.sort(key=lambda x: os.path.basename(x))
        return image_files

    def _extract_info(self, text_blocks: List[Dict]) -> Dict:
        """从OCR结果中提取与表头匹配的信息"""
        extracted_data = {}

        # 提取患者姓名
        name = self._find_field_value(text_blocks, ['姓名'])
        if name:
            extracted_data['姓名'] = name
            logger.debug(f'提取到姓名: {name}')

        # 提取住院号
        hospital_id = self._find_field_value(text_blocks, ['住院号', '病历号', 'ID'])
        if hospital_id:
            extracted_data['住院号'] = hospital_id
            logger.debug(f'提取到住院号: {hospital_id}')

        # 提取其他字段
        for field in self.header_data['fields']:
            if field not in extracted_data:
                value = self._find_field_value(text_blocks, [field])
                if value:
                    extracted_data[field] = value
                    logger.debug(f'提取到字段 {field}: {value}')

        return extracted_data if extracted_data else None

    def _find_field_value(self, text_blocks: List[Dict], keywords: List[str]) -> str:
        """根据关键词查找字段值"""
        for block in text_blocks:
            text = block['text']
            for keyword in keywords:
                if keyword in text:
                    # 提取冒号后的值并去除特殊字符
                    if ':' in text:
                        value = text.split(':')[-1].strip()
                        value = re.sub(r'[^\w\u4e00-\u9fa5]', '', value)  # 去除特殊字符
                        return value
                    # 或者直接返回整个文本(去除关键词和特殊字符)
                    value = text.replace(keyword, '').strip()
                    value = re.sub(r'[^\w\u4e00-\u9fa5]', '', value)
                    return value
        return None

    def _preprocess_image(self, img_path: str) -> Optional[str]:
        """简化的图片预处理流程，仅进行对比度增强和摩尔纹处理"""
        try:
            # 读取图片
            img = cv2.imread(img_path)

            # return img

            if img is None:
                logger.error(f'无法读取图片: {img_path}')
                return None

            # 对比度增强
            lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
            img = cv2.merge((l, a, b))
            img = cv2.cvtColor(img, cv2.COLOR_LAB2BGR)

            # 摩尔纹处理
            # img = cv2.fastNlMeansDenoisingColored(img, None, 12, 12, 7, 21)
            # img = self._remove_moire(img)
            # 保存预处理后的图片
            preprocessed_path = os.path.join(self.preprocessed_path, os.path.basename(img_path))
            cv2.imwrite(preprocessed_path, img)
            logger.info(f'图片预处理完成: {os.path.basename(img_path)}')
            return preprocessed_path

        except Exception as e:
            logger.error(f'图片预处理失败: {img_path}, 错误: {str(e)}')
            return None

    def _remove_moire(self,image):
        # 调整像素密度
        image = cv2.resize(image, (0, 0), fx=2, fy=2, interpolation=cv2.INTER_CUBIC)

        # 采样和插值
        height, width = image.shape[:2]  # 只获取高度和宽度
        image = cv2.pyrDown(image, dstsize=(width // 2, height // 2))
        image = cv2.pyrUp(image, dstsize=(width, height))

        # 模糊处理
        image = cv2.GaussianBlur(image, (5, 5), 0)

        # 反锯齿技术
        image = cv2.bilateralFilter(image, 9, 75, 75)

        return image
    def _archive_image(self, img_path: str, data: Dict):
        """归档图片"""
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path)

        # 根据患者姓名创建子目录
        patient_name = data.get('姓名', '未知')
        patient_dir = os.path.join(self.output_path, patient_name[:6])
        if not os.path.exists(patient_dir):
            os.makedirs(patient_dir)

        # 新文件名格式: 姓名_住院号_原文件名
        hospital_id = data.get('住院号', '')
        original_name = os.path.basename(img_path)
        new_name = f"{patient_name}_{hospital_id}_{original_name}" if hospital_id else f"{patient_name}_{original_name}"

        # 复制图片到归档目录
        new_path = os.path.join(patient_dir, new_name)
        os.rename(img_path, new_path)