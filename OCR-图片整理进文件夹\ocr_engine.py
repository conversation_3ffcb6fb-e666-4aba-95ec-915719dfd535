from paddleocr import PaddleOCR
from configparser import ConfigParser
import cv2
import numpy as np


class OCREngine:
    def __init__(self, config: ConfigParser):
        self.config = config
        self.engine_type = config.get('OCR', 'engine')
        self.language = config.get('OCR', 'language')
        self.confidence_threshold = config.getfloat('OCR', 'confidence_threshold')
        
        # 初始化OCR引擎
        if self.engine_type == 'paddleocr':
            self.ocr = PaddleOCR(
                use_angle_cls=True,
                lang=self.language,
                det_model_dir='./models/det',  # 指定检测模型路径
                rec_model_dir='./models/rec',  # 指定识别模型路径
                cls_model_dir='./models/cls',  # 指定分类模型路径
                use_gpu=True,  # 启用GPU加速
                det_db_thresh=0.3,  # 降低检测阈值
                det_db_box_thresh=0.5,  # 降低文本框阈值
                det_db_unclip_ratio=1.6,  # 增加文本框扩展比例
                rec_batch_num=6,  # 增加识别批处理大小
                use_space_char=True  # 允许识别空格
            )
        else:
            raise ValueError(f'Unsupported OCR engine: {self.engine_type}')
    
    def recognize_text(self, image_path: str):
        """识别图片中的文字"""
        # 读取图片
        # img = cv2.imread(image_path)
        print(f'正在处理图片: {image_path}')
        img = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
        if img is None:
            raise ValueError(f'Cannot read image: {image_path}')
            
        # 执行OCR
        result = self.ocr.ocr(img, cls=True)
        
        # 解析结果
        text_blocks = []
        for line in result[0]:
            text, confidence = line[1]
            if confidence >= self.confidence_threshold:
                text_blocks.append({
                    'text': text,
                    'confidence': confidence,
                    'position': line[0]
                })
                print(f'识别到文本: {text}, 置信度: {confidence}')
                if '姓名' in text:
                    print(f'找到姓名信息: {text}')
        
        print(f'识别完成，共找到{len(text_blocks)}个文本块')
        return text_blocks
    
    def batch_recognize(self, image_paths: list):
        """批量识别多张图片的文字"""
        import os
        import shutil
        
        # 确保等待复核文件夹存在
        review_dir = os.path.join(os.path.dirname(image_paths[0]), '等待复核')
        os.makedirs(review_dir, exist_ok=True)
        
        results = {}
        for img_path in image_paths:
            try:
                results[img_path] = self.recognize_text(img_path)
            except Exception as e:
                print(f'处理图片 {img_path} 时出错: {str(e)}')
                # 移动失败图片到等待复核文件夹
                try:
                    dst_path = os.path.join(review_dir, os.path.basename(img_path))
                    shutil.move(img_path, dst_path)
                    print(f'已将失败图片移动到: {dst_path}')
                except Exception as move_error:
                    print(f'移动失败图片时出错: {str(move_error)}')
                continue
        return results