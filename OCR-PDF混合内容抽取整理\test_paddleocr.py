#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_paddleocr():
    """测试PaddleOCR初始化"""
    
    try:
        logger.info('开始测试PaddleOCR...')
        
        logger.info('导入PaddleOCR...')
        from paddleocr import PaddleOCR
        logger.info('PaddleOCR导入成功')
        
        logger.info('初始化PaddleOCR...')
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            use_gpu=False,
            show_log=True  # 显示详细日志
        )
        logger.info('PaddleOCR初始化成功')
        
        # 测试OCR功能
        logger.info('测试OCR功能...')
        test_result = ocr.ocr('test_image.png', cls=True)
        logger.info(f'OCR测试结果: {test_result}')
        
    except Exception as e:
        logger.error(f'测试PaddleOCR失败: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_paddleocr()
